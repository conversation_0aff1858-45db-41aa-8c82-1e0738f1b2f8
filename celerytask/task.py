#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
@Title:     task.py
@Date:      2022/11/3 11:40
@Author:    wanglh
@Desc:     具体的异步任务执行函数(最底层的main执行函数)
"""

import json
import time
from builtins import Exception, open, print

from celery.result import AsyncResult
from flask import current_app, g

from celeryMain import my_celery
from utils import JkJob


# 异步任务结果获取
def cel_results():
    results = []
    time.sleep(5)
    for item in g.ids:
        for id, jobname in item.items():
            while True:
                result = AsyncResult(id, app=my_celery)
                # 判断task执行状态
                if result.status == "SUCCESS" or result.status == "FAILURE":
                    # 获取打包任务结果
                    results.append({jobname: result.get()})
                    print(f"{jobname} 打包任务的结果：{result.get()}，celery任务状态：{result.status}")
                    current_app.logger.info(f"{jobname} 打包任务的结果：{result.get()}，celery任务状态：{result.status}")
                    break
                time.sleep(10)
    with open("tmp.log", 'w+', encoding="utf-8") as f:
        f.write(json.dumps(results, ensure_ascii=False))
        f.write("\n")
    return json.dumps(results, ensure_ascii=False)


@my_celery.task(bind=True)
# 具体的异步任务
def jk_package(self, **kwargs):
    try:
        jkjob = kwargs.get("jkjob")
        creator = kwargs.get("creator")
        params = kwargs.get("params")
        if jkjob and creator and params:
            jobs = JkJob(jkjob, creator, params)
            jobstatus = jobs.start_main()
        print(f"\ncelery_id: {self.request.id.__str__()}\n项目: {jkjob}\n打包结果: {jobstatus}\n")
        return jobstatus
    except Exception as e:
        print("jk_package:", e)
        return f"celery 调用失败,{jkjob}打包失败"


@my_celery.task(bind=True)
def path_deployment(self, *args, **kwargs):
    pass
