[approval-dev]
approval_code = 3C2BC860-F172-44F0-9F7E-06F0DE8D948F

[approval-prod]
approval_code = 283D41A3-045E-4254-B0E0-C035DC491D98

[ops-reboot-dev]
; app_id = cli_a4ef7de23778d00c
; app_secret = MTjz6IvCPDklGOBx21FmigO4Bo2uBChd
app_id = cli_a4ef0323f3b8500d
app_secret = n0x31hCVajW2GzIqGC2TBhZiweNUZw0V

[ops-reboot-prod]
app_id = cli_a4ef0323f3b8500d
app_secret = n0x31hCVajW2GzIqGC2TBhZiweNUZw0V

[grafana]
grafana_url = http://grafana95.test.devops.szwego.com/api/dashboards/db/
;grafana domain域名
grafana_domain = grafana95.test.devops.szwego.com
;控制台生成的api_key
grafana_api_key = glsa_uLiufomYnececTgzjnmJmX2yJ8UZ1bEX_7547b078
;Dashboard 唯一标识
dashboard_slug = a4952844-0166-422a-b306-ba3d87e876af

[database]
HOSTNAME = ***********
PORT = 3306
DATABASE = wego_ops
USERNAME = loonflow
PASSWORD = Ccwo17k92Zw5
DB_URI = mysql+pymysql://%(USERNAME)s:%(PASSWORD)s@%(HOSTNAME)s:%(PORT)s/%(DATABASE)s?charset=utf8

[const]
time_zone = Asia/Shanghai

[devops-group]
group = 董鑫,吴宣,李家娜,王林浩

[message-id-prod]
;版本发布卡片ID和发送类型
release = ctp_AAglrWqLJxeq
release_id_type = chat_id

;告警卡片 ID 和发送类型
thealarm = ctp_AAgDw46MEBUp
thealarm_id_type = chat_id

;费用消息卡片和发送类型
costInformation = ctp_AAgD8ISfdyts
costInformation_id_type = open_id

;告警中心 ID 和发送类型
alarmCenter = ctp_AAVCtcqU7b6p
alarmCenter_id_type = chat_id
alarmVersion = 0.0.6

;通用消息卡片ID和版本号
commonMsgCardId = AAqIsnxlnwryz
commonMsgCardVersion= 1.0.0

[message-id-dev]
;版本发布卡片ID和发送类型
release = ctp_AAglxIsK7RU2
release_id_type = chat_id

;告警中心卡片 ID 和发送类型
alarmCenter = ctp_AAV7h1hfy2se
alarmCenter_id_type = chat_id

;通用消息卡片ID和版本号
commonMsgCardId = AAqIsnxlnwryz
commonMsgCardVersion= 1.0.0

[TEST-OPEN-ID-dev]
name = wanglinhao
receive_id_type = open_id
receive_id = ou_7b11c67f49fe3c74af7157c1c18cadff

[PRIVATE-KEY-dev]
; PRIVATE_KEY = /Users/<USER>/PC备份/xc_root_key
PRIVATE_KEY = ./ssh/xc_root_key

[PRIVATE-KEY-prod]
PRIVATE_KEY = /opt/ssh/xc_root_key

[PayMysql-dev]
host = mysql.in.wegoab.com
port = 3306
user = root
password = truedian#123
database = wg_tongs_pay

[PayMysql-prod]
host = **********
port = 3306
user = ops_repair
password = DD86^q19nteL
database = payment

[OrderMysql-dev]
host = mysql.in.wegoab.com
port = 3306
user = root
password = truedian#123
database = wg_orders

[OrderMysql-prod]
host = ***********
port = 3306
user = ops_repair
password = DD86^q19nteL
database = wg_tongs

[ruisu]
Authorization = eyJhbGciOiJIUzUxMiJ9.************************************************************************************************************************************.eoYQhqNeUpyuJRCux-m1FeE4LntAYODYdVeFasJ9R0ogzexRuC5cHsUOyAp-r6cqEa2mLOCYprdga6ZQxDk_SQ

[redis-loonflow]
host = **********
port = 6379
password = Ccwo17k92Zw5

[security_sg]
InstanceId = sg-8hjl9o2p
