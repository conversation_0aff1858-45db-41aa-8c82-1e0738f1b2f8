#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
演示 today_status 字段修复效果
"""

import json
from datetime import datetime, timedelta

def demonstrate_fix():
    """演示修复前后的逻辑差异"""
    
    print("=" * 60)
    print("HealthPanel today_status 字段修复演示")
    print("=" * 60)
    
    # 当前日期
    today = datetime.now().strftime("%Y-%m-%d")
    today_start = f"{today} 00:00:00"
    today_end = f"{today} 23:59:59"
    
    print(f"当前日期: {today}")
    print(f"今日时间范围: {today_start} ~ {today_end}")
    print()
    
    # 测试场景
    scenarios = [
        {
            "name": "场景1：查询包含今天的时间范围",
            "description": "前端查询最近7天数据（包含今天）",
            "params": {
                "date": today,
                "start_time": (datetime.now() - timedelta(days=6)).strftime("%Y-%m-%d 00:00:00"),
                "end_time": f"{today} 23:59:59"
            }
        },
        {
            "name": "场景2：查询不包含今天的时间范围", 
            "description": "前端查询历史7天数据（不包含今天）",
            "params": {
                "date": (datetime.now() - timedelta(days=7)).strftime("%Y-%m-%d"),
                "start_time": (datetime.now() - timedelta(days=13)).strftime("%Y-%m-%d 00:00:00"),
                "end_time": (datetime.now() - timedelta(days=7)).strftime("%Y-%m-%d 23:59:59")
            }
        },
        {
            "name": "场景3：查询未来日期",
            "description": "前端查询未来7天数据（测试边界情况）",
            "params": {
                "date": (datetime.now() + timedelta(days=7)).strftime("%Y-%m-%d"),
                "start_time": (datetime.now() + timedelta(days=1)).strftime("%Y-%m-%d 00:00:00"),
                "end_time": (datetime.now() + timedelta(days=7)).strftime("%Y-%m-%d 23:59:59")
            }
        }
    ]
    
    print("测试场景:")
    print("-" * 40)
    
    for i, scenario in enumerate(scenarios, 1):
        print(f"{i}. {scenario['name']}")
        print(f"   描述: {scenario['description']}")
        print(f"   参数: {json.dumps(scenario['params'], ensure_ascii=False, indent=8)}")
        print()
    
    print("修复前后对比:")
    print("-" * 40)
    
    # 修复前的逻辑（错误）
    print("❌ 修复前逻辑（错误）:")
    print("   def fetch_alerts_optimized(start_time, end_time, today_start, today_end):")
    print("       # 使用前端时间范围查询所有数据")
    print("       all_alerts = query.filter(")
    print("           starts_at >= start_time,  # 依赖前端参数！")
    print("           starts_at <= end_time     # 依赖前端参数！")
    print("       ).all()")
    print("       ")
    print("       # 在有限数据集中分离今日数据")
    print("       for alert in all_alerts:")
    print("           if today_start <= alert.starts_at <= today_end:")
    print("               today_alerts.append(alert)  # 可能为空！")
    print()
    
    # 修复后的逻辑（正确）
    print("✅ 修复后逻辑（正确）:")
    print("   def fetch_history_alerts(start_time, end_time, today_start):")
    print("       # 查询历史数据，排除今日")
    print("       return query.filter(")
    print("           starts_at >= start_time,")
    print("           starts_at <= end_time,")
    print("           starts_at < today_start   # 排除今日数据")
    print("       ).all()")
    print("   ")
    print("   def fetch_today_alerts(today_start, today_end):")
    print("       # 独立查询今日数据，不受前端参数影响")
    print("       return query.filter(")
    print("           starts_at >= today_start,  # 固定今日开始")
    print("           starts_at <= today_end     # 固定今日结束")
    print("       ).all()")
    print()
    
    print("预期结果对比:")
    print("-" * 40)
    
    results_table = [
        ["场景", "修复前 today_status", "修复后 today_status", "修复前 items", "修复后 items"],
        ["包含今天的查询", "✅ 有数据", "✅ 有数据", "❌ 包含历史+今日", "✅ 只包含历史"],
        ["不包含今天的查询", "❌ 空数据", "✅ 有数据", "✅ 只包含历史", "✅ 只包含历史"],
        ["未来日期查询", "❌ 空数据", "✅ 有数据", "❌ 空数据", "✅ 空数据"]
    ]
    
    # 打印表格
    col_widths = [max(len(str(row[i])) for row in results_table) + 2 for i in range(len(results_table[0]))]
    
    for i, row in enumerate(results_table):
        if i == 0:
            # 表头
            print("| " + " | ".join(f"{cell:<{col_widths[j]-2}}" for j, cell in enumerate(row)) + " |")
            print("|" + "|".join("-" * col_widths[j] for j in range(len(row))) + "|")
        else:
            print("| " + " | ".join(f"{cell:<{col_widths[j]-2}}" for j, cell in enumerate(row)) + " |")
    
    print()
    
    print("关键改进:")
    print("-" * 40)
    improvements = [
        "✅ 数据一致性：today_status 在所有场景下都返回相同数据",
        "✅ 逻辑正确性：完全符合 v4.0.0 设计要求",
        "✅ 查询独立性：实时数据查询不再依赖前端参数",
        "✅ 性能优化：减少不必要的数据处理",
        "✅ 向后兼容：API 接口和响应格式保持不变"
    ]
    
    for improvement in improvements:
        print(f"  {improvement}")
    
    print()
    
    print("验证方法:")
    print("-" * 40)
    print("1. 运行端到端测试:")
    print("   python test_today_status_fix.py")
    print()
    print("2. 运行单元测试:")
    print("   python test_alert_functions.py")
    print()
    print("3. 手动验证:")
    print("   - 使用不同的时间范围参数调用 API")
    print("   - 比较返回的 today_status 数据")
    print("   - 确认数据完全一致")
    print()
    
    print("=" * 60)
    print("修复完成！today_status 字段现在完全符合设计要求")
    print("=" * 60)


def show_api_examples():
    """显示 API 调用示例"""
    
    print("\nAPI 调用示例:")
    print("-" * 40)
    
    base_url = "http://localhost:5000/alertscenter/api/v1/panel"
    
    examples = [
        {
            "description": "查询包含今天的数据",
            "url": f"{base_url}?date=2025-07-31&start_time=2025-07-25%2000:00:00&end_time=2025-07-31%2023:59:59"
        },
        {
            "description": "查询不包含今天的历史数据", 
            "url": f"{base_url}?date=2025-07-24&start_time=2025-07-18%2000:00:00&end_time=2025-07-24%2023:59:59"
        }
    ]
    
    for i, example in enumerate(examples, 1):
        print(f"{i}. {example['description']}")
        print(f"   GET {example['url']}")
        print()
    
    print("期望结果:")
    print("- 两个请求返回的 today_status 字段应该完全相同")
    print("- items 字段应该包含不同的历史数据")
    print("- dateRange 字段应该反映不同的日期范围")


if __name__ == "__main__":
    demonstrate_fix()
    show_api_examples()
