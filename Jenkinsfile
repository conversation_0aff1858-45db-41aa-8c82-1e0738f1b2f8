// 自动触发jenkins进行打包
pipeline {
    agent any
    stages {
        stage("查看当前工作目录") {
            steps {
                echo "${WORKSPACE}"
                echo "${BUILD_NUMBER}"
            }
        }
        stage('构建镜像') {
            steps {
                sh '''#!/bin/bash
                echo "开始构建镜像"
                docker build -t ccr.ccs.tencentyun.com/wego-ops/api-manager:devops-cicd.${BUILD_NUMBER} .
                docker push ccr.ccs.tencentyun.com/wego-ops/api-manager:devops-cicd.${BUILD_NUMBER} '''
            }
        }

        stage("触发镜像") {
            steps {
                sh '''
                echo  "调用蓝鲸脚本，更新pod镜像"
                python3 ./restart_docker.py api-ops ccr.ccs.tencentyun.com/wego-ops/api-manager:devops-cicd.${BUILD_NUMBER}
                '''
            }
        }
    }
}