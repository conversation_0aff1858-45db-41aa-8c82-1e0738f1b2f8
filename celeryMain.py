#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
@Title:     celeryMain.py
@Date:      2022/8/8 19:43
@Author:    wanglh
@Desc：     
"""
import os

from celery import Celery
from dotenv import load_dotenv

from config import config

env_path = os.path.join(os.path.dirname(__file__), ".env")
if os.path.exists(env_path):
    load_dotenv(env_path)


def make_celery(app_name):
    broker = getattr(config[os.getenv('FLASK_ENV') or 'default'], "CELERY_BROKER_URL")
    backend = getattr(config[os.getenv('FLASK_ENV') or 'default'], "CELERY_BACKEND_URL")

    celery = Celery(
        app_name,
        broker=broker,
        backend=backend
    )
    return celery


my_celery = make_celery(__name__)
