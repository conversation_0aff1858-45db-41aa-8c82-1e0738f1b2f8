#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
@Title:   config.py
@Date:    2022/4/12 13:51
@Update:  2023/05/05 10:27
@Author:  wanglh
@Desc:    配置文件
"""

import os

from celery.schedules import crontab


class Config:
    @classmethod
    def init_app(cls, app):
        pass

    # CELERY 参数没有启用
    CELERY_BROKER_URL = "redis://127.0.0.1:6379/0"
    CELERY_BACKEND_URL = "redis://127.0.0.1:6379/1"
    CELERYBEAT_SCHEDULE = {
        "log-every-1-minutes": {
            "task": "app.tasks.log",
            "schedule": crontab(minute="*/1"),
            "args": ("log every 1 minutes",),
        }
    }


class Development(Config):
    HOSTNAME = "***********"
    PORT = "3306"
    DATABASE = "wego_ops_bak"
    USERNAME = "loonflow"
    PASSWORD = "Ccwo17k92Zw5"

    DB_URI = "mysql+pymysql://{}:{}@{}:{}/{}?charset=utf8".format(
        USERNAME, PASSWORD, HOSTNAME, PORT, DATABASE
    )
    SQLALCHEMY_DATABASE_URI = DB_URI
    SQLALCHEMY_TRACK_MODIFICATIONS = True
    SQLALCHEMY_POOL_RECYCLE = 3600
    SQLALCHEMY_ENGINE_OPTIONS = {
        "pool_pre_ping": True,
        "max_overflow": 30,  # 增加最大溢出连接数
        "pool_timeout": 60,  # 增加连接超时时间
        "pool_size": 20,  # 增加基础连接池大小
        "pool_recycle": 3600,  # 定期回收连接
    }
    TIMEZONE = "Asia/Shanghai"
    SQLALCHEMY_ECHO = True
    POOL_PRE_PING = True
    DEBUG = True
    JKMASTER = "http://*********:8088/jenkins"
    JKUSER = "devops"
    JKPASSWORD = "WEgo%402019%28%29*"
    LEVEL = "DEBUG"
    SECRETID = "AKIDtWFh04sSothLjro8sLXswl22uoSptaZK"
    SECRETKEY = "dGdka9SlrhTzFRMEpVnqdAlqxoqcm12I"
    LOCAL_HOST = "127.0.0.1"
    LOCAL_PORT = 4001

    UPLOAD_PATH = os.getcwd() + os.sep + "upload_path"
    UPLOAD_EXTENSIONS = [".jpg", ".png", ".gif", ".json", ".text", ".py"]
    TX_OLD_SECRETID = "AKID51k1A6nLejZAg291INFtNwLXWKvJUWLx"
    TX_OLD_SECRETKEY = "SlwufEbooeLrDOPHzWs20oX6TeeS7Zop"
    TX_OLD_BUCKET = "xc-static-test-1251632793"
    BUCKET_REGION = "ap-guangzhou"
    COS_BUCKET_PATH = "/etc/"

    NACOS_V3_HOST = "mysql.in.wegoab.com"
    NACOS_V3_DATABASE = "nacos_config"
    NACOS_V3_USER = "nacos"
    NACOS_V3_PWD = "nacos@2nacos"
    AES_KEY = "wegooooo"


class Production(Config):
    HOSTNAME = "***********"
    PORT = "3306"
    DATABASE = "wego_ops"
    USERNAME = "loonflow"
    PASSWORD = "Ccwo17k92Zw5"
    DB_URI = "mysql+pymysql://{}:{}@{}:{}/{}?charset=utf8".format(
        USERNAME, PASSWORD, HOSTNAME, PORT, DATABASE
    )
    SQLALCHEMY_DATABASE_URI = DB_URI
    SQLALCHEMY_TRACK_MODIFICATIONS = True
    SQLALCHEMY_POOL_RECYCLE = 3600
    SQLALCHEMY_ENGINE_OPTIONS = {
        "pool_pre_ping": True,
        "max_overflow": 30,  # 增加最大溢出连接数
        "pool_timeout": 60,  # 增加连接超时时间
        "pool_size": 20,  # 增加基础连接池大小
        "pool_recycle": 3600,  # 定期回收连接
    }
    TIMEZONE = "Asia/Shanghai"
    POOL_PRE_PING = True
    DEBUG = False
    LEVEL = "INFO"
    JKMASTER = "http://*********:8088/jenkins"
    JKUSER = "devops"
    JKPASSWORD = "WEgo%402019%28%29*"
    SECRETID = "AKIDtWFh04sSothLjro8sLXswl22uoSptaZK"
    SECRETKEY = "dGdka9SlrhTzFRMEpVnqdAlqxoqcm12I"
    LOCAL_HOST = "127.0.0.1"
    LOCAL_PORT = 4001

    UPLOAD_PATH = os.getcwd() + os.sep + "upload_path"
    UPLOAD_EXTENSIONS = [".jpg", ".png", ".gif", ".json", ".text"]
    TX_OLD_SECRETID = "AKID51k1A6nLejZAg291INFtNwLXWKvJUWLx"
    TX_OLD_SECRETKEY = "SlwufEbooeLrDOPHzWs20oX6TeeS7Zop"
    TX_OLD_BUCKET = "xc-static-1251632793"
    BUCKET_REGION = "ap-guangzhou"
    COS_BUCKET_PATH = "/etc/"

    NACOS_V3_HOST = "mysql.in.wegoab.com"
    NACOS_V3_DATABASE = "nacos_config"
    NACOS_V3_USER = "nacos"
    NACOS_V3_PWD = "nacos@2nacos"
    AES_KEY = "wegooooo"

    DEVOPS_GROUP = "董鑫, 吴宣, 李家娜, 王林浩"


config = {"default": Development, "dev": Development, "prod": Production}
