## 说明文档

仓库地址：[wego-gitlab-devops-cicd](https://gitlab.in.szwego.com/devops-admin/devops-cicd)

功能： cicd相关接口

## 描述

相关接口说明，请点击下面链接：

[cicd程序API接口文档](https://yapi.in.szwego.com/project/192/interface/api/cat_1636)

## 启动方式

### 本地启动

在本地启动，请执行当前目录下的run.sh脚本

```bash
./run.sh help # 可通过下面的命令查看启动帮助
```

请确保在启动前脚本有执行权限

程序启动失败可能存在的原因是缺少相关的依赖包

请查看当前项目是否有`requirements.txt`文件

**安装相关依赖**

```bash
# 当前项目需要的依赖
pipreqs.exe . --encoding=utf8 --force

# 安装依赖
pip3 install -r requirements.txt -i https://pypi.douban.com/simple
```

> 由于开发环境与线上环境存在一定差异，如包的版本等  
> 所以安装过程中出现报错，请根据当前实际环境自行处理

### 容器启动

```bash
docker run --itd --name <container-name> <image_name/image_id>
```

> 具体的启动信息，查看文件  
> **Dockerfile**: Dockerfile定义的变量默认为 **dev**  
> 需要修改为生成，需要将FLASK_ENV设置为：**prod**
> **docker_run.sh**

> 修改FLASK_ENV可通过下面的两种方法：
>
> 1. 修改Dockerfile文件中的ENV，然后重新构建镜像打包
> 2. 在容器服务中，更新Pod配置，添加环境变量并设置为：**FLASK_ENV=prod**

## 目录结构

```text
├── devops-cicd
    ├── blueprint                           # 视图目录
    ├── config.py                           # 程序配置文件
    ├── main.py                             # Flask主文件
    ├── extension.py                        # 扩展程序
    ├── model.py                            # 数据库模块文件
    ├── quote                               # 后端模块文件
    ├── static                              # 静态文件
    ├── templates                           # 模板文件
    ├── Dockerfile                          # 镜像构建
    ├── Jenkinsfile                         # 流水线（pipeline）
    ├── run.sh                              # 本地启动脚本
    ├── docker_run.sh                       # 镜像启动脚本
    ├── requirements.txt                    # 环境依赖包
    ├── flask.log                           # 程序标准控制台输出信息
    ├── logs/flask.log                      # 访问日志持久存储 10天  
    └── README.md                           # 项目说明
```

## 说明

1. 已完成CICD流程化验证，当修改代码，push后会触发**Jenkins打包**和更新**Deployment**

## 注意事项

1. 配置文件对比和配置的修改上线（ci_sandbox_config_template），参数是否设置为一样

```text
配置文件对比的参数与现在做的ci_sandbox_config_template 要设置成一样的吗？
配置文件对比的参数传递：{sbx_uuid: "ta5fecoy", type: "apollo", filename: "application.yml", service: "lookbook"}
sbx_uuid：集群，对应apollo中的clustername，对应nacos的tenant_id
filename：文件名，对应apollo中的namespace，对应nacos的data_id
service：服务，对应Apollo中的appid对应，对应group_id
type：配置类型：分为Apollo和nacos
```

## 相关图

### dnspodrecord.py

- 时序图

```mermaid
sequenceDiagram
  actor User
  participant DnspodRecord
  User ->>+ DnspodRecord: 创建实例
  User ->>+ DnspodRecord: record_list()
  User ->>+ DnspodRecord: record_add()
  User ->>+ DnspodRecord: record_modify()
  User ->>+ DnspodRecord: record_delete()
```

- 类图

```mermaid
classDiagram
  class DnspodRecord {
    -record_params: List[str]
    -client: DnspodClient
    +__init__(secretId: str, secretKey: str)
    +record_list(**kwargs: Any)
    +record_add(**kwargs: Any)
    +record_modify(**kwargs: Any)
    +record_delete(**kwargs: Any)
  }

  class Credential {
    +__init__(secretId: str, secretKey: str)
  }

  class HttpProfile {
    -endpoint: str
  }

  class ClientProfile {
    -httpProfile: HttpProfile
  }

  class DescribeRecordListRequest {
    +from_json_string(json_string: str)
  }

  class CreateRecordRequest {
    +from_json_string(json_string: str)
  }

  class ModifyRecordRequest {
    +from_json_string(json_string: str)
  }

  class DeleteRecordRequest {
    +from_json_string(json_string: str)
  }

  DnspodRecord --> Credential
  DnspodRecord --> HttpProfile
  DnspodRecord --> ClientProfile
  DnspodRecord --> DescribeRecordListRequest
  DnspodRecord --> CreateRecordRequest
  DnspodRecord --> ModifyRecordRequest
  DnspodRecord --> DeleteRecordRequest

```

- 状态图

```mermaid
stateDiagram-v2
  [*] --> DnspodRecord: 初始化
  DnspodRecord --> [*]: 错误
  DnspodRecord --> record_list: 就绪
  record_list --> record_add: 就绪
  record_add --> record_modify: 就绪
  record_modify --> record_delete: 就绪
  record_delete --> record_list: 就绪
```

- 用例图

```mermaid
classDiagram
  class User {
    +使用DnspodRecord()
    +配置密钥()
    +管理记录()
  }
  class 使用DnspodRecord {
    +记录列表()
    +添加记录()
    +修改记录()
    +删除记录()
  }
  User --|> 使用DnspodRecord: 使用

```

- [时序图](./systemctl.puml)

- 流程图（GraphTD）

```mermaid
graph TD
  User(用户)
  User --> 使用DnspodRecord[使用DnspodRecord]
  User --> 配置密钥[配置密钥]
  User --> 管理记录[管理记录]
  使用DnspodRecord --> 记录列表[记录列表]
  使用DnspodRecord --> 添加记录[添加记录]
  使用DnspodRecord --> 修改记录[修改记录]
  使用DnspodRecord --> 删除记录[删除记录]
```

- 序列图（sequenceDiagram）

```mermaid
sequenceDiagram
  actor User as 用户
  participant 使用DnspodRecord as 使用DnspodRecord
  participant 记录列表 as 记录列表
  participant 添加记录 as 添加记录
  participant 修改记录 as 修改记录
  participant 删除记录 as 删除记录
  User ->>+ 使用DnspodRecord: 使用DnspodRecord
  使用DnspodRecord ->>+ 记录列表: 查看记录列表
  记录列表 -->>- 使用DnspodRecord: 完成查看
  使用DnspodRecord ->>+ 添加记录: 添加记录
  添加记录 -->>- 使用DnspodRecord: 完成添加
  使用DnspodRecord ->>+ 修改记录: 修改记录
  修改记录 -->>- 使用DnspodRecord: 完成修改
  使用DnspodRecord ->>+ 删除记录: 删除记录
  删除记录 -->>- 使用DnspodRecord: 完成删除
```

## 版本记录

- 2025-06-27
  - 新增飞书应用发送消息到指定联系人接口

- 2025-06-20
  - 新增 IP 黑名单 接口

- 2025-05-29

  - 新增健康看板V2接口，提供更详细的告警统计和对象详情数据，支持默认15分钟时间范围查询。同时添加健康看板V3接口返回结构化表格格式数据。

  - 新增告警指纹生成规则文档，说明不同告警类型的指纹生成方法和共同特点。优化告警处理器代码格式，统一字符串引号风格。

  - 主要变更包括：
      1. 新增/healthboardV2接口，支持告警对象详情展示
      2. 新增/healthboardV3接口，返回表格结构化数据
      3. 添加fingerprintMd.md文档说明告警指纹规则
      4. 优化alerts_processor.py代码格式

- 2025-05-14
  - 添加腾讯产品列表模型并优化告警处理逻辑
  - 删除了冗余的tencent.py文件
  - 修改.gitignore文件
  - 告警中心：告警事件记录落盘，腾讯云告警，自建告警
  - 开放订阅接口优化

- 2025-03-12
  - 重构: Pinpoint，RocketMQ 配置更新，上传等接口
  - 新增：飞书通用告警消息接口 V2 版本，V1 版本继续保留
  - 新增：飞书群组 ID 获取接口
  - 优化：alarm 中的其他代码，清楚无用代码

- 2025-03-05
  - 优化【预发布环境】相关接口

- 2025-01-07
  - 优化：presm.EnvGrayRuleAPI.put逻辑
  - 新增：预发布环境规则批量更新，优化

- 2024-12-03
  - 添加【预发布环境】用户规则更新，删除接口
  - 优化 waf 白名单列表获取

- 2024-11-25
  - 修复：crypt 日志上传解密时间过长，大文本文件失败等问题

- 2024-10-31
  - 添加 xlog kibanaUrl 连接
  - 添加 xlog 各字段筛选

- 2024-10-29
  - 添加 xlog 日志详情页功能

- 2024-10-22
  - 添加 xlog 日志分析处理
  - 优化 xlog-list 返回 owner 字段
  - 新增 xlog 下载接口

- 2024-09-20
  - 优化 traffic 接口返回参数
  - 用户管理页面：打开离职人员信息

- 2024-09-10
  - 修改 DeleteService函数
  - 添加 traffic 流量数据
  - 添加 Jenkins build 信息数据接口

- 2024-09-03
  - 修改：预发布沙盒环境打包传递的 name 变更为 appname

- 2024-08-29
  - 新增预发布管理后端接口：环境，服务，日志，规则
    - 文件位置：`presm.py`

- 2024-08-29
  - 优化 kafka event notidy 相关查询

- 2024-08-15
  - 优化 KafkaAlertFire.get 查询

- 2024-07-05
  - 优化：工单-重启album 回调接口

- 2024-07-04
  - 优化：业务数据监控接口优化

- 2024-07-03
  - 新增：waf 疑似 IP 添加接口

- 2024-07-02
  - 新增：sql 慢查询统计接口
    - 慢查询统计，分 sqltemplate 和 所有

- 2024-06-27
  - 新增：业务数据接口
    - ids 获取，比较数据获取，30%对比获取

- 2024-06-24
  - 新增：sql 慢日志查询接口

- 2024-06-13
  - 修改：SLA 字典

- 2024-05-31
  - 新增：安全中心：redis 查询接口

- 2024-05-24
  - 新增：安全中心接口

- 2024-05-10
  - 新增：投诉信息处理数据中心时间参数判断

- 2024-05-07
  - 新增：/download/complaint/info接口 （投诉信息查询）

- 2024-04-08
  - 新增 err_alert_rules 接口，get，post
  - 新增 err_alert_firing 接口，get，post
  - 新增 err_ignore_rules 接口，get，post

- 2024-03-06
  - 新增 yml 规则文件上传 [yml规则文件上传](./utils/monitor/yaml_push.py)
  - 添加 pinpoint 配置后端接口信息

- 2024-03-08
  - 新增 kafkaerralertfiring接口
  - 修改 README.md 文件，增加版本记录

- 2024-03-15
  - 新增/kafka/ignore/rules接口

- 2024-03-18
  - 修改 model 文件，自动映射

#### other

vscode启动：

```bash
flask run -p 4001 -h 0.0.0.0 --debugger --reload
```
