#!/bin/bash

# @Title:   cicd-flask.sh
# <AUTHOR>
# @Date:    2021-06-23 15:41:00
# @Desc:    重启main.py程序


start() {
  echo "" >flask.log
  find $(pwd) -name "*.pyc" -delete
  export FLASK_APP="main:create_app()"
  [ $1 == "" ] && export FLASK_ENV=default || export FLASK_ENV=$1

  if [[ $? -ne 0 ]] && [[ $1 == "prod" ]]; then
    netstat -tnulp | grep 4001
    export FLASK_DEBUG=0
    export LOG_LEVEL=INFO
    echo "服务启动中......."
    # nohup uvicorn main:asgi_app --host 0.0.0.0 --port 4001 --reload >>flask.log 2>&1 &
    nohup python3.9 -u -m flask run -p 4001 -h 0.0.0.0 >>flask.log 2>&1 &
  fi

  if [[ $1 == "dev" ]]; then
    export FLASK_DEBUG=1
    export LOG_LEVEL=INFO
    echo "测试服务启动中..."
    # ASGI服务器使用
    # nohup uvicorn main:asgi_app --host 0.0.0.0 --port 5001 --reload >>flask.log 2>&1 &

     nohup python3.9 -u -m flask run -p 5001 -h 0.0.0.0 >>flask.log 2>&1 &
  fi
}

restart() {
  echo "" >flask.log
  find $(pwd) -name "*.pyc" -delete

  export FLASK_APP="main:create_app()"
  [[ $1 == "" ]] && export FLASK_ENV=default || export FLASK_ENV=$1
  if [[ $1 == "dev" ]]; then
    ps -ef | grep 5001 | grep -v grep | grep flask | awk '{print $2}' | xargs kill -9
    start $1
    sleep 3
    status 5001
  else
    ps -ef | grep 4001 | grep -v grep | grep flask | awk '{print $2}' | xargs kill -9
    nohup python3.9 -u -m flask run -p 4001 -h 0.0.0.0 >>flask.log 2>&1 &
    echo "重启中......"
    sleep 3
    status 4001
  fi

}

status() {
  netstat -tnulp | grep $1
  [ $? -eq 0 ] && printf "启动正常，日志查看：./flask.log\n" || printf "启动失败，日志查看：./flask.log\n"
}

stop() {
  ps -ef | grep 5001 | grep -v grep | grep flask | awk '{print $2}' | xargs kill -9
  [ $? -eq 0 ] && printf "cicd程序关闭成功\n"
  # 删除pyc文件
  find $(pwd) -name "*.pyc" | xargs rm
}


start_celery() {
  flush_redis_db
  [ ! -d /opt/logs ] && mkdir -p /opt/logs/
  [[ $1 == "" ]] && export FLASK_ENV="default" || export FLASK_ENV=$1
  echo "" >/opt/logs/celery.log
  echo "日志文件：/opt/logs/celery.log"
  exec celery multi start -A celery_main.my_celery worker --logfile=/opt/logs/celery.log --loglevel=INFO
}

case $1 in
start)
  start $2
  ;;
restart)
  restart $2
  ;;
stop)
  stop
  ;;
status)
  status
  ;;
*)
  echo "****************************************"
  echo "请输入两个参数："
  echo "参数一"
  echo "1. status:   查看状态"
  echo "2. start :   启动hook 需要第2参数"
  echo "3. restart:  重启hook 需要第2参数"
  echo "4. stop:     停止hook"
  echo "****************************************"
  echo "****************************************"
  echo "第二参数：配置环境"
  echo "1. dev:       开发环境 5001端口"
  echo "2. prod:      生产环境 4001端口"
  echo "3. 默认开发环境"
  echo "****************************************"
  ;;
esac
