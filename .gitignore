# Python 相关
__pycache__/
*.py[cod]
*$py.class
*.so
.python-version

# 虚拟环境
venv/
.env/
.venv/
env/
ENV/
env.bak/
venv.bak/

# 测试和覆盖率
.coverage
.coverage.*
.cache
nosetests.xml
coverage.xml
*.cover
*.py,cover
htmlcov/
.tox/
.nox/

# 日志和IDE
*.log
.vscode/
.idea/
*.DS_Store

# 构建和发布
build/
dist/
*.egg-info/
*.egg
*.manifest
*.spec

# 系统文件
.DS_Store
Thumbs.db

# 项目特定
blueprint/msgversion
*.json
.scannerwork
/pylint.txt
/logs
pp_rule
mq_rule
out/
/upload_path
templates/deployment
templates/services
templates/configmap
/.kube

# 测试文件
test*.py
*.back
*.bk
pylint.*
local_env_test_params.txt
blueprint/authentication.py

# 其他
softs/
*.tar.gz
python/__pycache__/
.ipynb_checkpoints/
python/api/kubertnetes-api/.kube
.Python
develop-eggs/
downloads/
eggs/
.eggs/
lib/
lib64/
parts/
sdist/
var/
wheels/
share/python-wheels/
.installed.cfg
MANIFEST
pip-log.txt
pip-delete-this-directory.txt
.hypothesis/
.pytest_cache/
cover/
*.mo
*.pot
local_settings.py
db.sqlite3
db.sqlite3-journal
instance/
.webassets-cache
.scrapy
docs/_build/
.pybuilder/
target/
profile_default/
ipython_config.py
__pypackages__/

*.sage.py
.spyderproject
.spyproject
.ropeproject
/site
.mypy_cache/
.dmypy.json
dmypy.json
.pyre/
.pytype/
cython_debug/
celerybeat-schedule
celerybeat.pid
celerybeat-*.bak
celerybeat-*.dat
celerybeat-*.dir
business*.yml
*.prom!/.vscode/