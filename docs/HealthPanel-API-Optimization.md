# HealthPanel API 接口优化文档

## 概述

基于前端 HealthPanel 组件 v4.0.0 版本的要求，对 `/alertscenter/api/v1/panel` 接口进行了重大优化，实现了数据源分离和实时状态统计的功能。

## 主要优化内容

### 1. 数据源分离（v4.0.0 核心特性）

**优化前**：
- 所有数据（历史+当前）混合在 `items` 字段中
- 状态统计包含所有时间范围的数据
- 无法区分历史数据和实时数据

**优化后**：
- **历史数据**：`items` 字段只包含历史数据（排除今天）
- **实时数据**：新增 `today_status` 字段专门存储当天实时状态
- **状态统计**：移除了混合统计，前端可根据需要使用 `today_status` 进行实时统计

### 2. API 响应结构变更

#### 新的响应格式
```json
{
  "status": "success",
  "message": "获取数据成功",
  "data": {
    "items": [
      {
        "id": "category_name",
        "product_name": "",
        "category": "计算",
        "isCategory": true,
        "date_2025_07_30": {
          "statuses": ["healthy"],
          "hasData": false,
          "timestamp": null
        }
      },
      {
        "id": "fingerprint_or_unique_id",
        "product_name": "云服务器",
        "category": "计算", 
        "isCategory": false,
        "date_2025_07_30": {
          "statuses": ["warning", "error"],
          "hasData": true,
          "timestamp": "2025-07-30 10:00:00"
        }
      }
    ],
    "today_status": [
      {
        "product_name": "云服务器",
        "category": "计算",
        "statuses": ["healthy", "warning"],
        "hasData": true,
        "timestamp": "2025-07-31 14:30:00"
      }
    ],
    "dateRange": [
      "2025-07-30",
      "2025-07-29",
      "2025-07-28",
      "2025-07-27",
      "2025-07-26",
      "2025-07-25",
      "2025-07-24"
    ]
  }
}
```

### 3. 数据查询逻辑优化

#### 时间范围分离
```python
# 获取当前日期作为实时数据基准
today = datetime.now().strftime("%Y-%m-%d")
today_start = f"{today} 00:00:00"
today_end = f"{today} 23:59:59"

# 历史数据：排除今天的数据
history_alerts = MonitoringCenterAlert.query.filter(
    MonitoringCenterAlert.starts_at >= start_time,
    MonitoringCenterAlert.starts_at <= end_time,
    MonitoringCenterAlert.starts_at < today_start
).all()

# 实时数据：只查询今天的数据
today_alerts = MonitoringCenterAlert.query.filter(
    MonitoringCenterAlert.starts_at >= today_start,
    MonitoringCenterAlert.starts_at <= today_end
).all()
```

### 4. 数据结构标准化

#### HistoryHealthItem（items字段）
- `id`: 分类名称或产品fingerprint
- `product_name`: 产品名称（分类行为空）
- `category`: 产品分类
- `isCategory`: 是否为分类行
- `date_YYYY_MM_DD`: 动态日期字段，包含该日期的健康状态

#### TodayStatusItem（today_status字段）
- `product_name`: 产品名称
- `category`: 产品分类
- `statuses`: 当前状态数组
- `hasData`: 是否有数据
- `timestamp`: 最新更新时间

### 5. 状态标准化逻辑

```python
def normalize_statuses(statuses_list):
    """标准化状态数组"""
    if not statuses_list:
        return ['healthy']
    
    statuses_set = set(statuses_list)
    
    # 如果只有healthy状态或为空，返回healthy
    if not statuses_set or statuses_set == {'healthy'}:
        return ['healthy']
    
    # 如果包含healthy和其他状态，移除healthy
    if 'healthy' in statuses_set and len(statuses_set) > 1:
        statuses_set.remove('healthy')
    
    return sorted(list(statuses_set))
```

## 兼容性说明

### 保持兼容的部分
- API 路径：`/alertscenter/api/v1/panel`
- 请求参数：`date`, `start_time`, `end_time`
- 基本响应格式：`status`, `message`, `data`
- 产品分类逻辑和映射规则

### 变更的部分
- 移除了 `statistics` 字段（前端可基于 `today_status` 自行计算）
- `items` 中的字段名从 `productName` 改为 `product_name`
- 新增 `today_status` 字段
- `items` 不再包含 `current` 字段

## 前端适配建议

### 1. "此刻"列数据源
```javascript
// 使用 today_status 字段渲染"此刻"列
const todayStatusMap = new Map();
data.today_status.forEach(item => {
  const key = `${item.category}_${item.product_name}`;
  todayStatusMap.set(key, item);
});
```

### 2. 历史列数据源
```javascript
// 使用 items 字段中的动态日期字段渲染历史列
data.items.forEach(item => {
  if (!item.isCategory) {
    // 渲染产品行的历史数据
    data.dateRange.forEach(date => {
      const dateKey = `date_${date.replace(/-/g, '_')}`;
      const dateData = item[dateKey];
      // 渲染该日期的状态
    });
  }
});
```

### 3. 状态统计
```javascript
// 基于 today_status 计算实时状态统计
const statistics = {
  healthy: 0,
  info: 0, 
  warning: 0,
  error: 0,
  critical: 0
};

data.today_status.forEach(item => {
  item.statuses.forEach(status => {
    if (statistics.hasOwnProperty(status)) {
      statistics[status]++;
    }
  });
});
```

## 测试验证

提供了测试脚本 `test_health_panel_api.py` 用于验证接口功能：

```bash
python test_health_panel_api.py
```

测试内容包括：
- API 响应状态验证
- 数据结构完整性检查
- 字段类型和格式验证
- 前端期望格式对比

## 重要修复：today_status 字段逻辑错误（2025-07-31）

### 问题发现
在代码审查中发现 `today_status` 字段存在严重的逻辑错误：
- **问题**：`today_status` 的数据会根据前端传递的时间范围参数变化
- **影响**：违背了 v4.0.0 设计要求，`today_status` 应该始终返回当天实时状态
- **根因**：`fetch_alerts_optimized()` 函数使用前端时间范围查询所有数据，然后分离今日数据

### 修复方案
将单一的 `fetch_alerts_optimized()` 函数拆分为两个独立函数：

#### 修复前（错误逻辑）：
```python
def fetch_alerts_optimized(start_time, end_time, today_start, today_end):
    # 使用前端时间范围查询所有数据
    all_alerts = MonitoringCenterAlert.query.filter(
        MonitoringCenterAlert.starts_at >= start_time,  # 问题：依赖前端参数
        MonitoringCenterAlert.starts_at <= end_time     # 问题：依赖前端参数
    ).all()

    # 在有限数据集中分离今日数据（错误！）
    for alert in all_alerts:
        if today_start <= alert.starts_at <= today_end:
            today_alerts.append(alert)
```

#### 修复后（正确逻辑）：
```python
def fetch_history_alerts(start_time, end_time, today_start):
    """查询历史数据，排除今日数据"""
    return MonitoringCenterAlert.query.filter(
        MonitoringCenterAlert.starts_at >= start_time,
        MonitoringCenterAlert.starts_at <= end_time,
        MonitoringCenterAlert.starts_at < today_start  # 排除今日
    ).all()

def fetch_today_alerts(today_start, today_end):
    """独立查询今日数据，不受前端参数影响"""
    return MonitoringCenterAlert.query.filter(
        MonitoringCenterAlert.starts_at >= today_start,
        MonitoringCenterAlert.starts_at <= today_end
    ).all()
```

### 验证测试
创建了专门的测试用例验证修复效果：

**测试场景1**：前端查询包含今天的时间范围
```json
{
    "date": "2025-07-31",
    "start_time": "2025-07-25 00:00:00",
    "end_time": "2025-07-31 23:59:59"
}
```

**测试场景2**：前端查询不包含今天的时间范围
```json
{
    "date": "2025-07-24",
    "start_time": "2025-07-18 00:00:00",
    "end_time": "2025-07-24 23:59:59"
}
```

**期望结果**：两个场景返回相同的 `today_status` 数据，不同的 `items` 数据

### 修复验证
- ✅ **测试脚本**：`test_today_status_fix.py` - 端到端验证
- ✅ **单元测试**：`test_alert_functions.py` - 函数级验证
- ✅ **边界测试**：验证未来日期查询的正确性
- ✅ **一致性测试**：确保不同参数下 `today_status` 完全一致

## 总结

此次优化完全符合前端 HealthPanel v4.0.0 的设计要求，实现了：

1. ✅ 数据源分离：历史数据与实时数据完全独立
2. ✅ 实时状态展示：专门的 `today_status` 字段
3. ✅ 状态统计优化：移除混合统计，支持纯实时统计
4. ✅ 向后兼容：保持核心API结构不变
5. ✅ 性能优化：分离查询减少数据处理复杂度
6. ✅ **逻辑修复**：`today_status` 字段现在真正独立于前端时间参数

该优化为前端提供了更清晰的数据结构，支持更准确的实时健康状态监控。
