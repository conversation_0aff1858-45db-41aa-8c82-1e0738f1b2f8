#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
HealthPanel 文档完整性验证脚本
"""

import os
import re
from typing import List, Dict, Tuple

def check_file_exists(file_path: str) -> bool:
    """检查文件是否存在"""
    return os.path.exists(file_path)

def count_lines(file_path: str) -> int:
    """统计文件行数"""
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            return len(f.readlines())
    except Exception:
        return 0

def extract_headers(file_path: str) -> List[str]:
    """提取文档标题"""
    headers = []
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            for line in f:
                if line.startswith('#'):
                    headers.append(line.strip())
    except Exception:
        pass
    return headers

def check_code_blocks(file_path: str) -> int:
    """统计代码块数量"""
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            content = f.read()
            return len(re.findall(r'```', content)) // 2
    except Exception:
        return 0

def validate_documentation():
    """验证文档完整性"""
    
    print("=" * 60)
    print("HealthPanel 文档完整性验证")
    print("=" * 60)
    
    # 定义文档列表
    docs = {
        "综合技术文档": "docs/HealthPanel-Complete-Documentation.md",
        "API 优化文档": "docs/HealthPanel-API-Optimization.md", 
        "代码优化文档": "docs/HealthPanel-Code-Optimization.md",
        "修复记录文档": "docs/HealthPanel-TodayStatus-Fix.md",
        "文档索引": "docs/README.md"
    }
    
    # 验证文档存在性
    print("1. 文档存在性检查")
    print("-" * 30)
    
    all_exist = True
    for name, path in docs.items():
        exists = check_file_exists(path)
        status = "✅" if exists else "❌"
        print(f"{status} {name}: {path}")
        if not exists:
            all_exist = False
    
    print()
    
    # 统计文档信息
    print("2. 文档统计信息")
    print("-" * 30)
    
    total_lines = 0
    total_code_blocks = 0
    
    for name, path in docs.items():
        if check_file_exists(path):
            lines = count_lines(path)
            code_blocks = check_code_blocks(path)
            total_lines += lines
            total_code_blocks += code_blocks
            
            print(f"{name}:")
            print(f"  📄 行数: {lines}")
            print(f"  💻 代码块: {code_blocks}")
            print()
    
    print(f"📊 总计:")
    print(f"  📄 总行数: {total_lines}")
    print(f"  💻 总代码块: {total_code_blocks}")
    print()
    
    # 检查综合文档的结构
    print("3. 综合文档结构检查")
    print("-" * 30)
    
    complete_doc_path = "docs/HealthPanel-Complete-Documentation.md"
    if check_file_exists(complete_doc_path):
        headers = extract_headers(complete_doc_path)
        
        # 期望的主要章节
        expected_sections = [
            "# HealthPanel 完整技术文档",
            "## 1. 概述和架构",
            "## 2. 后端 API 实现", 
            "## 3. 代码优化记录",
            "## 4. 问题修复记录",
            "## 5. 测试验证方案",
            "## 6. 部署和维护指南",
            "## 7. 总结"
        ]
        
        print("期望章节检查:")
        for section in expected_sections:
            found = any(section in header for header in headers)
            status = "✅" if found else "❌"
            print(f"{status} {section}")
        
        print(f"\n📋 总章节数: {len([h for h in headers if h.startswith('##')])}")
        print(f"📋 总标题数: {len(headers)}")
    else:
        print("❌ 综合文档不存在，无法检查结构")
    
    print()
    
    # 检查关键内容
    print("4. 关键内容检查")
    print("-" * 30)
    
    key_content_checks = [
        ("API 接口地址", "/alertscenter/api/v1/panel"),
        ("today_status 字段", "today_status"),
        ("数据源分离", "数据源分离"),
        ("性能优化", "性能优化"),
        ("测试验证", "测试验证"),
        ("部署指南", "部署")
    ]
    
    if check_file_exists(complete_doc_path):
        with open(complete_doc_path, 'r', encoding='utf-8') as f:
            content = f.read()
            
        for check_name, keyword in key_content_checks:
            found = keyword in content
            status = "✅" if found else "❌"
            print(f"{status} {check_name}: 包含 '{keyword}'")
    else:
        print("❌ 无法检查关键内容")
    
    print()
    
    # 文档质量评估
    print("5. 文档质量评估")
    print("-" * 30)
    
    quality_score = 0
    max_score = 10
    
    # 评估标准
    if all_exist:
        quality_score += 2
        print("✅ 文档完整性: +2分")
    else:
        print("❌ 文档完整性: 0分")
    
    if total_lines > 800:
        quality_score += 2
        print("✅ 文档详细程度: +2分")
    else:
        print("⚠️  文档详细程度: +1分")
        quality_score += 1
    
    if total_code_blocks > 20:
        quality_score += 2
        print("✅ 代码示例丰富: +2分")
    else:
        print("⚠️  代码示例一般: +1分")
        quality_score += 1
    
    # 检查是否有目录
    if check_file_exists(complete_doc_path):
        with open(complete_doc_path, 'r', encoding='utf-8') as f:
            content = f.read()
        if "## 目录" in content:
            quality_score += 2
            print("✅ 包含目录索引: +2分")
        else:
            print("❌ 缺少目录索引: 0分")
    
    # 检查是否有交叉引用
    if "参考" in content or "查看" in content:
        quality_score += 2
        print("✅ 包含交叉引用: +2分")
    else:
        print("❌ 缺少交叉引用: 0分")
    
    print(f"\n📊 文档质量评分: {quality_score}/{max_score}")
    
    if quality_score >= 8:
        grade = "优秀 🏆"
    elif quality_score >= 6:
        grade = "良好 👍"
    elif quality_score >= 4:
        grade = "一般 ⚠️"
    else:
        grade = "需要改进 📝"
    
    print(f"📊 文档质量等级: {grade}")
    
    print()
    
    # 建议和总结
    print("6. 建议和总结")
    print("-" * 30)
    
    if all_exist and quality_score >= 8:
        print("🎉 恭喜！文档整合完成，质量优秀")
        print("✅ 所有文档都已创建并包含丰富的技术细节")
        print("✅ 文档结构清晰，便于查阅和维护")
        print("✅ 代码示例充足，有助于理解和实现")
    else:
        print("📝 文档整合基本完成，但仍有改进空间：")
        
        if not all_exist:
            print("- 补充缺失的文档文件")
        if total_lines < 800:
            print("- 增加更多技术细节和说明")
        if total_code_blocks < 20:
            print("- 添加更多代码示例和配置说明")
        if quality_score < 8:
            print("- 完善文档结构和交叉引用")
    
    print()
    print("📚 推荐阅读顺序:")
    print("1. docs/README.md - 了解文档结构")
    print("2. docs/HealthPanel-Complete-Documentation.md - 完整技术参考")
    print("3. 根据需要查阅专项文档")
    
    print()
    print("=" * 60)
    print("文档验证完成")
    print("=" * 60)

if __name__ == "__main__":
    validate_documentation()
