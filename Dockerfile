FROM ccr.ccs.tencentyun.com/wego-ops/python:3.9.10.apiops

LABEL maintainer="wanglh <<EMAIL>>"
WORKDIR /opt

ENV FLASK_APP="main:create_app()"
ENV FLASK_ENV="prod"

COPY ["./","./"]

# 时区设置
RUN rm -f /etc/localtime \
    && ln -sf /usr/share/zoneinfo/Asia/Shanghai /etc/localtime \
    && echo "Asia/Shanghai" > /etc/timezone
RUN chmod 777 ./docker_run.sh

# 启动应用进程
EXPOSE 4001
ENTRYPOINT ["/bin/bash"]
CMD ["docker_run.sh"]
