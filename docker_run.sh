#!/bin/bash
# @Title:   docker_run.sh
# @Date:    2021-06-23 15:41:00
# @Author:  <PERSON>L<PERSON>
# @Desc:    容器启动

echo "$(pwd)"
mkdir /root/.pip
cp -rp ./pip.conf /root/.pip/pip.conf

pip install --upgrade pip==24.0
/usr/local/bin/pip3 install markupsafe
/usr/local/bin/pip3 install -r requirements.txt
/usr/local/bin/pip3 install sqlparse

[ -f flask.log ] && echo "" > flask.log
if [ ! -d /opt/logs ];then
  mkdir -p /opt/logs
fi

if [ ! -f /opt/logs/flask.log ];then
  touch /opt/logs/flask.log
fi
echo "当前模式：${FLASK_ENV}"

# 显式设置 FLASK_APP 环境变量
export FLASK_APP="main:app"
flask run -p 4001 -h 0.0.0.0 >> flask.log
