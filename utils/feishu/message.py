#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
@Title:     messages.py
@Date:      2023/6/6 14:30
@Author:    WangLH
@Desc:      飞书消息管理中心
"""
import json

import requests

from utils import get_tenant_access_token
from utils.feishu import config_read, env
from utils.feishu.baseClass import retry_get_token
from utils.messagepub import group_names


def send_feishu_message(receive_id: str,
                        msg_type: str,
                        receive_id_type: str = None,
                        template_variable: dict = None,
                        card_id: str = None):
    """
    发送消息
    Args
        receive_id (str): 用户ID或者chat_id
        msg_type (str): 消息类型 文本：text  模板卡片：interactive
        receive_id_type (str): 消息主体的类型，默认从配置获取：chat_id；open_id: 应用单独发送到个人，chat_id：应用机器人发送到所在的群组
        template_variable (dict): 消息发送内容
    Returns
        response http.Response
    """
    if card_id is None or card_id == "":
        card_id = "release"
    message_id = config_read.get(f"message-id-{env}", card_id)  # 卡片ID
    receive_id_type = config_read.get(f"message-id-{env}",
                                      f"{card_id}_id_type") if receive_id_type is None else receive_id_type

    # 判断消息类型
    if msg_type == "interactive":
        # 自定义template_variable
        if template_variable:
            pass
        else:
            template_variable = {
                "change_title": "飞书消息测试功能： goods111 <font color='green'>发布中... </font>",
                "change_env": "B",
                "change_version": "goods111",
                "change_owner": f"<at id={receive_id}></at>",
                "change_backend": "前端",
                "change_link": "<a href='https://slowisfast.feishu.cn/docx/SxbZdQXpHoRRimxk8ficVMqZnaf'></a>",
                "change_text": "1. 自定义&转发来源支持删除\n2. 自定义来源支持改名\n3. 来源字符 18 字上限改为 36 字\n4. 网址、专享转让增加日志"
            }
        # 或许需要发送的receive_id

        message_content = {
            "type": "template",
            "data": {
                "template_id": message_id,
                "template_variable": template_variable
            }
        }

    if msg_type == "text":
        message_content = template_variable
    # 主体发送函数
    params = {
        "receive_id": receive_id,  # 使用open_id，用户向单个用户发送，使用chat_id 用户向多个用户发送
        "msg_type": msg_type,
        "content": json.dumps(message_content)
    }
    payload = json.dumps(params)
    headers = {
        'Content-Type': 'application/json',
        'Authorization': f'Bearer {get_tenant_access_token()}'
    }
    url = f"https://open.feishu.cn/open-apis/im/v1/messages?receive_id_type={receive_id_type}"
    try:
        response = requests.request("POST", url, headers=headers, data=payload)
        response.raise_for_status()
    except requests.exceptions.HTTPError:
        headers = {
            'Content-Type': 'application/json',
            'Authorization': f'Bearer {retry_get_token()}'
        }
        response = requests.request("POST", url, headers=headers, data=payload)
    return response


def feishu_robot(name, value):
    """
    飞书机器人消息
    :param name:
    :param value:
    :return:
    """
    url = f"https://open.feishu.cn/open-apis/bot/v2/hook/{group_names[name]}"
    headers = {
        'Content-Type': 'application/json; charset=utf-8',
    }
    message = {
        "msg_type": "text",
        "content": {
            "text": value
        }
    }
    message = json.dumps(message)
    req = requests.post(url, headers=headers, data=message)
    if req.json()['StatusMessage'] != "success":
        return "消息发送失败"
    return "发送成功"

def feishu_robot_v2(roboturl, message):
    """
    飞书机器人发送卡片消息
    """
    headers = {
        'Content-Type': 'application/json; charset=utf-8',
    }
    data = json.dumps(message)
    req = requests.post(roboturl, headers=headers, data=data)
    # 检查响应状态码
    req.raise_for_status()
    response = req.json()
    if response.get('StatusCode',"1") == 0 and response.get('StatusMessage',"") == "success":
        return "消息发送成功"
    return "消息发送失败"