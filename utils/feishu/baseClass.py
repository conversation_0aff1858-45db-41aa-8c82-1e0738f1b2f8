#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
@Title:     baseClass.py
@Date:      2023/5/17 17:06
@Author:    WangLH
@Desc:      基础类
"""
import datetime
import json
import os
import pickle
import time
from typing import Dict, Union

import pytz
import requests
from flask import request

from utils.feishu import config_read, env


# config_read = ConfigParser()
# env = os.environ.get('FLASK_ENV')


# 假设这里是获取 Token 的代码
# def get_tenant_access_token(app_id: str, app_secret: str) -> str:
#     """
#     使用 app_id 和 app_secret 获取租户访问令牌
#
#     Args:
#         app_id (str): 应用 ID
#         app_secret (str): 应用秘钥
#     Returns:
#         str: 返回一个字符串，是获取到的访问令牌（或空字符串）
#     """
#     url = "https://open.feishu.cn/open-apis/auth/v3/tenant_access_token/internal"
#     # 构造请求，包含 app_id 和 app_secret
#     req = {
#         "app_id": app_id,
#         "app_secret": app_secret
#     }
#     headers = {
#         "Content-Type": "application/json; charset=utf-8"
#     }
#     # 将请求体转为 json 格式
#     payload = json.dumps(req)
#     # 发送 Post 请求
#     response = requests.request("POST", url, headers=headers, data=payload)
#     # 获取访问令牌，返回空字符串若未获取到
#     tenant_access_token = response.json().get('tenant_access_token', '')
#     return tenant_access_token


def get_token(app_id: str, app_secret: str) -> str:
    """
    使用 app_id 和 app_secret 获取租户访问令牌

    Args:
        app_id (str): 应用 ID
        app_secret (str): 应用秘钥
    Returns:
        str: 返回一个字符串，是获取到的访问令牌（或空字符串）
    """
    url = "https://open.feishu.cn/open-apis/auth/v3/tenant_access_token/internal"
    # 构造请求，包含 app_id 和 app_secret
    req = {
        "app_id": app_id,
        "app_secret": app_secret
    }
    headers = {
        "Content-Type": "application/json; charset=utf-8"
    }
    # 将请求体转为 json 格式
    payload = json.dumps(req)
    # 发送 Post 请求
    response = requests.request("POST", url, headers=headers, data=payload)
    # 获取访问令牌，返回空字符串若未获取到
    tenant_access_token = response.json().get('tenant_access_token', '')
    expire_time = time.time() + 3600  # 2 hours
    return tenant_access_token, expire_time


def save_token(token, expire_time):
    """
    # 保存 Token 到本地文件
    :param token:
    :param expire_time:
    :return: None
    """
    with open(f"token.pickle.{env}", "wb") as f:
        data = {"token": token, "expire_time": expire_time}
        pickle.dump(data, f)


def load_token() -> Union[str, None]:
    """
    从本地文件加载 Token
    :return:
    """
    max_retries = 5  # 最大重试次数
    retry_count = 0  # 当前重试次数
    if os.path.exists(f"token.pickle.{env}"):
        while True:
            try:
                with open(f"token.pickle.{env}", "rb") as f:
                    data = pickle.load(f)
                    if data["expire_time"] > time.time():
                        return data["token"]
                    else:
                        os.remove(f"token.pickle.{env}")
            except PermissionError:
                # 文件被占用，等待一段时间再尝试访问
                time.sleep(1)
                retry_count += 1

                if retry_count >= max_retries:
                    # 超过最大重试次数，退出循环
                    os.remove(f"token.pickle.{env}")
                    break
            else:
                # 文件访问成功，退出循环
                break
    return None


def get_tenant_access_token():
    """
    获取 Token
    :return:
    """
    token = load_token()
    if token is None:
        token, expire_time = get_token(config_read.get(f"ops-reboot-{env}", "app_id"),
                                       config_read.get(f"ops-reboot-{env}", "app_secret"))
        save_token(token, expire_time)
    return token


def retry_get_token():
    """
    重新获取token，并写入
    :return:
    """
    token, expire_time = get_token(config_read.get(f"ops-reboot-{env}", "app_id"),
                                   config_read.get(f"ops-reboot-{env}", "app_secret"))
    save_token(token, expire_time)
    return token


def upload_image(filepath: str, tenant_access_token: str) -> str:
    """
    将指定的文件上传到飞书并获取图像密钥
    Args:
        filepath (str): 文件路径
        tenant_access_token (str): 租户访问令牌
    Returns:
        str：返回上传的图像密钥或空值
    """
    url = "https://open.feishu.cn/open-apis/im/v1/images"
    # 准备要发送的内容：图像类型和图像名称
    payload = {'image_type': 'message'}
    image_name = filepath.split('/')[-1]
    # 准备文件列表
    files = [
        ('image', (image_name, open(filepath, 'rb'), 'application/json'))
    ]
    # 准备请求头
    headers = {
        'Authorization': f'Bearer {tenant_access_token}'
    }
    # 发送 POST 请求
    response = requests.request("POST", url, headers=headers, data=payload, files=files)
    # 解析响应并返回图像密钥，或者返回空值
    res = response.json()
    if res.get('code') == 0:
        return res.get('data').get('image_key')
    return None


def convert_to_rfc3339(input_time: str, time_zone: str) -> str:
    """
    时间处理：将字符串时间转换为符合rfc3339的格式
    Args:
        input_time (str): 传递的str时间"%Y-%m-%d %H:%M:%S"
        time_zone (str): 时区
    Returns:
        str
    """

    dt = datetime.datetime.strptime(input_time, "%Y-%m-%d %H:%M:%S")  # 将输入字符串时间转换为datetime对象
    tz = pytz.timezone(time_zone)  # 创建时区对象
    dt_tz = tz.localize(dt)  # 将datetime对象转换为时区时间
    output_time = dt_tz.isoformat()  # 将时区时间转换为RFC3339格式的字符串
    return output_time


def get_response(request_object: request) -> Dict:
    """
    从request对象中获取数据
    Args:
        request_object (request): request对象
    Returns:
        Dict
    """
    global datas
    form_data = ['multipart/form-data', 'application/x-www-form-urlencoded']
    json_data = ['application/json']
    if request_object.method == 'POST' or request_object.method == 'DELETE':
        try:
            if request_object.headers.get('content_type-type') in json_data:
                datas = request_object.json
            elif request_object.headers.get('content_type') in form_data:
                pass
            else:
                datas = request_object.get_json(force=True)
        except Exception as e:
            print(e)
            datas = dict()
    if request_object.method == 'GET':
        # data = request_object.values
        data = request_object.args
        datas = {i: data.get(i) for i in data}
    return datas


def get_timestamp(time_str=None):
    if time_str is None:
        timestamp = int(time.time())
        millisecond = int(round(time.time() * 1000))
    else:
        try:
            timestamp = int(time.mktime(time.strptime(time_str, '%Y-%m-%d %H:%M:%S')))  # 字符串时间
        except ValueError:
            try:
                timestamp = int(time.mktime(datetime.strptime(time_str, '%Y-%m-%dT%H:%M:%S').timetuple()))  # datetime时间
            except ValueError:
                timestamp = int(time.mktime(datetime.strptime(time_str, '%Y-%m-%dT%H:%M:%S+08:00').timetuple()))  # %Y-%m-%dT%H:%M:%S+08:00等类型的时间

        millisecond = int(round(timestamp * 1000))

    return timestamp, millisecond


def convert_to_rfc3339(input_time: str, time_zone: str) -> str:
    """
    时间处理：将字符串时间转换为符合rfc3339的格式
    Args:
        input_time (str): 传递的str时间"%Y-%m-%d %H:%M:%S"
        time_zone (str): 时区
    Returns:
        str
    """

    dt = datetime.datetime.strptime(input_time, "%Y-%m-%d %H:%M:%S")  # 将输入字符串时间转换为datetime对象
    tz = pytz.timezone(time_zone)  # 创建时区对象
    dt_tz = tz.localize(dt)  # 将datetime对象转换为时区时间
    output_time = dt_tz.isoformat()  # 将时区时间转换为RFC3339格式的字符串
    return output_time
