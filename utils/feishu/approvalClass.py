#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
@Title:     approvalClass.py
@Date:      2023/6/9 14:11
@Author:    WangLH
@Desc:      审批函数后端参数构建，减少文件approval.py的复杂度
"""

import json
from typing import Dict, List, Tuple

from model import FeishuApprovalDef, FeishuApprovalNode, FeishuUserData
from .baseClass import convert_to_rfc3339


def get_def_data(def_query):
    """
    将审批定义查询结果转换为所需的数据结构

    :param def_query: 审批定义查询结果
    :return: 转换后的审批定义数据列表
    """
    def_datas = FeishuApprovalDef.to_all_json(def_query)
    def_data = []
    for item in def_datas:
        option = item.get('option')
        if option and item.get('type') != 'fieldList':
            options = json.loads(option.replace('\'', '"'))  # 将 option 转换为列表
        item_dict = {
            'id': item.get('id'),
            'name': item.get('name'),
            'type': item.get('type'),
            'required': bool(item.get('required')),
            'option': options
        }
        def_data.append(item_dict)
    return def_data


def get_name_list(approver_range_type: str, approver_range_ids: str) -> List[str]:
    """
    从审批范围获取用户名称和用户ID

    :param approver_range_type: 审批范围类型
    :param approver_range_ids: 审批范围ID列表
    :return: 用户名称和用户ID的列表
    """
    name_list = []
    if approver_range_type == 2:
        approval_range = approver_range_ids.split(',')
        for open_id in approval_range:
            query = FeishuUserData.query.filter_by(open_id=open_id).with_entities(FeishuUserData.name,
                                                                                  FeishuUserData.open_id).first()
            if query:
                keys = query.keys()
                result_dict = dict(zip(keys, query))
                name_list.append(result_dict)
    else:
        # 获取所有的用户
        query = FeishuUserData.query.with_entities(FeishuUserData.name, FeishuUserData.open_id).all()
        if query:
            keys = query[0].keys()
            for row in query:
                result_dict = dict(zip(keys, row))
                name_list.append(result_dict)
    return name_list


def get_node_list(node_query: FeishuApprovalNode) -> List[str]:
    """
    将审批节点查询结果转换为所需的数据结构

    :param node_query: 审批节点查询结果
    :return: 转换后的审批节点数据列表
    """
    node_datas = FeishuApprovalNode.to_all_json(node_query)
    node_list = []
    for item in node_datas:
        approver_range_type = item.get('approver_range_type', 3)
        approver_range_ids = item.get('approver_range_ids')
        name_list = get_name_list(approver_range_type, approver_range_ids)
        item_dict = {
            'name': item.get('name'),
            'node_id': item.get('node_id'),
            'name_list': name_list
        }
        node_list.append(item_dict)
    return node_list


def build_form(
        fieldlist_query: FeishuApprovalDef,
        def_query_dict: Dict[str, FeishuApprovalDef],
        get_params: Dict[str, str],
) -> List[dict]:
    """构建表单数据"""
    fieldlist_ids = fieldlist_query.option.split(',')

    def type_assembly(record: FeishuApprovalDef, value_: str) -> dict:
        if record.type in {"input", "textarea"}:
            value = value_
        elif record.type == "date":
            value = convert_to_rfc3339(value_, "Asia/Shanghai")
        elif record.type == "checkboxV2":
            option = json.loads(record.option.replace("'", "\""))
            for item in option:
                if value_ == item.get("text"):
                    value = [item.get("value")]
        return {"id": record.id, "type": record.type, "value": value, "required": bool(record.required)}

    form = []
    field_list_value = []

    for html_id, record in def_query_dict.items():
        value_ = get_params.get(html_id)
        if record.id in fieldlist_ids:
            field_list_value.append(type_assembly(record, value_))
        else:
            form.append(type_assembly(record, value_))

    form.append({"id": fieldlist_query.id, "type": fieldlist_query.type, "value": [field_list_value]})

    return form


def build_node_approver_open_id_list(
        node_query_dict: Dict[str, FeishuApprovalNode], get_params: Dict[str, str], id_list: List[str]
) -> Tuple[List[dict], List[str]]:
    """构建审批节点数据"""

    node_approver_open_id_list = []

    for html_id, record in node_query_dict.items():
        user_query = FeishuUserData.query.filter_by(name=get_params.get(html_id)).first()
        if user_query.open_id not in record.approver_range_ids and html_id != "back_appover":
            continue
        node_approver_open_id_list.append({"key": record.node_id, "value": [user_query.open_id]})
        id_list.append(user_query.open_id)

    return node_approver_open_id_list, id_list
