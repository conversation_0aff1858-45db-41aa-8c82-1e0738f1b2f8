#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
@Title:     __init__.py
@Date:      2023/5/31 11:12
@Author:    wanglh
@Desc：     配置信息
"""
import os
from configparser import ConfigParser

current_path = os.path.abspath(__file__)
current_dir = os.path.dirname(os.path.dirname(current_path))

config_path = os.path.join(os.path.dirname(current_dir), 'config.ini')
config_read = ConfigParser()
config_read.read(config_path)
env = os.environ.get('FLASK_ENV')
