#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
@Title:     __init__.py
@Date:      2025/2/27 11:22
@Author:    wanglh
@Desc:      自定义告警模型库，用于统一处理不同类型的告警信息
"""

from typing import Dict, List, Optional, Union

from pydantic import BaseModel, Field


class BaseAlert(BaseModel):
    """
    基础告警模型
    用于统一告警数据结构
    """

    alert_type: str = Field(
        default="custom", description="告警类型: pinpoint/rocketmq/custom"
    )
    alert_title: str = Field(..., description="告警标题")
    alert_object: str = Field(..., description="告警对象")
    alert_severity: str = Field(..., description="告警级别")
    alert_message: str = Field(..., description="告警详情")
    status: str = Field(..., description="告警状态: firing(告警) / resolved(恢复)")
    starts_at_str: str = Field(..., description="告警开始时间")
    business_ids: Union[str, List[str]] = Field(
        default="", description="业务ID列表，支持字符串或列表"
    )

    @property
    def business_id_list(self) -> List[str]:
        """
        获取业务ID列表
        支持字符串（逗号分隔）或列表类型的输入
        """
        if isinstance(self.business_ids, list):
            return [str(bid).strip() for bid in self.business_ids if str(bid).strip()]
        elif isinstance(self.business_ids, str):
            if not self.business_ids:
                return []
            return [bid.strip() for bid in self.business_ids.split(",") if bid.strip()]
        return []

    @property
    def priority(self) -> int:
        """
        根据告警级别返回优先级
        critical -> 2 (紧急)
        warning -> 1 (重要)
        info -> 0 (普通)
        """
        severity_map = {"critical": 2, "error": 2, "warning": 1, "info": 0}
        return severity_map.get(self.alert_severity.lower(), 0)

    @property
    def alert_title(self) -> str:
        """获取告警标题"""
        return self.alert_title

    @property
    def alert_object(self) -> str:
        """获取告警对象"""
        return self.alert_object

    @property
    def alert_message(self) -> str:
        """获取告警消息"""
        return self.alert_message

    @property
    def alert_severity(self) -> str:
        """获取告警级别"""
        return self.alert_severity

    @property
    def type_(self) -> str:
        """获取告警类型"""
        return self.alert_type

    @property
    def starts_at_str(self) -> str:
        """获取告警开始时间"""
        return self.starts_at_str

    class Config:
        populate_by_name = True
        arbitrary_types_allowed = True


class PinPointAlert(BaseModel):
    """
    PinPoint告警模型
    用于处理PinPoint系统的告警信息
    """

    alert_type: str = Field(default="pinpoint", description="告警类型")
    status: str = Field(..., description="告警状态")
    labels: Dict[str, str] = Field(..., description="告警标签信息")
    annotations: Dict[str, str] = Field(..., description="告警注释信息")
    startsAt: str = Field(..., description="告警开始时间")
    endsAt: str = Field(default="0001-01-01T00:00:00Z", description="告警结束时间")

    @property
    def priority(self) -> int:
        """
        根据告警级别返回优先级
        critical -> 2 (紧急)
        warning -> 1 (重要)
        info -> 0 (普通)
        """
        severity_map = {"critical": 2, "error": 2, "warning": 1, "info": 0}
        return severity_map.get(self.alert_severity.lower(), 0)

    @property
    def alert_title(self) -> str:
        """获取告警标题"""
        return self.labels.get("alertname", "")

    @property
    def alert_object(self) -> str:
        """获取告警对象"""
        return self.labels.get("appname", "")

    @property
    def alert_message(self) -> str:
        """获取告警消息"""
        return self.annotations.get("description", "")

    @property
    def alert_severity(self) -> str:
        """获取告警级别"""
        return self.labels.get("severity", "")

    @property
    def business_id_list(self) -> List[str]:
        """
        获取业务ID列表
        支持逗号分隔的多个业务ID
        """
        business_ids = self.labels.get("business_ids", "")
        if not business_ids:
            return []
        # 分割字符串并去除空白字符
        return [bid.strip() for bid in business_ids.split(",") if bid.strip()]

    @property
    def type_(self) -> str:
        """获取告警类型"""
        return self.alert_type

    @property
    def starts_at_str(self) -> str:
        """获取告警开始时间"""
        return self.startsAt


class MqTopicAlert(BaseModel):
    """
    RocketMQ告警模型
    用于处理RocketMQ相关的告警信息
    """

    alert_type: str = Field(default="rocketmq", description="告警类型")
    status: str = Field(..., description="告警状态")
    labels: Dict[str, str] = Field(..., description="告警标签信息")
    annotations: Dict[str, str] = Field(..., description="告警注释信息")
    startsAt: str = Field(..., description="告警开始时间")
    endsAt: str = Field(default="0001-01-01T00:00:00Z", description="告警结束时间")

    @property
    def priority(self) -> int:
        """
        根据告警级别返回优先级
        critical -> 2 (紧急)
        warning -> 1 (重要)
        info -> 0 (普通)
        """
        severity_map = {"critical": 2, "error": 2, "warning": 1, "info": 0}
        return severity_map.get(self.alert_severity.lower(), 0)

    @property
    def alert_title(self) -> str:
        """获取告警标题"""
        return self.labels.get("alertname", "")

    @property
    def alert_object(self) -> str:
        """获取告警对象"""
        return self.labels.get("topic", "")

    @property
    def alert_message(self) -> str:
        """获取告警消息"""
        return self.annotations.get("description", "")

    @property
    def alert_severity(self) -> str:
        """获取告警级别"""
        return self.labels.get("severity", "")

    @property
    def business_id_list(self) -> List[str]:
        """
        获取业务ID列表
        支持逗号分隔的多个业务ID
        """
        business_ids = self.labels.get("business_ids", "")
        if not business_ids:
            return []
        # 分割字符串并去除空白字符
        return [bid.strip() for bid in business_ids.split(",") if bid.strip()]

    @property
    def type_(self) -> str:
        """获取告警类型（对应group）"""
        return self.labels.get("group", "")

    @property
    def starts_at_str(self) -> str:
        """获取告警开始时间"""
        return self.startsAt


class CustomAlert(BaseAlert):
    """
    自定义告警模型
    用于用户自定义脚本调用，支持扩展额外字段
    """

    alert_type: str = Field(default="custom", description="告警类型")
    extra_info: Optional[dict] = Field(default={}, description="额外的告警信息")
    notify_methods_str: str = Field(
        default="feishu",
        alias="notify_methods",
        description="通知方式，支持逗号分隔的字符串",
    )
    custom_priority: Optional[int] = Field(
        default=None,
        description="自定义告警优先级: 0(普通) / 1(重要) / 2(紧急)，若不设置则根据告警级别自动判断",
    )

    @property
    def priority(self) -> int:
        """
        获取告警优先级
        如果设置了自定义优先级，则返回自定义优先级
        否则根据告警级别返回对应的优先级
        critical/error -> 2 (紧急)
        warning -> 1 (重要)
        info -> 0 (普通)
        """
        if self.custom_priority is not None:
            return self.custom_priority
        return super().priority

    @property
    def notify_methods(self) -> List[str]:
        """
        获取通知方式列表
        支持字符串（逗号分隔）或列表类型的输入
        """
        if not self.notify_methods_str:
            return []
        return [
            method.strip()
            for method in self.notify_methods_str.split(",")
            if method.strip()
        ]

    @notify_methods.setter
    def notify_methods(self, value: Union[str, List[str]]) -> None:
        """
        设置通知方式
        支持字符串（逗号分隔）或列表类型的输入
        """
        if isinstance(value, list):
            self.notify_methods_str = ",".join(
                str(v).strip() for v in value if str(v).strip()
            )
        elif isinstance(value, str):
            self.notify_methods_str = value
        else:
            self.notify_methods_str = ""
