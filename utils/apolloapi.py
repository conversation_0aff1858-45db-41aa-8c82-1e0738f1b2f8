#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
@Title:   apolloapi.py
@Date:    2022/5/19 20:23
@Author:  wanglh
@Desc:    apollo相关配置获取
"""
import json
from datetime import datetime

import requests
from flask import current_app, jsonify


class ApolloWego:
    def __init__(self, envid=None, clustername=None):
        self.headers = {
            "Authorization": "0bcccb2d9a98f267b151aef90bde8b069f5a4d44",
            "Content-Type": "application/json;charset=UTF-8"
        }
        apps_url = "http://**********:8070/openapi/v1/apps"
        req = requests.get(apps_url, headers=self.headers)
        apps_dict = req.json()

        self.appId = [app["appId"] for app in apps_dict]
        self.env = envid
        self.clusterName = clustername
        self.url = "**********:8070"

    def clusters_all_namespace(self, appId):
        """
        获取集群下的appid的namespaces
        :param appId:
        :return:
        """
        url = f"http://**********:8070/openapi/v1/envs/{self.env}/apps/{appId}/clusters/{self.clusterName}/namespaces"
        response = requests.get(url=url, headers=self.headers)
        req = response.json()
        data = []
        column_str = "uat_content" if self.clusterName == "uat" else "new_content"
        if response.status_code == 200:
            for req_dict in req:
                if req_dict["format"] != "properties":
                    content = req_dict["items"][0]["value"] if len(req_dict["items"]) > 0 else ""
                    data.append({"service": appId, "filename": req_dict["namespaceName"], column_str: content})
                else:
                    filename = f"{req_dict['namespaceName']}.{req_dict['format']}"
                    content = [f"{item['key']}={item['value']}" for item in req_dict["items"]] if len(
                        req_dict["items"]) > 0 else ""
                    data.append({"service": appId, "filename": filename, column_str: "\n".join(content)})
        return data

    def get_cluster_namespace(self, appid, filename):
        """
        :param appid:  服务名称
        :param filename: 文件名，包含后缀
        :return:
        """
        if "properties" == filename.split('.')[-1]:
            filename = ".".join(filename.split('.')[:-1])
        url = f"http://**********:8070/openapi/v1/envs/{self.env}/apps/{appid}/clusters" \
              f"/{self.clusterName}/namespaces/{filename}"
        response = requests.get(url=url, headers=self.headers)
        req = response.json()
        if response.status_code == 200:
            if req["format"] != "properties":
                content = req["items"][0]["value"] if len(req["items"]) > 0 else ""
            else:
                content = [f"{item['key']}={item['value']}" for item in req["items"]] if len(req["items"]) > 0 else ""
                content = "\n".join(content)
            return content
        return None

    def change_namespace(self, appid, filename, new_content):
        try:
            if "properties" == filename.split('.')[-1]:
                filename = ".".join(filename.split('.')[:-1])
                keys_values = {i.split('=')[0]: i.split('=')[-1] for i in new_content.split('\n')}
                for key, value in keys_values.items():
                    url = f"http://**********:8070/openapi/v1/envs/{self.env}/apps/{appid}/clusters" \
                          f"/{self.clusterName}/namespaces/{filename}/items/{key}"
                    body_json = {
                        "key": key,
                        "value": value,
                        "dataChangeLastModifiedBy": "apollo"
                    }
                    response = requests.request(method='PUT', url=url, headers=self.headers, data=json.dumps(
                        body_json))
                    response.raise_for_status()
            else:
                key = "content"

                url = f"http://**********:8070/openapi/v1/envs/{self.env}/apps/{appid}/clusters" \
                      f"/{self.clusterName}/namespaces/{filename}/items/{key}"
                body_json = {
                    "key": key,
                    "value": new_content,
                    "dataChangeLastModifiedBy": "apollo"
                }
                response = requests.request(method='PUT', url=url, headers=self.headers, data=json.dumps(body_json))
                response.raise_for_status()
            rel_code = self.release_namespace(appid, filename)
            return rel_code
        except Exception as e:
            return f"ERROR: 配置修改失败，Exception：{e}"

    def add_namespace(self, appid, filename, new_content):
        try:
            if "properties" == filename.split('.')[-1]:
                filename = ".".join(filename.split('.')[:-1])
                keys_values = {i.split('=')[0]: i.split('=')[-1] for i in new_content.split('\n')}
                for key, value in keys_values.items():
                    url = f"http://apollo.in.wsxcme.com/openapi/v1/envs/{self.env}/apps/{appid}/clusters" \
                          f"/{self.clusterName}/namespaces/{filename}/items"
                    body_json = {
                        "key": key,
                        "value": value,
                        "dataChangeCreatedBy": "apollo"
                    }
                    response = requests.request(method='POST', url=url, headers=self.headers, data=json.dumps(
                        body_json))
                    response.raise_for_status()
            else:
                key = "content"

                url = f"http://**********:8070/openapi/v1/envs/{self.env}/apps/{appid}/clusters" \
                      f"/{self.clusterName}/namespaces/{filename}/items"
                body_json = {
                    "key": key,
                    "value": new_content,
                    "dataChangeCreatedBy": "apollo"
                }
                response = requests.request(method='POST', url=url, headers=self.headers, data=json.dumps(body_json))
                response.raise_for_status()
            rel_code = self.release_namespace(appid, filename)
            return rel_code
        except Exception as e:
            return f"ERROR: 配置添加失败，Exception：{e}"

    # 发布
    def release_namespace(self, appid, filename):
        try:
            release_url = f"http://**********:8070/openapi/v1/envs/{self.env}/apps/{appid}/clusters" \
                          f"/{self.clusterName}/namespaces/{filename}/releases"
            body_json = {
                "releaseTitle": datetime.now().strftime("%Y-%m-%d %H:%M:%S"),
                "releasedBy": "apollo"
            }
            response = requests.post(url=release_url, headers=self.headers, data=json.dumps(body_json))
            response.raise_for_status()
            return response.status_code
        except Exception as e:
            return f"ERROR: 配置发布失败，Exception：{e}"

    # 修改apollo具体的namespace信息
    def change_namespace_items(self, *args, **kwargs) -> json:
        """
        修改配置，不存在则添加
        Args:
        dict kwargs:
            :kwargs key, : 配置的key, 需和url中的key值一致。非properties格式，key固定为content
            :kwargs value : 配置的value，长度不能超过20000个字符，非properties格式，value为文件全部内容
            :kwargs namespace: 所属的namespace
            :kwargs appid: 所属的appid
        tuple args:

        """
        namespace = kwargs.get('namespace')
        key = kwargs.get('item')
        from utils import code_to_str
        value = code_to_str(kwargs.get('value'))
        appid = kwargs.get('appid')
        suffix = namespace.split('.')[-1]
        filename = namespace if suffix != "properties" else ".".join(namespace.split('.')[:-1])
        key = "content" if suffix != "properties" else key
        url = f"http://{self.url}/openapi/v1/envs/{self.env}/apps/{appid}/clusters/{self.clusterName}/namespaces/{filename}/items/{key}?createIfNotExists=true"
        current_app.logger.info(f"当前apollo修改的URL地址为：{url}")  # "createIfNotExists": True 不存在则添加
        body_json = {
            "key": key,
            "value": value,
            "dataChangeLastModifiedBy": "apollo",
            "dataChangeCreatedBy": "apollo"
        }
        current_app.logger.info(f"Apollo传参：{body_json}")
        try:
            response = requests.request("PUT", url=url, headers=self.headers, data=json.dumps(body_json))
            current_app.logger.info(f"请求返回码：{response.status_code}")
            if response.status_code == 200:
                current_app.logger.info(f"Apollo配置：{self.env}-{self.clusterName}:{namespace}，修改成功")
                response = self.release_namespace(appid=appid, filename=filename)  # 发布
                return jsonify({"code": 200, 'msg': f"Apollo配置：{self.env}-{self.clusterName}-{namespace}，修改成功，发布结果：{response}"})
            return jsonify({"code": 403, 'msg': f"Apollo配置：{self.env}-{self.clusterName}-{namespace}，修改失败，联系运维处理"})
        except Exception as e:
            current_app.logger.error(f"{e}")
            return jsonify({'code': 500, "msg": e})


def getApps() -> list:
    """
    Args:
        host: Host
    """
    headers = {
        "Authorization": "0bcccb2d9a98f267b151aef90bde8b069f5a4d44",
        "Content-Type": "application/json;charset=UTF-8"
    }
    host = "**********:8070"
    url = f"http://{host}/openapi/v1/apps"
    req = requests.get(url, headers=headers)
    req = req.json()
    appids = [app["name"] for app in req]
    return appids
