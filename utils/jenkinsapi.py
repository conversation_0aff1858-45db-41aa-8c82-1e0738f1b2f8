#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
@Title:   jenkinsapi.py
@Date:    2022/4/20 10:37
@Update:  2022/7/26 15:31
@Author:  wanglh
@Desc:    Jenkins-API操作
"""

import datetime
import time

import jenkins


class JkJob:
    def __init__(self, jenkins_job, parameters):
        """
        Args:
        jenkins_job: 项目名称;
        parameters：列表元组
        """

        self.jenkins_master = 'http://jenkins.jenkins.test.szwego.com/'
        self.jenkins_user = 'admin'
        self.jenkins_passwd = 'wegooooo'
        self.jenkins_job = jenkins_job
        self.parameters = parameters
        self.jenkins_server = jenkins.Jenkins(url=self.jenkins_master, username=self.jenkins_user,
                                              password=self.jenkins_passwd)

    def buildjob(self):
        """
        触发job
        """
        token = datetime.datetime.now().strftime("%Y%m%d %H:%M:%S")
        if self.parameters:
            return self.jenkins_server.build_job(name=self.jenkins_job, parameters=self.parameters, token=token)
        return self.jenkins_server.build_job(name=self.jenkins_job, token=token)

    def getNextJobId(self):
        """
        获取job的最后构件号
        """
        return self.jenkins_server.get_job_info(name=self.jenkins_job)["nextBuildNumber"]

    def getJobBuilding(self, jobId):
        """
        判断job的构建是否还在构建中
        """
        result = self.jenkins_server.get_build_info(self.jenkins_job, jobId)["building"]
        return result

    def getJobResultStatus(self, jobId):
        """
        获取job某次构建的执行结果
        """
        return self.jenkins_server.get_build_info(self.jenkins_job, jobId)["result"]

    def start(self):
        """
        获取当前jkJob项目的信息，查看下一构建编号
        :return:
        """

        try:
            next_bn = self.getNextJobId()
        except:
            # print("可能是应用名和jenkinsjob名不一致，你可以尝试修改一下应用拓展信息")
            # print("next_bn获取异常，重试")
            time.sleep(5)
            next_bn = self.getNextJobId()
        # print("next_bn", next_bn)

        try:
            self.buildjob()
        except:
            # print("可能是应用名和jenkinsjob名不一致，你可以尝试修改一下应用拓展信息")
            # print("启动jenkinsjob异常，重试")
            time.sleep(5)
            self.buildjob()

        # 判断jenkins job是否启动
        time.sleep(10)
        sleeptime = 0
        # TODO 下面的循环确认是否需要 等待现网验证
        while sleeptime < 50:
            try:
                result = self.getJobBuilding(jobId=next_bn)
            except:
                time.sleep(5)
                # print("plugins job还没有启动起来，继续等待5秒...")
                sleeptime = sleeptime + 5
            else:
                # print("plugins job 启动成功")
                # print("plugins job 启动状态", result)
                sleeptime = 51

        while True:
            # 获取当前状态 Building,building为False表示构建过程完成，但不确定是否构建成功
            result = self.getJobBuilding(jobId=next_bn)
            if result == False:
                jobstatus = self.getJobResultStatus(jobId=next_bn)
                break
        return jobstatus
