#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
@Title:   mykubernetes.py
@Date:    2022/2/24 16:50
@Author:  Wanglh
@Desc:    创建沙盒集群-测试集群
"""

import os
import time

import yaml
from kubernetes import client, config

"""
1. 导入kubernetes的相关模块和工具包
2. 连接：config.load_kube_config(config<集群的配置文件>)
2. 通过client.AppsV1Api 创建namespace和deployment
4. 获取是否成功，成功返回sys.exit(0)
"""


# 创建，删除deployment service

def create_deployment(api_instance, body, namespace):
    api_response = api_instance.create_namespaced_deployment(body=body, namespace=namespace)
    return api_response


def delete_deployment(api_instace, name, namespace):
    api_response = api_instace.delete_namespaced_deployment(name=name, namespace=namespace)
    return api_response


def create_service(api_instance, body, namespace):
    api_response = api_instance.create_namespaced_service(body=body, namespace=namespace)
    return api_response


def delete_service(api_instance, name, namespace):
    api_response = api_instance.delete_namespaced_service(name=name, namespace=namespace)
    return api_response


def read_service(api_instance, name, namespace, pretty="true"):
    api_response = api_instance.read_namespaced_service(name=name, namespace=namespace, pretty=pretty)
    return api_response


def replace_service(api_instance, name, namespace, body, pretty="true"):
    api_response = api_instance.replace_namespaced_service(name, namespace, body, pretty=pretty)
    return api_response


def create_configmap(api_instance, body, namespace):
    api_response = api_instance.create_namespaced_config_map(body=body, namespace=namespace)
    return api_response


def delete_configmap(api_instance, name, namespace):
    api_response = api_instance.delete_namespaced_config_map(name=name, namespace=namespace)
    return api_response


def read_deployment(service, namespace):
    kube_config = "/etc/.kube/cls-13r4n95i"
    config.load_kube_config(kube_config)
    api_instance = client.AppsV1Api()
    pretty = 'true'
    try:
        while True:
            api_response = api_instance.read_namespaced_deployment(name=service, namespace=namespace, pretty=pretty)
            replicas = api_response.status.replicas
            if replicas:
                if int(replicas) == 1:
                    status = 1
                elif int(replicas) == 0:
                    status = 0
                break
            else:
                time.sleep(3)
                i = 1
                if i > 5:
                    break
                continue
    except Exception:
        status = 0
    return status


def c_deployment_main(clusterid, service, tags):
    namespace = "sandbox"
    kube_config = "/etc/.kube/{}".format(clusterid)
    config.load_kube_config(kube_config)
    base_url = "/data/flow-api/devops_cicd/templates/"
    deployment_v1_api = client.AppsV1Api()
    service_v1_api = client.CoreV1Api()
    config_v1_api = client.CoreV1Api()
    DEPL_URL = base_url + "deployment/tomcat-start" if service in tomcat_start else base_url + "deployment/jar-start/"
    with open(os.path.join(DEPL_URL, 'module.yml')) as f:
        deployment_body = f.read().replace("{ namespace }", namespace).replace("{ service }", service).replace(
            "{ tags }", tags)
        deployment_body = yaml.safe_load(deployment_body)

    with open(os.path.join(base_url, 'services/module.yml')) as f:
        service_body = f.read().replace("{ namespace }", namespace).replace("{ service }", service)
        service_body = yaml.safe_load(service_body)

    # 获取状态
    status = read_deployment(service, namespace)
    if status == 0:
        print("{} is not found \n start create {}...".format(service, service))
        try:
            create_deployment(deployment_v1_api, body=deployment_body, namespace=namespace)
            create_service(service_v1_api, body=service_body, namespace=namespace)
        except Exception as e:
            print(e)
    else:
        print("{} is already exists \n stop create {}".format(service, service))

    if service == "identification":
        with open(os.path.join(base_url, 'configmap/wego3.0.yml')) as f:
            configmap_body = f.read().replace("{ namespace }", namespace)
            configmap_body = yaml.safe_load(configmap_body)
            create_configmap(config_v1_api, body=configmap_body, namespace=namespace)

    # 获取deployment状态：
    status = read_deployment(service, namespace)
    if status == 1:
        print("{} create deployment/services is successfully".format(service))
    return status


def delete_main(clusterid, service):
    # 删除deployment, service
    global status
    namespace = "sandbox"
    kube_config = "/etc/.kube/{}".format(clusterid)
    config.load_kube_config(kube_config)
    deployment_v1_api = client.AppsV1Api()
    service_v1_api = client.CoreV1Api()
    config_v1_api = client.CoreV1Api()

    try:
        print("{} is delete....".format(service))
        deploy_status = delete_deployment(deployment_v1_api, name=service, namespace=namespace)
        service_status = delete_service(service_v1_api, name=service, namespace=namespace)
        if deploy_status.status == "Success" and service_status.status == "Success":
            status = "success"
            # print("{} is delete successfully".format(service))
    except Exception as e:
        print(e)

    # 判断是否为identification 然后删除deployment
    if service == "identification":
        config_map = delete_configmap(config_v1_api, name=service, namespace=namespace)
        if config_map["status"] == "Success":
            status = "success"

    # 检查是否还存在
    return status


def delete_cluster_namespace(clusterid, namespace):
    global status
    kube_config = "/etc/.kube/{}".format(clusterid)
    config.load_kube_config(kube_config)  # 加载配置文件连接
    namespace_v1_api = client.CoreV1Api  # 确定api接口名称
    response = namespace_v1_api.delete_namespace(name=namespace)


class KubernetesBase:
    def __init__(self, kube_config_file):
        config.load_kube_config(kube_config_file)  # 加载配置文件连接
        self.core_v1_instance = client.CoreV1Api()  # 确定api接口名称
        self.apps_v1_instance = client.AppsV1Api()
        self.batch_api = client.BatchApi()
