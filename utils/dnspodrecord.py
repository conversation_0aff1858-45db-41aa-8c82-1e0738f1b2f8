# -*- coding: utf-8 -*-
"""
@Title:     dnspod_record.py
@Date:      2022/8/31 11:40
@Author:    WangLH
@Desc：     DNSPod 记录相关接口
"""

import json
from tencentcloud.common import credential
from tencentcloud.common.exception.tencent_cloud_sdk_exception import TencentCloudSDKException
from tencentcloud.common.profile.client_profile import ClientProfile
from tencentcloud.common.profile.http_profile import HttpProfile
from tencentcloud.dnspod.v20210323 import dnspod_client, models


class DnspodRecord:
    def __init__(self, secretId, secretKey):
        self.record_params = ["RecordId", "Value", "Status", "Name", "Type", "MX", "Line"]
        cred = credential.Credential(secretId, secretKey)
        httpProfile = HttpProfile()
        httpProfile.endpoint = "dnspod.tencentcloudapi.com"
        clientProfile = ClientProfile()
        clientProfile.httpProfile = httpProfile
        self.client = dnspod_client.DnspodClient(cred, "", clientProfile)

    def record_list(self, **kwargs):
        """
        获取域名解析记录列表，排除NS记录
        :param: Domain str 域名
        :param: Limit int 最大3000
        :return:
        Status str: 记录状态，启用：ENABLE，暂停：DISABLE
        Type str: 记录类型
        """
        global record
        try:
            req = models.DescribeRecordListRequest()
            params = {"Limit": 3000, **kwargs}
            req.from_json_string(json.dumps(params))
            resp = self.client.DescribeRecordList(req)
            recordList = resp.RecordList
            data = []
            for line in recordList:
                if line.Type != "NS":
                    record = {params: json.loads(line.to_json_string())[params] for params in self.record_params}
                    data.append(record)
            return data
        except TencentCloudSDKException as err:
            return err

    def record_add(self, **kwargs):
        """
        添加记录
        :param **kwargs
            for Example:
            kwargs = {"Domain":"szwego.live","SubDomain": "test", "RecordType": "A", "RecordLine": "默认", "Value": '*******'}
                RecordType True 记录类型 比如：IP，CNAME
                RecordLine  True 记录线路 中文，比如：默认
                Value True 记录值 比如：IP：1.1.1.
                mx {1-20} True  MX 优先级 MX记录必填
                Status 记录初始状态，取值 ENABLE DISABLE
                SubDomain 主机记录，默认为@
            具体详情：https://console.cloud.tencent.com/api/explorer?Product=dnspod&Version=2021-03-23&Action=CreateRecord&SignVersion=
        """
        try:
            req = models.CreateRecordRequest()
            params = {**kwargs}
            req.from_json_string(json.dumps(params))
            resp = self.client.CreateRecord(req)
            return {"RecordId": resp.RecordId}
        except TencentCloudSDKException as err:
            return {"error": str(err)}

    def record_modify(self, **kwargs):
        """
        修改记录
        :param **kwargs
            for Example:
            kwargs = {"Domain":"szwego.live","RecordId":1190437757, "SubDomain": "test", "RecordType": "A", "RecordLine": "默认", "Value": '*******'}
                Domain str 域名ID或者域名
                RecordType str 记录类型 比如：IP，CNAME
                RecordLine str 记录线路 中文，比如：默认
                Value str 记录值 比如：IP：1.1.1.
                mx {1-20} int  MX 优先级 MX记录必填
                Status str 记录初始状态，取值 ENABLE DISABLE
                RecordId int 记录ID
                SubDomain str 主机记录，默认为@
            具体详情：https://console.cloud.tencent.com/api/explorer?Product=dnspod&Version=2021-03-23&Action=ModifyRecord&SignVersion=
        """
        try:
            req = models.ModifyRecordRequest()
            params = {**kwargs}
            req.from_json_string(json.dumps(params))
            resp = self.client.ModifyRecord(req)
            return {"RecordId": resp.RecordId}
        except TencentCloudSDKException as err:
            return {"error": str(err)}

    def record_delete(self, **kwargs):
        """
        删除记录
        :param **kwargs
            for Example:
            kwargs = {"Domain":"szwego.live","RecordId":1190437757}
                Domain str 域名ID或者域名
                RecordId int 记录ID
            具体详情：https://console.cloud.tencent.com/api/explorer?Product=dnspod&Version=2021-03-23&Action=DeleteRecord&SignVersion=
        """
        try:
            req = models.DeleteRecordRequest()
            params = {**kwargs}
            req.from_json_string(json.dumps(params))
            resp = self.client.DeleteRecord(req)
            return {"RecordId": kwargs["RecordId"], "msg": f"{kwargs['RecordId']} 删除完成"}
        except TencentCloudSDKException as err:
            return {"error": str(err)}
