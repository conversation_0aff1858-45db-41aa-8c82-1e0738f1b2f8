#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
@Title:   variable.py
@Date:    2022/2/26 10:10
@Author:  Wangl<PERSON>
@Desc:    变量维护
"""

tomcat_start = ["acs", "album", "ccs", "bcs", "pts"]
test_project_name = {
    "测试环境-微购3.0服务流水线": "adapter,identification,shop,increase,check,ai,commoditysearch,xwayshare,marketing,statistics,flygeesecenter,flygeeseadmin,xwaypush",
    "测试环境-微购3.0服务流水线-B": "commodity,bifrost"
}
tencent_secret = {
    "secretId": "AKID8C1asCZd8QTkkVdUbS88tPq9CyUk4A0S",
    "secretKey": "O15uV7YlhMHyxkfLCoAIEuNr3zeFqBF2"
}

kube_config_dict = {
    'dev': 'cls-13r4n95i',
    'test': 'cls-13r4n95i',
    'pre': 'cls-4a066ctu',
    'prod3': 'cls-0cdjzonk',
    'prod-a': 'cls-4a066ctu',
    'prod-b': 'cls-g0qj4t9a',
    'dragine': 'cls-cvablnss',
    'pretest': 'cls-nwrqaouy'
}

kube_config_file = "/etc/.kube/%(cluster_id)s"
# kube_config_file = "F:/home/<USER>/gitee-oneself/devops-cicd/.kube/%(cluster_id)s"
devops_group = ["dongxin", "wanglinhao", "wuxuan", "lijiana"]
