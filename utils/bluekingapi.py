#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
@Title:   bluekingapi.py
@Date:    2022/4/13 9:58
@Author:  wanglh
@Desc:    蓝鲸api接口的调用
"""
import copy
import json

import requests
from flask import current_app


class BlueKings:
    def __init__(self):
        self.bk_global = {"bk_app_code": 'wegoops',
                          "bk_app_secret": '1186e36a-b156-42f0-8724-e6afd64fdc59',
                          "bk_username": 'admin',
                          "bk_biz_id": 3,
                          "global_vars": [
                              {  # 全局变量信息
                                  "id": 1000096,
                                  "target_server": {  # 目标服务器
                                      "ip_list": [{  # 主机IP列表
                                          "bk_cloud_id": 0,  # 云区域ID
                                          "ip": "**********"  # 主机IP
                                      }]
                                  }},
                          ]}
        self.bk_global_v3 = {"bk_app_code": 'wegoops',
                             "bk_app_secret": '1186e36a-b156-42f0-8724-e6afd64fdc59',
                             "bk_username": 'admin',
                             "bk_biz_id": 3,
                             "global_vars": [
                                 {  # 全局变量信息
                                     "id": 1000096,
                                     "server": {  # 目标服务器
                                         "ip_list": [{  # 主机IP列表
                                             "bk_cloud_id": 0,  # 云区域ID
                                             "ip": "**********"  # 主机IP
                                         }]
                                     }},
                             ]}
        # v3接口的job_id参数为：job_plan_id
        self.blue_domain = "https://paas.in.szwego.com"

    def get_job_list(self):
        kwargs = copy.deepcopy(self.bk_global)
        values = {
            "start": 0,
            "length": 100
        }
        kwargs = dict(kwargs, **values)
        url = self.blue_domain + '/api/c/compapi/v2/jobv3/get_job_plan_list/'
        res = requests.post(url, data=json.dumps(kwargs))
        result = res.json()['data']
        return result

    # 获取job任务的详情
    def getjobinfo(self, jobid):
        url = self.blue_domain + "/api/c/compapi/v2/job/get_job_detail/"
        kwargs = copy.deepcopy(self.bk_global)
        kwargs["bk_job_id"] = jobid
        rs = requests.post(url, data=json.dumps(kwargs))
        global_vars = rs.json()["data"]["global_vars"]
        return global_vars

    # 执行任务
    def execute_job(self, jobid, values=None):
        """
        执行蓝鲸任务
        :param jobid: 执行任务ID
        :param values: 任务的global_vars参数
        :return:
        """

        kwargs = copy.deepcopy(self.bk_global)
        kwargs["bk_job_id"] = jobid
        if values:
            kwargs['global_vars'].extend(values)
        url = self.blue_domain + '/api/c/compapi/v2/job/execute_job/'
        current_app.logger.info(f"蓝鲸参数：{kwargs}")
        res = requests.post(url, data=json.dumps(kwargs))
        return res

    def execute_job_v3(self, jobid, values=""):
        kwargs = copy.deepcopy(self.bk_global_v3)
        kwargs["job_plan_id"] = jobid
        if values != "":
            kwargs['global_vars'].extend(values)
        url = self.blue_domain + '/api/c/compapi/v2/jobv3/execute_job_plan/'
        res = requests.post(url, data=json.dumps(kwargs))
        return res

    # 获取执行状态
    def get_job_status(self, jobid):
        url_status = self.bk_domain + "/api/c/compapi/v2/jobv3/get_job_instance_status/"
        kwargs = copy.deepcopy(self.bk_global)
        kwargs["job_instance_id"] = jobid
        response = requests.post(url_status, data=json.dumps(kwargs))
        result = response.json()["data"]["finished"]
        return result, response


# 执行任务
# 根据调用的ID 定义函数名字

def execute_job_1000152(namespace, ops):
    bk = BlueKings()
    job_globalvars_info = bk.getjobinfo(jobid=1000152)
    for i in job_globalvars_info:
        if i["name"] == "ns":
            namespace_id = int(i["id"])
        if i["name"] == "ops":
            ops_id = int(i["id"])
        # if i["name"] == "env":
        #     env_id = int(i["id"])
    try:
        current_app.logger.info("蓝鲸任务：1000152调用中，蓝鲸任务清单请查看地址：/blueking/api/v1/index")
        values = [
            {
                "id": namespace_id,
                "value": namespace  # 变量：namespace
            },
            {
                "id": ops_id,
                "value": ops  # 变量ops
            },
            # {
            #     "id": env_id,
            #     "value": env
            # }
        ]
        res = bk.execute_job(jobid=1000152, values=values)
        return res.json()
    except Exception as e:
        current_app.logger.error(f"蓝鲸任务：1000152调用失败，失败信息：{e}")
        return str(e)


def execute_job_1000154(services, branch, deploy_env, sequence, username=""):
    bk = BlueKings()
    job_globalvars_info = bk.getjobinfo(jobid=1000154)

    try:
        values = []
        for i in job_globalvars_info:
            if i["name"] == "services":
                values.append({"id": int(i["id"]), "value": services})
            if i["name"] == "branch":
                values.append({"id": int(i["id"]), "value": branch})
            if i["name"] == "deploy_env":
                values.append({"id": int(i["id"]), "value": deploy_env})
            if i["name"] == "sequence_id":
                values.append({"id": int(i["id"]), "value": sequence})
            if i["name"] == "username":
                values.append({"id": int(i["id"]), "value": username})
        current_app.logger.info(f"蓝鲸任务：1000154调用中，values: {values}")
        res = bk.execute_job(jobid=1000154, values=values)
        return res.json()
    except Exception as e:
        current_app.logger.error(f"蓝鲸任务：1000154调用失败，失败信息：{e}")
        return str(e)


def exectue_job_1000142(name, groupname):
    bk = BlueKings()
    job_globalvars_info = bk.getjobinfo(jobid=1000142)
    for i in job_globalvars_info:
        if i["name"] == "name":
            nameid = i["id"]
        if i["name"] == "groupname":
            groupid = i["id"]
    try:
        values = [
            {
                "id": nameid,
                "value": name
            },
            {
                "id": groupid,
                "value": groupname
            }
        ]
        current_app.logger.info("蓝鲸任务：1000142调用中，蓝鲸任务清单请查看地址：/blueking/api/v1/index")
        res = bk.execute_job(jobid=1000142, values=values)
        return res.json()
    except Exception as e:
        current_app.logger.error(f"蓝鲸任务：1000142调用失败，失败信息：{e}")
        return str(e)


def execute_job_1000169(**pargs):
    service = pargs['service']
    env = pargs['env']
    username = pargs['username']
    bk = BlueKings()
    bk.bk_global = {"bk_app_code": 'wegoops',
                    "bk_app_secret": '1186e36a-b156-42f0-8724-e6afd64fdc59',
                    "bk_username": 'admin',
                    "bk_biz_id": 3,
                    "global_vars": [
                        {  # 全局变量信息
                            "id": 1000096,
                            "target_server": {  # 目标服务器
                                "ip_list": [{  # 主机IP列表
                                    "bk_cloud_id": 0,  # 云区域ID
                                    "ip": "*********"  # 主机IP
                                }]
                            }},
                    ]}
    job_globalvars_info = bk.getjobinfo(jobid=1000169)
    for i in job_globalvars_info:
        if i['name'] == "service":
            serviceid = i['id']
        if i['name'] == 'env':
            envid = i['id']
        if i["name"] == "username":
            usernameid = i['id']
    try:
        values = [
            {
                "id": serviceid,
                "value": service
            },
            {
                "id": envid,
                "value": env
            },
            {
                "id": usernameid,
                "value": username
            }
        ]
        current_app.logger.info("蓝鲸任务：1000169调用中，蓝鲸任务清单请查看地址：/blueking/api/v1/index")
        res = bk.execute_job(jobid=1000169, values=values)
        return res.json()
    except Exception as e:
        current_app.logger.error(f"蓝鲸任务：1000169调用失败，失败信息：{e}")
        return str(e)


def execute_job_1000182(**kwargs):
    bk = BlueKings()
    bk.bk_global = {"bk_app_code": 'wegoops',
                    "bk_app_secret": '1186e36a-b156-42f0-8724-e6afd64fdc59',
                    "bk_username": 'admin',
                    "bk_biz_id": 3,
                    "global_vars": [
                        {  # 全局变量信息
                            "id": 1000096,
                            "target_server": {  # 目标服务器
                                "ip_list": [{  # 主机IP列表
                                    "bk_cloud_id": 0,  # 云区域ID
                                    "ip": "*********"  # 主机IP
                                }]
                            }},
                    ]}
    job_globalvars_info = bk.getjobinfo(jobid=1000182)
    try:
        values = [{"id": line['id'], "value": kwargs[line["name"]]} for line in job_globalvars_info]
        current_app.logger.info("蓝鲸任务：1000182调用中，蓝鲸任务清单请查看地址：/blueking/api/v1/index")
        res = bk.execute_job(jobid=1000182, values=values)
        return res.json()
    except Exception as e:
        current_app.logger.error(f"蓝鲸任务：1000182调用失败，失败信息：{e}")
        return str(e)


def execute_job_1000183(**pargs):
    """
    调用蓝鲸作业：1000183
    :param pargs:
    :pargs username:
    :pargs create_by:
    :pargs email:
    :return:
    """
    bk = BlueKings()
    bk.bk_global = {"bk_app_code": 'wegoops',
                    "bk_app_secret": '1186e36a-b156-42f0-8724-e6afd64fdc59',
                    "bk_username": 'admin',
                    "bk_biz_id": 3,
                    "global_vars": [
                        {  # 全局变量信息
                            "id": 1000096,
                            "target_server": {  # 目标服务器
                                "ip_list": [{  # 主机IP列表
                                    "bk_cloud_id": 0,  # 云区域ID
                                    "ip": "***********"  # 主机IP
                                }]
                            }},
                    ]}
    job_globalvars_info = bk.getjobinfo(jobid=1000183)
    try:
        values = [{"id": line['id'], "value": pargs[line["name"]]} for line in job_globalvars_info]
        current_app.logger.info("蓝鲸任务：1000131调用中，蓝鲸任务清单请查看地址/blueking/api/v1/index")
        res = bk.execute_job(jobid=1000183, values=values)
        return res.json()
    except Exception as e:
        current_app.logger.error(f"蓝鲸任务：1000183调用失败，失败信息：{e}")
        return str(e)


def execute_job_1000131(**pargs):
    """
    调用蓝鲸作业：1000131
    :param pargs:
    :return:
    """
    bk = BlueKings()
    try:
        current_app.logger.info("蓝鲸任务：1000131调用中，蓝鲸任务清单请查看地址/blueking/api/v1/index")
        res = bk.execute_job(jobid=1000131)
        return res.json()
    except Exception as e:
        current_app.logger.error(f"蓝鲸任务：1000131调用失败，失败信息：{e}")
        return str(e)


def execute_job_1000205() -> str:
    """
    半弹窗服务发布
    Return:
        job_id (str): 任务执行ID
    """
    bk = BlueKings()
    bk_kwargs = copy.deepcopy(bk.bk_global)
    bk_kwargs["bk_job_id"] = 1000205
    values = [
        {
            'id': 1000196,  # 部署环境
            'value': 'deploy'},
    ]
    bk_kwargs["global_vars"].extend(values)
    job_id = bk.execute_job(bk_kwargs)
    return job_id


def execute_job_1000140(**kwargs) -> str:
    """
    后端服务发布
    Args:
        deployment_env (str)：部署环境
        backend (str): 后端服务
    Return:
        job_id (str): 任务执行ID
    """
    bk = BlueKings()
    bk_kwargs = copy.deepcopy(bk.bk_global)
    bk_kwargs['bk_job_id'] = 1000140
    values = [
        {
            'id': 1000151,  # 部署环境
            'value': kwargs.get('deployment_env', 'B')},
        {
            'id': 1000152,  # 部署应用名
            'value': kwargs.get('backend', 'acs')}
    ]
    bk_kwargs["global_vars"].extend(values)
    job_id = bk.execute_job(bk_kwargs)
    return job_id


def execute_job_1000077(**kwargs) -> str:
    """
    前端服务发布
    Args:
        deployment_env (str): 部署环境
        change_url (str): 变更URL
    Return:
        job_id (str): 任务执行ID
    """
    bk = BlueKings()
    bk_kwargs = copy.deepcopy(bk.bk_global)
    bk_kwargs['bk_job_id'] = 1000077
    values = [
        {
            "id": 1000091,  # 发布环境(A/B)选择
            "value": kwargs.get('deployment_env', 'B')
        },
        {
            "id": 1000112,  # 版本发布链接
            "value": kwargs.get('change_url')
        }
    ]
    bk_kwargs["global_vars"].extend(values)
    job_id = bk.execute_job(bk_kwargs)
    return job_id


# Apollo Nacos 预发布配置同步
def execute_job_1000210() -> str:
    bk = BlueKings()
    bk.bk_global['global_vars'] = [
        {  # 全局变量信息
            "id": 1000096,
            "target_server": {  # 目标服务器
                "ip_list": [{  # 主机IP列表
                    "bk_cloud_id": 0,  # 云区域ID
                    "ip": "***********"  # 主机IP
                }]
            }},
    ]
    res = bk.execute_job(jobid=1000210)
    return res.json()


def execute_job_1000215(**pargs):
    """
    save exclude
    :param kwargs:
    :return:
    """
    bk = BlueKings()
    bk.bk_global['global_vars'] = [
        {  # 全局变量信息
            "id": 1000096,
            "target_server": {  # 目标服务器
                "ip_list": [{  # 主机IP列表
                    "bk_cloud_id": 0,  # 云区域ID
                    "ip": "***********"  # 主机IP
                }]
            }},
    ]
    job_globalvars_info = bk.getjobinfo(jobid=1000215)
    try:
        values = [{"id": line['id'], "value": pargs[line["name"]]} for line in job_globalvars_info]
        current_app.logger.info("蓝鲸任务：1000215调用中，蓝鲸任务清单请查看地址/blueking/api/v1/index")
        res = bk.execute_job(jobid=1000215, values=values)
        return res.json()
    except Exception as e:
        current_app.logger.error(f"蓝鲸任务：1000183调用失败，失败信息：{e}")
        return str(e)


def execute_job_1000221(**pargs):
    bk = BlueKings()
    bk.bk_global['global_vars'] = [
        {  # 全局变量信息
            "id": 1000096,
            "target_server": {  # 目标服务器
                "ip_list": [{  # 主机IP列表
                    "bk_cloud_id": 0,  # 云区域ID
                    "ip": "*********"  # 主机IP
                }]
            }
        }
    ]
    job_globalvars_info = bk.getjobinfo(jobid=1000221)
    value = [{"id": line['id'], "value": pargs["creator"]} for line in job_globalvars_info if line['name'] == 'create_user']
    current_app.logger.info(f"工单：1000221，蓝鲸参数{value}")
    res = bk.execute_job(jobid=1000221, values=value)
    return res.json()


# 二级网关 预发布环境环境与服务打包

def execute_job_1000240(**params):
    """
    预发布-环境创建
    :param params:
        namespace：pre_uuid
        ops: create or delete
    :return:
    """
    jobid = 1000240
    bk = BlueKings()
    bk.bk_global['global_vars'] = [
        {  # 全局变量信息
            "id": 1000096,
            "target_server": {  # 目标服务器
                "ip_list": [{  # 主机IP列表
                    "bk_cloud_id": 0,  # 云区域ID
                    "ip": "*********"  # 主机IP
                }]
            }
        }
    ]

    # 定义字典来映射键名到变量
    job_globalvars_info = bk.getjobinfo(jobid=jobid)

    key_map = {
        "namespace": "namespace",
        "ops": "ops"
    }
    values = []
    # 假设job_globalvars_info是一个列表，pargs是一个字典
    for i in job_globalvars_info:
        # 检查字典中是否有对应的键
        for key, var_name in key_map.items():
            if i.get("name") == key:
                values.append({"id": int(i.get("id")), "value": params.get(var_name)})
                break  # 如果找到匹配项，跳出循环

    current_app.logger.info(f"执行方案：{jobid}，蓝鲸参数{values}")
    # return f"执行方案：{jobid}，蓝鲸参数{values}"
    res = bk.execute_job(jobid=jobid, values=values)
    return res.json()


# def execute_job_1000237(services, branch, deploy_env, sequence, username=""):

def execute_job_1000237(**params):
    """
    预发布-服务打包
    :param params:
            services:
            branch:
            deploy_env:
            sequence:
            username:
    :return:
    """
    jobid = 1000237
    bk = BlueKings()
    job_globalvars_info = bk.getjobinfo(jobid=jobid)

    key_map = {
        "services": "services",
        "branch": "branch",
        "deploy_env": "deploy_env",
        "sequence_id": "sequence",
        "username": "username"
    }

    try:
        values = []
        # 假设job_globalvars_info是一个列表，pargs是一个字典
        for i in job_globalvars_info:
            # 检查字典中是否有对应的键
            for key, var_name in key_map.items():
                if i.get("name") == key:
                    values.append({"id": int(i.get("id")), "value": params.get(var_name)})
                    break  # 如果找到匹配项，跳出循环
        current_app.logger.info(f"蓝鲸任务：{jobid}调用中，values: {values}")
        res = bk.execute_job(jobid=jobid, values=values)
        return res.json()
    except Exception as e:
        current_app.logger.error(f"蓝鲸任务：{jobid}调用失败，失败信息：{e}")
        return str(e)



def execute_job_1000238(**pargs):
    bk = BlueKings()
    bk.bk_global['global_vars'] = [
        {  # 全局变量信息
            "id": 1000096,
            "target_server": {  # 目标服务器
                "ip_list": [{  # 主机IP列表
                    "bk_cloud_id": 0,  # 云区域ID
                    "ip": "***********"  # 主机IP
                }]
            }},
    ]
    job_globalvars_info = bk.getjobinfo(jobid=1000238)
    try:
        values = [{"id": line['id'], "value": pargs[line["name"]]} for line in job_globalvars_info]
        current_app.logger.info("蓝鲸任务：1000238调用中，蓝鲸任务清单请查看地址/blueking/api/v1/index")
        res = bk.execute_job(jobid=1000238, values=values)
        return res.json()
    except Exception as e:
        current_app.logger.error(f"蓝鲸任务：1000238调用中，失败信息：{e}")
        return str(e)