#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
@Title:   mysql.py
@Date:    2022/3/21 9:14
@Author:  <PERSON>lh
@Desc:    mysql类
"""

import pymysql


class MysqlAcs:
    def __init__(self, host="***********", user="loonflow", password="Ccwo17k92Zw5", db="wego_ops"):
        self.host = host
        self.user = user
        self.password = password
        self.db = db
        self.con = pymysql.connect(host=self.host, user=self.user, password=self.password, db=self.db,
                                   cursorclass=pymysql.cursors.DictCursor)
        self.cur = self.con.cursor()

    def __disconnect__(self):
        self.cur.close()
        self.con.close()

    # 写入更新数据
    def execute(self, sql):
        self.cur.execute(sql)

    def commit(self):
        self.con.commit()

    def rollback(self):
        self.con.rollback()

    def select(self, sql, type_="fetchall"):
        self.cur.execute(sql)
        if type_ == "fetchall":
            return self.cur.fetchall()
        return self.cur.fetchone()


def acs_write(sql):
    global message
    acs_mysql = MysqlAcs()
    try:
        acs_mysql.execute(sql)
        acs_mysql.commit()
        message = "SUCCESS"
    except Exception as e:
        acs_mysql.rollback()
        message = str(e)
    finally:
        acs_mysql.__disconnect__()
        return message


def acs_select_all(sql):
    """列表"""
    acs_mysql = MysqlAcs()
    acs_mysql.execute(sql)
    result = acs_mysql.cur.fetchall()
    acs_mysql.__disconnect__()
    return result


def acs_select_one(sql):
    """字典"""
    acs_mysql = MysqlAcs()
    acs_mysql.execute(sql)
    result = acs_mysql.cur.fetchone()
    acs_mysql.__disconnect__()
    return result
