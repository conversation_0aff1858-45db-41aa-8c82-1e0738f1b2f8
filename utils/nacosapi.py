#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
@Title:     nacosapi.py
@Date:      2023/9/21 11:35
@Author:    <PERSON>L<PERSON>
@Desc:
"""
from typing import Dict, List

import pymysql


def getTenantInfo() -> List[Dict]:
    """
    获取现网 nacos 3.0 Tenant Info
    :return:
        List[Dict]:
    """
    conn = pymysql.Connection(user='wg_nacos', password='U3wM9HK#0Qi5yE6w', host='***********', database='nacos',
                              port=3306, cursorclass=pymysql.cursors.DictCursor)
    sql = """select tenant_name, tenant_desc from tenant_info"""
    cur = conn.cursor()
    cur.execute(sql)
    data = cur.fetchall()
    return data
