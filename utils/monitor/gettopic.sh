#!/bin/bash
# Author: wanglh
# Update: 2024-12-12

# 在************上执行

declare -a ROCKETMQ_IPS=(
    "***********"
    "***********"
    "***********"
    "************"
    "************"
)

for ip in "${ROCKETMQ_IPS[@]}"
do
  /data/wanglh/rocketmq-all-4.7.1-bin-release/bin/mqadmin  topicList -n ${ip}:9876 -c 2>/dev/null | awk '$3!=""{print $0}' | awk '{if($2~/^[a-z]/)print}' > /data/wanglh/${ip}.log
done

echo -n 200