#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
@Title:     get_topic.py
@Date:      2023/12/11 16:19
@Author:    WangLH
@Desc:
"""
import re
import time
from pathlib import Path

from model import *
from .monitrossh import establish_ssh_connection, execute_command, upload_files


def upload_and_execute_script(hostname, username, private_key, local_script):
    """
    上传脚本
    Returns:
        list[dict] | str: 获取的 topic 列表 或 错误信息
    """
    script_name = "gettopic.sh"
    remote_script_path = f"/data/wanglh/{script_name}"
    ssh_client = establish_ssh_connection(hostname, username, private_key)
    try:
        upload_files(ssh_client, local_script, remote_script_path)
        # 执行远程命令
        if execute_remote_script(ssh_client, remote_script_path):
            return parse_log_files(ssh_client)
    except Exception as e:
        return f"Error: {str(e)}"
    finally:
        ssh_client.close()


def execute_remote_script(ssh_client, remote_script_path):
    # 执行远程命令
    # iplist = ["***********", "***********", "***********"]
    command = f"chmod +x {remote_script_path} && bash -lc \"{remote_script_path}\""
    exit_status, _ = execute_command(ssh_client, command)
    return int(exit_status) == 0


def parse_log_files(ssh_client):
    iplist = ["***********", "***********", "***********","************","************"]
    res_str = ""

    for slave in iplist:
        command = f"/usr/bin/cat /data/wanglh/{slave}.log"
        _, stdout = execute_command(ssh_client, command)
        res_str += stdout
    return extract_data_from_logs(res_str)


def extract_data_from_logs(log_str):
    lines = log_str.strip().split('\n')
    pattern = r'\s+'
    data_list = []
    cre_time = time.strftime("%Y-%m-%d %H:%M:%S", time.localtime())
    for line in lines:
        columns = re.split(pattern, line.strip())
        data_list.append(dict(name=columns[0],
                              topic=columns[1],
                              group=columns[2],
                              cre_time=cre_time
                              )
                         )
    return data_list


def main():
    script_name = "gettopic.sh"
    remote_hostname = "************"
    remote_username = "root"

    # 获取 topic 信息，写入数据库
    current_directory = Path(__file__).resolve().parent
    private_key = current_directory / '..' / '..' / 'ssh' / 'xc_root_key'
    private_key_path = private_key.resolve()
    local_script_path = os.path.join(current_directory, script_name)
    res = upload_and_execute_script(remote_hostname, remote_username, private_key_path, local_script_path)
    return res
