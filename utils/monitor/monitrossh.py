#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
@Title:     monitrossh.py
@Date:      2023/12/12 11:39
@Author:    WangLH
@Desc:      监控：ssh 客户端
"""

import time

import paramiko
from flask import current_app
from scp import SCPClient, SCPException


def establish_ssh_connection(hostname, username, private_key_path):
    """
    创建 ssh 客户端
    """
    try:
        ssh_client = paramiko.SSHClient()
        ssh_client.set_missing_host_key_policy(paramiko.AutoAddPolicy())
        private_key = paramiko.RSAKey(filename=private_key_path)
        ssh_client.connect(hostname, username=username, pkey=private_key)
        return ssh_client
    except Exception as e:
        current_app.logger.error(f"Failed to establish SSH connection: {e}")
        raise Exception(f"Failed to establish SSH connection: {e}")


def execute_command(ssh_client, command):
    """
    执行远程命令，并返回执行结果
    """
    print("Running command: ", command)
    _, stdout, stderr = ssh_client.exec_command(command)
    # 等待命令执行完昵称
    exit_status = stdout.channel.recv_exit_status()
    while not stdout.channel.exit_status_ready():
        time.sleep(1)
    stdoutstr = stdout.read().decode('utf-8')
    error = stderr.read().decode('utf-8')
    if exit_status != 0:
        raise Exception(f"Command execution failed with error: {error}")
    return exit_status, stdoutstr


def upload_files(ssh_client, local_file_path, remote_file_path):
    """
    使用SFTP协议上传本地脚本到远程服务器
    """
    try:
        print(f"Uploading {local_file_path} to {remote_file_path}")
        sftp = ssh_client.open_sftp()
        sftp.put(local_file_path, remote_file_path)
        sftp.close()
        current_app.logger.info(f"Uploaded {local_file_path} successfully.")
    except Exception as e:
        current_app.logger.error(f"Func Name: upload_files. Failed to upload {local_file_path}，具体报错信息：\n{e}")
        raise  # Re-raise the exception to handle it in the calling function


def scp_transfer_file(ssh_client, local_file_path, remote_file_path):
    """
    使用 SCP 传输文件
    """
    try:
        with SCPClient(ssh_client.get_transport()) as scp:
            scp.put(local_file_path, remote_file_path)
            current_app.logger.info(f"File {local_file_path} successfully transferred to {remote_file_path}")
    except SCPException as e:
        current_app.logger.error(f"Func Name: scp_transfer_file. Failed to transfer file due to SCP error: \n{e}")
    except Exception as e:
        current_app.logger.error(f"Func Name: scp_transfer_file. Failed to transfer file due to unexpected error: \n{e}")
