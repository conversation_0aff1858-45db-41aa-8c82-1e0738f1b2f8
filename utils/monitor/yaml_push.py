#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
@Title:     yaml_push.py
@Date:      2024/3/5 15:56
@Author:    WangLH
@Desc:      上传 yaml到指定的服务器，目前有规则文件上传
"""

import glob
import os
import stat
import time
from pathlib import Path

from flask import current_app, jsonify, request

from utils import save_to_operation_log
from utils.monitor.monitrossh import (
    establish_ssh_connection,
    execute_command,
    scp_transfer_file,
)


def kebab_to_camel_case(s):
    """
    变更为驼峰命名字符串
    """
    parts = s.split("-")
    return parts[0] + "".join(part.capitalize() for part in parts[1:])


def change_file_permission(file_path):
    """
    修改文件权限
    :param file_path:
    :return:
    """
    try:
        # 更改文件权限为 644
        os.chmod(file_path, stat.S_IRUSR | stat.S_IWUSR | stat.S_IRGRP | stat.S_IROTH)
        current_app.logger.info(f"Changed file permission for {file_path}")
    except Exception as e:
        current_app.logger.error(f"Error changing file permission for {file_path}: {e}")


def generate_rule_files(rule_file_dir, rule_file_data, rule_template):
    """
    生成规则文件
    :param rule_file_dir:
    :param rule_file_data:
    :param rule_template:
    :return:
    """
    if not os.path.exists(rule_file_dir):
        os.makedirs(rule_file_dir)
    for name, rules in rule_file_data.items():
        rule_name = os.path.join(rule_file_dir, f"{kebab_to_camel_case(name)}.yml")
        with open(rule_name, "w") as file:
            file.writelines(rule_template(name, rules))


def generate_rule_file_content(template_func, name, rules):
    """
    生成规则文件内容
    :param template_func:
    :param name:
    :param rules:
    :return:
    """
    lines = [f"groups:\n", f"- name: {name}\n", f"  rules:\n"]
    lines += template_func(rules)
    return lines


def rule_template_mq(rules):
    """
    mq 规则文件模板
    :param rules: 规则列表
    :return: 生成的规则行
    """
    methodsSend = {"1": "sendWechat", "2": "sendFeishuNew"}

    lines = []
    for item in rules:
        current_app.logger.info(f"item: {item}")
        # 构建通知方式标签
        labelList = [
            f"        {methodsSend.get(method)}: true\n"
            for method in item.get("notify_method", "").split(",")
        ]
        if "2" in item["notify_method"]:
            labelList.append(f"        business_ids: {item.get('business_group','')}\n")

        # 修改这部分逻辑
        alarm_title = item.get("alarm_title")
        if alarm_title is None or alarm_title == "None" or alarm_title == "":
            alarm_title = item.get("topic")

        lines += [
            f"    - alert: {alarm_title}\n",
            f"      expr: {item.get('alarm_expression')} > {item.get('threshold_value')}\n",
            f"      for: {item.get('for_value', '1m')}\n",
            f"      labels:\n",
            f"        alert_type: rocketmq\n",
            f"        alertStatus: true\n",
            f"        severity: warning\n",
            "".join(labelList),
            f"      annotations:\n",
            f"        description: 'Topic:{{{{$labels.topic}}}} 产生堆积, 堆积消费组：{{{{$labels.group}}}}, {{{{$value | printf \"堆积数量：%.f\" }}}}'\n",
            f"        summary: consumer lag behind\n",
        ]

    return lines


def rule_template_pp(rules):
    """
    pinpoint 规则文件模板
    :param rules:
    :return:
    """
    current_app.logger.info(f"type for rules: {type(rules)}")

    methodsSend = {"1": "sendWechat", "2": "sendFeishuNew"}

    lines = []
    for item in rules:
        labelList = [
            f"        {methodsSend.get(method)}: true\n"
            for method in item.get("notify_method", "").split(",")
        ]
        if "2" in item["notify_method"]:
            labelList.append(f"        business_ids: {item.get('business_group','')}\n")

        lines += [
            f"    - alert: {item.get('alarm_title')}\n",
            f"      expr: {item.get('alarm_expression')} >= {item.get('threshold_value')}\n",
            f"      for: 1m\n",
            f"      labels:\n",
            f"        alert_type: pinpoint\n",
            f"        alertStatus: true\n",
            f"        severity: warning\n",
            "".join(labelList),
            f"      annotations:\n",
            f"        description: '服务: {{{{$labels.appname}}}} , {{{{$labels.type}}}}告警:{{{{printf \"%.f\" $value}}}}' \n",
            f"        summary: '{{{{$labels.appname}}}}'\n",
        ]
    return lines


def push_rule_files_and_restart_services(
    rule_file_dir, remote_script_path, sftp_host, sftp_user
):
    """
    将规则文件推送到远端服务器并重启服务
    """

    # 指定 ssh 密钥文件地址
    current_directory = Path(__file__).resolve().parent
    private_key = current_directory / ".." / ".." / "ssh" / "xc_root_key"
    private_key_path = private_key.resolve()

    try:
        client = establish_ssh_connection(sftp_host, sftp_user, private_key_path)
        absolute_rule_file_dir = os.path.abspath(rule_file_dir)
        yml_files = glob.glob(os.path.join(absolute_rule_file_dir, "*.yml"))

        for local_file in yml_files:
            try:
                change_file_permission(local_file)
                scp_transfer_file(client, local_file, remote_script_path)
                current_app.logger.info(
                    f"{local_file} transferred successfully to {remote_script_path}."
                )
            except Exception as e:
                current_app.logger.error(f"Error transferring {local_file}: {e}")
                return jsonify({"code": 500, "msg": str(e)}), 500
        current_app.logger.info(
            f"ssh_host: {sftp_host}, 重启Prometheus和Alertmanager服务...."
        )
        # 重启Prometheus和Alertmanager服务
        prometheus_status, _ = execute_command(
            client, "systemctl restart prometheus.service"
        )
        time.sleep(2)
        alert_manager_status, _ = execute_command(
            client, "systemctl restart alertmanager.service"
        )

        if prometheus_status == alert_manager_status == 0:
            return jsonify({"code": 200, "msg": "【规则文件】更新成功"}), 200
        return (
            jsonify(
                {
                    "code": 200,
                    "msg": f"AlertManager更新结果：{alert_manager_status} Prometheus更新结果: {prometheus_status}",
                }
            ),
            200,
        )
    except Exception as e:
        return jsonify({"code": 500, "msg": str(e)}), 500
    finally:
        client.close()


def push_rule_file(rule_file, rule_type):
    """
    推送规则文件
    :param rule_file: dict 规则信息 dict
    :param rule_type: 文件类型：pp 或者 mq 等
    :return:
    """
    if not rule_file:
        return jsonify({"code": 404, "msg": "rule_files is None"}), 404
    try:
        rule_file_dir = f"{rule_type}_rule"
        if not os.path.exists(rule_file_dir):
            os.makedirs(rule_file_dir)

        template_func = rule_template_mq if rule_type == "mq" else rule_template_pp
        for name, rules in rule_file.items():
            rule_file_name = os.path.join(
                rule_file_dir, f"{kebab_to_camel_case(name)}.yml"
            )
            with open(rule_file_name, "w") as file:
                content = generate_rule_file_content(template_func, name, rules)
                file.writelines(content)
        current_app.logger.info(f"rule_type: {rule_type}, 配置文件写入本地成功")
        save_to_operation_log(request, f"{rule_type} 配置文件写入本地", result=200)
        # return jsonify({"code": 200, "msg": "配置文件写入本地成功"}), 200

        # SSH连接、文件权限更改、文件传输以及远程命令执行的具体逻辑
        ssh_host = "************" if rule_type == "pp" else "***********"
        remote_script_path = f"/data/prometheus/alertmanager/rule/" if rule_type == "pp" else f"/data/prometheus-2.19.2.linux-amd64/rules/"
        save_to_operation_log(request, f"{rule_type} 规则文件上传", result=200)
        return push_rule_files_and_restart_services(rule_file_dir, remote_script_path, ssh_host, 'root')
    except Exception as e:
        save_to_operation_log(request, f"{rule_type} 规则文件上传", result=500)
        return jsonify({"code": 500, "msg": str(e)}), 500
