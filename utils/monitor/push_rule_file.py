#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
@Title:     sshclient.py
@Date:      2023/12/5 15:41
@Author:    WangL<PERSON>
@Desc:      Execute command on remote host and save mysql
"""
import glob
import os
import stat
import time
from pathlib import Path

from flask import jsonify, current_app

from utils.monitor.monitrossh import establish_ssh_connection, execute_command, scp_transfer_file


def kebab_to_camel_case(s):
    """
    变更为驼峰命名
    """
    parts = s.split('-')
    return parts[0] + ''.join(part.capitalize() for part in parts[1:])


def change_file_permission(file_path):
    """
    修改文件权限
    :param file_path:
    :return:
    """
    try:
        # 更改文件权限为 644
        os.chmod(file_path, stat.S_IRUSR | stat.S_IWUSR | stat.S_IRGRP | stat.S_IROTH)
        current_app.logger.info(f"Changed file permission for {file_path}")
    except Exception as e:
        current_app.logger.error(f"Func Name: change_file_permission. Error changing file permission for {file_path}: \n{e}")


def push_mq_rule_file(rule_file: dict):
    if rule_file is not None:
        try:
            rule_file_dir = "mq_rule"
            if not os.path.exists(rule_file_dir):
                os.makedirs(rule_file_dir)
            for name, rules in rule_file.items():
                rule_name = os.path.join(rule_file_dir, f"{kebab_to_camel_case(name)}.yml")
                with open(rule_name, 'w') as file:
                    file.write(f"groups:\n")
                    file.write(f"  - name: {name}\n")
                    file.write(f"    rules:\n")
                    for rule in rules:
                        expr, threshold_value, for_value, topic = rule
                        file.write(f"      - alert: {topic}\n")
                        file.write(f"        expr: {expr} > {threshold_value}\n")
                        file.write(f"        for: {for_value}\n")
                        file.write(f"        labels:\n")
                        file.write(f"          severity: warning\n")
                        file.write(f"        annotations:\n")
                        file.write(f"          description: 'Topic:{{{{$labels.topic}}}} 产生堆积, 堆积消费组：{{{{$labels.group}}}}, {{{{$value | printf \"堆积数量：%.f\" }}}}'\n")
                        file.write(f"          summary: consumer lag behind\n")

            # 开启ssh连接，并push文件
            sftp_host = '***********'
            sftp_user = 'root'
            # 获取 topic 信息，写入数据库
            current_directory = Path(__file__).resolve().parent
            private_key = current_directory / '..' / '..' / 'ssh' / 'xc_root_key'
            private_key_path = private_key.resolve()
            client = establish_ssh_connection(sftp_host, sftp_user, private_key_path)
            absolute_rulefile_dir = os.path.abspath(rule_file_dir)
            remote_script_path = f"/data/prometheus-2.19.2.linux-amd64/rules/"
            yml_files = glob.glob(os.path.join(absolute_rulefile_dir, "*.yml"))
            for file in yml_files:
                try:
                    change_file_permission(file)
                    scp_transfer_file(client, file, remote_script_path)
                    current_app.logger.info(f"{file} transferred successfully.")
                except Exception as e:
                    current_app.logger.error(f"Func Name: push_mq_rule_file. Error transferring {file}: \n{e}")

            # 执行脚本
            prometheus_exit_status, _ = execute_command(client, "systemctl restart alertmanager.service")
            time.sleep(3)
            alert_exit_status, _ = execute_command(client, "systemctl restart prometheus.service")
            if alert_exit_status == prometheus_exit_status == 0:
                return jsonify({"code": 200, "msg": "【规则文件】更新成功"}), 200
            return jsonify({"code": 200, "msg": f"AlertManager更新结果：{alert_exit_status} Prometheus更新结果: {prometheus_exit_status}"}), 200
        except Exception as e:
            return jsonify({"code": 500, "msg": str(e)}), 500
        finally:
            # 关闭SSH连接
            client.close()
    return jsonify({"code": 404, "msg": "rule_files is None"}), 404


def push_pp_rule_file(rule_file: dict):
    if rule_file is not None:
        try:
            rule_file_dir = "pp_rule"
            if not os.path.exists(rule_file_dir):
                os.makedirs(rule_file_dir)
            for name, rules in rule_file.items():
                rule_name = os.path.join(rule_file_dir, f"{kebab_to_camel_case(name)}.yml")
                with open(rule_name, 'w') as file:
                    file.write(f"groups:\n")
                    file.write(f"- name: {name}\n")
                    file.write(f"  rules:\n")
                    for rule in rules:
                        name, alert, expr, threshold_value, type = rule
                        file.write(f"    - alert: {alert}\n")
                        file.write(f"      expr: {expr} > {threshold_value}\n")
                        file.write(f"      for: 2m\n")
                        file.write(f"      labels:\n")
                        file.write(f"        alert_status: true\n")
                        file.write(f"        severity: warning\n")
                        file.write(f"      annotations:\n")
                        file.write(f"        description: '服务: {{{{$labels.appname}}}} , {{{{$value | printf \"$labels.type告警：%.f\" }}}}'\n")
                        file.write(f"        summary: '{{{{$labels.appname}}}}'\n")

            # 获取 topic 信息，写入数据库
            current_directory = Path(__file__).resolve().parent
            private_key = current_directory / '..' / '..' / 'ssh' / 'xc_root_key'
            private_key_path = private_key.resolve()
            absolute_rulefile_dir = os.path.abspath(rule_file_dir)
            remote_script_path = f"/data/prometheus/alertmanager/rule/"
            yml_files = glob.glob(os.path.join(absolute_rulefile_dir, "*.yml"))
            # 开启ssh连接，并push文件
            sftp_host = '************'
            sftp_user = 'root'
            client = establish_ssh_connection(sftp_host, sftp_user, private_key_path)

            for file in yml_files:
                try:
                    change_file_permission(file)
                    scp_transfer_file(client, file, remote_script_path)
                    current_app.logger.info(f"{file} transferred successfully.")
                except Exception as e:
                    current_app.logger.error(f"Func Name: push_mq_rule_file. Error transferring {file}: \n{e}")
            return jsonify({"code": 200, "msg": f"测试脚本， 文件列表： {yml_files}，远程路径：{remote_script_path}"})
            # 执行脚本
            prometheus_exit_status, _ = execute_command(client, "systemctl restart alertmanager.service")
            time.sleep(3)
            alert_exit_status, _ = execute_command(client, "systemctl restart prometheus.service")
            if alert_exit_status == prometheus_exit_status == 0:
                return jsonify({"code": 200, "msg": "【规则文件】更新成功"}), 200
            return jsonify({"code": 200, "msg": f"AlertManager更新结果：{alert_exit_status} Prometheus更新结果: {prometheus_exit_status}"}), 200
        except Exception as e:
            return jsonify({"code": 500, "msg": str(e)}), 500
        finally:
            # 关闭SSH连接
            client.close()
    return jsonify({"code": 404, "msg": "rule_files is None"}), 404
