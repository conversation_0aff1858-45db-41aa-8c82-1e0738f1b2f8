#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
@Title:     const.py
@Date:      2023/3/9 11:52
@Author:    wanglh
@Desc：     定义常量
"""

NGINX_TOKEN = "b11rctHtZzZfhCqbkb33avn"
NGINX_CMDB = {
    "10.20.0.241": "A-Album",
    #    "10.10.0.43":"B-Album",
    "10.20.0.252": "A-ACS",
    "10.10.0.136": "B-ACS",
    "10.20.0.243": "A-Order",
    #    "10.10.0.87":"B-Order",
    "10.20.1.19": "A-BCS",
    #    "10.10.0.15":"B-BCS",
    "10.20.0.249": "A-PTS",
    #    "10.10.0.148":"B-PTS",
    "************": "A-varnish",
    #    "***********":"B-varnish",
    "**********": "Offline",
    "***********": "A-Check",
    "***********": "B-ACS",
    "***********": "B-Album",
    "***********": "B-BCS",
    "***********": "B-Order",
    "************": "B-Varnish",
    "***********3": "B-PTS",
    "***********": "A-APISix",
    "************": "B-APISix",
    "***********": "A-Das",
    "************": "B-Das",
    "***********": "A-Xway",
    "***********": "B-Xway",
    "***********": "A-Script",
    "***********": "B-Xway",
    "***********": "A-bifrost",
    "************": "B-bifrost",
}
NGINX_NGCMDB = {
    "ng1": "***********",
    "ng2": "***********",
    "ng3": "***********",
    "ng4": "***********",
    "ng5": "***********",
    "ng6": "***********",
    "ng8": "***********",
    "ng9": "***********",
    "ng10": "***********",
    "ng11": "***********",
    "ng12": "***********",
    "ng13": "***********",
    "ng14": "***********",
    "ng15": "***********",
    "ng16": "***********",
    "ng17": "***********",
    "ng18": "***********",
    "ng19": "***********",
    "ng20": "***********",
    "ngtest": "***********"}
NGINX_CLUSTER = [
    "***********", "***********", "***********", "***********", "***********", "***********", "***********", "***********", "***********",
    "***********", "***********", "***********", "***********", "***********", "***********", "***********", "***********", "***********", "***********", "***********"
]
NGINX_STATS = {
    "renderer": "global",
    "name": "Wegooooo",
    "nodes": [
        {
            "renderer": "region",
            "name": "INTERNET",
            "class": "normal"
        },
        {
            "renderer": "region",
            "name": "nginx-cluster",
            "maxVolume": 50000,
            "class": "normal",
            "updated": 1466838546805,
            "nodes": [],
            "connections": [
                {
                    "source": "INTERNET",
                    "target": "***********",
                    "metrics": {
                        "normal": 1500
                    },
                    "class": "normal"
                }
            ]
        }
    ],
    "connections": [
        {
            "source": "INTERNET",
            "target": "nginx-cluster",
            "metrics": {
                "normal": 26037.626
            },
            "notices": [
            ],
            "class": "normal"
        }
    ]
}
