#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
@Title:     release.py
@Date:      2023/6/15 16:00
@Author:    wanglh
@Desc：     版本更新后端函数
"""

# 现网发布
import json
import time
from collections import defaultdict
from datetime import datetime
from typing import Dict, List

import requests
from flask import current_app, jsonify
from sqlalchemy import or_

from extension import db
from model import CiProjects, CiUpgradeSchedule, NoticeslistModel
from utils import BlueKings, bot_msg, execute_job_1000077, execute_job_1000140, execute_job_1000205


def feishu_send_release(template_variable: dict, name: str) -> jsonify:
    LOCAL_HOST = current_app.config.get('LOCAL_HOST')
    LOCAL_PORT = current_app.config.get('LOCAL_PORT')
    url = f'http://{LOCAL_HOST}:{LOCAL_PORT}/feishu/api/v1/message/send/release'
    payload = {
        "name": name,
        "template_variable": template_variable
    }
    response = requests.request('POST', url, data=json.dumps(payload), headers={'Content-Type': 'application/json'})
    return response


def get_projects_by_services(services: str) -> Dict[str, Dict[str, str]]:
    """
    获取services并分组
    :return:
    """

    services_list = [s.strip() for s in services.split(',')]

    # 查询 CiProjects 表
    task_filter = {
        or_(
            CiProjects.appname.in_(services_list),
            CiProjects.alianame.in_(services_list),
            CiProjects.project.in_(services_list),
        )
    }
    projects = CiProjects.query.filter(*task_filter).all()

    # type 字段分类，组装字典
    projects_dict = defaultdict(list)
    for project in projects:
        type_ = project.type
        service = project.appname
        if service not in projects_dict[type_]:
            projects_dict[type_].append(service)

    for type_, service_list in projects_dict.items():
        projects_dict[type_] = ','.join(service_list)
    return dict(projects_dict)


class PlayProd(BlueKings):
    def __init__(self):
        super().__init__()
        self.belonging_end = None
        self.change_version = None
        self.d_env = None
        self.change_title = None

    def go_release(self, projects_dict) -> List[str]:
        job_ids = []
        for type_, services in projects_dict.items():
            if type_ == 0:  # 前端
                job_ids.append(execute_job_1000140(**{"deployment_env": self.d_env, 'backend': services}))
            if type_ == 1:  # 后端
                job_ids.append(execute_job_1000077(**{"deployment_env": self.d_env, 'change_url': services}))
            if type_ == 3:  # 半弹窗
                job_ids.append(execute_job_1000205())
        return job_ids

    @staticmethod
    def main(self, **kwargs) -> str:
        """
        发布版本的函数。
        Args:
            self: class 属性
            **kwargs: 关键字参数，包含以下参数：
                - upgrade_id (str): 数据表变更ID。
                - d_env (str): 变更环境，可以是 A 或 B。
                - type (str): 代码的类型，可以是前端、后端或半弹窗。
                - services (str): 逗号分隔的服务名，用于指定要发布的服务。

        Returns:
            str: 该函数消息结果
        """
        self.upgrade_id = kwargs.get('upgrade_id')
        self.d_env = kwargs.get('d_env', 'B')
        schedule = CiUpgradeSchedule.query.filter_by(upgrade_id=self.upgrade_id).first()
        if not schedule:
            return f'Upgrade ID {self.upgrade_id} not found'

        # 飞书发送模板
        self.fs_template_variable = {
            "change_title": f"{schedule.version_name} <font color='green'>发布中...</font>",
            "change_env": f"{self.d_env}",
            "change_version": f"{schedule.git_branch}",
            "change_backend": f"{schedule.be_end}",
            "change_link": f"<a href='{schedule.tapd_link}'></a>",
            "change_text": f"{schedule.change_log}"
        }
        self.fs_name = ",".join([v for k, v in schedule.__dict__.items() if k in schedule.__table__.columns.keys() and 'appover' in k])
        current_app.logger.info(
            "【INFO】 即将在 {} 环境部署.....;部署服务:{}".format(self.d_env, kwargs.get('services')))
        current_app.logger.info("【INFO】当前参数:{}".format(kwargs))

        # 飞书消息发送
        try:
            feishu_send_release(self.fs_template_variable, self.fs_name)
        except Exception as e:
            current_app.logger.info(f"发送飞书消息异常:{e}")

        # 执行发布任务
        type_service_dict = get_projects_by_services(kwargs.get('services'))
        job_ids = self.go_release(type_service_dict)

        for jobid in job_ids:
            while True:
                time.sleep(5)
                result, response = self.get_job_status(jobid)
                if result:
                    status = response.json()["data"]["job_instance"]["status"]  # 任务执行结果
                    break
            if status != 3:
                message = "版本：{}，环境{}：发布失败".format(schedule.git_branch, self.d_env)
                message_content = {
                    "msgtype": "text",
                    "text": {
                        "content": message,
                        "mentioned_list": ["@all"],
                    }
                }
                try:
                    self.fs_template_variable['change_title'] = f"{schedule.version_name} <font color='red'>发布失败</font>"
                    feishu_send_release(self.fs_template_variable, self.fs_name)
                except Exception as e:
                    current_app.logger.info(f"发送飞书消息异常:{e}")
                bot_msg(message_content, "group_ops")
                return message

        # 完成通知
        try:
            self.fs_template_variable["change_title"] = f"{schedule.version_name} <font color='green'>发布完成</font>"
            feishu_send_release(self.fs_template_variable, self.fs_name)
        except Exception as e:
            current_app.logger.info(f"发送飞书消息异常: {e}")
        end_date = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        message = "{}环境：{}，发布完成!".format(self.d_env, schedule.version_name)
        data = {"info": message, "date": end_date, "level": NoticeslistModel.CHOIVE_LEVEL["FATAL"]}
        db.session.execute(NoticeslistModel.__table__.insert(), [data])
        db.session.commit()
        db.session.close()
        return message
