#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
@Title:   mythread.py
@Date:    2022/2/26 14:19
@Author:  <PERSON><PERSON><PERSON>
@Desc:    多线程函数
"""

import threading


class MyThread(threading.Thread):
    def __init__(self, func, args):
        super().__init__()
        self.result = None
        self.func = func
        self.args = args

    def run(self):
        self.result = self.func(*self.args)

    def get_result(self):
        try:
            return self.result
        except Exception:
            return None
