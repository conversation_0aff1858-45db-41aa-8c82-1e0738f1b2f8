#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
@Title:   messagepub.py
@Date:    2022/5/24 15:14
@Author:  wanglh
@Desc:    消息发送模块
"""
import json
import urllib.error
import urllib.parse
import urllib.request

import requests
from flask import current_app

# 群机器人字典
group_names = {
    'group_ops': 'ef41f3c9-ee62-4eca-b348-9e71c240663e',  # 企微：运维群机器人
    'group_bk': 'ae433d87-e905-4946-8dcf-b519b883060e',  # 企微：中后台群机器人
    'group_backage': '85c1d995-c9e5-469f-b1b0-9ad65e2db775',  # 企微：jenkins打包群
    'group_deploy': '3ac25e7d-b05d-4755-94db-5f92aef8eea2',  # 企微：版本发布群机器人
    '001_problem': 'e90b3152-575c-49fa-8cbc-6eecfa941898',  # 企微：001 现网问题内部技术沟通群
    'feishu_bk': 'd3041829-e503-4d91-8c5d-6f2fb66f66b3',  # 飞书：后端运维群
    'feishu_ops': '6f1710ce-8252-4965-a587-13a7da839a80'  # 飞书：运维群
}


def get_token(coreID, apisecret):
    """
    token 获取
    :param coreID:
    :param apisecret:
    :return:
    """
    url = 'https://oa.in.szwego.com/wework/gettoken'
    req = urllib.request.Request(url)
    result = urllib.request.urlopen(req)
    access_token = result.read()
    access_token = bytes.decode(access_token).strip("\n")
    return access_token


def bot_msg(values: dict, bot_name: str = "group_ops"):
    """
    企业微信机器人消息
    :param values:
    :param bot_name:
    :return:
    """
    # send = json.dumps(values)
    webhook = "https://qyapi.weixin.qq.com/cgi-bin/webhook/send?key={}".format(
        group_names[bot_name])
    headers = {"Content-Type": "text/plain"}
    current_app.logger.info(f"群机器人信息地址接口：{webhook}")
    sendto = requests.post(webhook, data=json.dumps(values), headers=headers)
    if sendto.status_code == 200:
        return sendto.status_code
    return "消息通知发送失败"


def loonflow_msg(values):
    """
    工单消息通知：：通过企业微信应用：自动化运维
    :param values:
    :return:
    """
    token = get_token("wxd31346a477166736", "sLKLLRi3csKtXqsLHs4XLZz7F6Y6sevfZBnRtX2kTj")
    url = 'https://qyapi.weixin.qq.com/cgi-bin/message/send?access_token=%s' % token
    send = json.dumps(values)
    sendto = requests.post(url, send)
    if sendto.status_code != 200:
        return "消息发送失败"
    return "发送成功"


def gra_msg(values):
    """
    Grafana 告警消息
    :param values:
    :return:
    """
    # 获取token
    coreID = "wxd31346a477166736"
    apisecret = "26dL4RW6Rra0UwAmpTWgMnHFxIkCwvJsMa0jyzaMr0g"
    url = 'https://qyapi.weixin.qq.com/cgi-bin/gettoken?corpid=%s&corpsecret=%s' % (coreID, apisecret)
    req = urllib.request.Request(url)
    result = urllib.request.urlopen(req)
    access_token = json.loads(result.read())
    token = access_token['access_token']

    # 发送消息
    url = 'https://qyapi.weixin.qq.com/cgi-bin/message/send?access_token=%s' % token
    send = json.dumps(values)
    sendto = requests.post(url, send)
    if sendto.status_code != 200:
        return "消息发送失败"
    return "发送成功"


def phone_msg(iphone, templatemode):
    """
    电话消息通知
    :param iphone: 电话列表
    :param templatemode: 模板参数，若模板没有参数，请提供为空数组
    :return:
    """
    from tencentcloud.common import credential
    from tencentcloud.common.profile.http_profile import HttpProfile
    from tencentcloud.common.profile.client_profile import ClientProfile
    from tencentcloud.vms.v20200902 import models, vms_client
    from tencentcloud.common.exception.tencent_cloud_sdk_exception import TencentCloudSDKException

    try:
        cred = credential.Credential("AKID51k1A6nLejZAg291INFtNwLXWKvJUWLx", "SlwufEbooeLrDOPHzWs20oX6TeeS7Zop")
        httpProfile = HttpProfile()
        httpProfile.reqMethod = "POST"  # POST 请求（默认为 POST 请求）
        httpProfile.reqTimeout = 30  # 请求超时时间，单位为秒（默认60秒）
        httpProfile.endpoint = "vms.tencentcloudapi.com"  # 指定接入地域域名（默认就近接入）
        clientProfile = ClientProfile()
        clientProfile.signMethod = "TC3-HMAC-SHA256"  # 指定签名算法
        clientProfile.language = "en-US"
        clientProfile.httpProfile = httpProfile
        client = vms_client.VmsClient(cred, "ap-guangzhou", clientProfile)
        req = models.SendTtsVoiceRequest()
        req.TemplateId = "1479921"  # 告警通知： 告警标题：{1} 告警内容：{2} 请尽快处理！
        # 模板参数，若模板没有参数，请提供为空数组
        req.TemplateParamSet = templatemode  # ["HTTP", "400"]

        req.VoiceSdkAppid = "1400295710"
        # 播放次数，可选，最多3次，默认2次
        req.PlayTimes = 2
        for i in range(len(iphone)):
            req.CalledNumber = iphone[i]
            client.SendTtsVoice(req)
        return "success"
    except TencentCloudSDKException as err:
        print(err)
        return "failed"
