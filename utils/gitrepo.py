#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
@Title:   gitrepo.py
@Date:    2022/4/18 11:40
@Author:  wanglh
@Desc:    工蜂项目管理
"""
import copy
from collections import OrderedDict

import requests

from extension import db


class CodeTencent:
    def __init__(self, git_repo=""):
        self.domain = "https://git.code.tencent.com"
        self.headers = {
            "PRIVATE-TOKEN": "IVaX7HSL1Hj06Ppasgdq"
        }
        self.git_repo = git_repo

    def get_project_branches(self):
        """
        获取所有的分支
        默认获取100条，没找到分支点击刷新按钮，记录到数据库，只保留最近一年
        """
        git_repo = copy.deepcopy(self.git_repo)
        git_repo = git_repo.replace('/', '%2F')
        branch_all = []
        page = 1
        try:
            params = {
                "page": page,
                "per_page": 100
            }
            params = copy.deepcopy(params)
            url = self.domain + "/api/v3/projects/{}/repository/branches".format(git_repo)
            res = requests.get(url=url, headers=self.headers, params=params)
            branch_list = [{"name": branch['name'], "create_time": branch["commit"]["created_at"]} for branch in res.json()]
            branch_all.extend(branch_list)
            page += 1
            name = [line['name'] for line in branch_all]

            select_sql = "SELECT branches FROM `ci_project_branchs` where git_project='%s';" % self.git_repo
            query = db.session.execute(select_sql)
            results = query.fetchall()
            for result in results:
                name.extend(result[0].split(','))
            new_name = list(OrderedDict.fromkeys(name))

            return new_name
        except Exception as e:
            return f"【Error】：{e}，Failed 没有找到项目名"
