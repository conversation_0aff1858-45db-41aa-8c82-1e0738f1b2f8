#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
@Title:   __init__.py.py
@Date:    2022/4/9 9:58
@Author:  Wanglh
@Desc:    此包：插件相关的python包
"""
from .bluekingapi import *
from .const import *
from .dnspodrecord import *
from .feishu.baseClass import get_tenant_access_token, get_timestamp
from .feishu.message import send_feishu_message
from .gitrepo import *
from .jenkinsapi import JkJob
from .messagepub import *
from .mysql import (
    acs_select_all,
    acs_select_one,
    acs_write
)
# from .other import get_short_id, get_response, DiffFile
from .other import *
from .ssobackend import *
from .tapdbakend import *
from .variable import test_project_name, tomcat_start


# from .apolloapi import ApolloWego

def _now(format='%Y-%m-%d %H:%M:%S'):
    return datetime.now().strftime(format)

def get_page_keyword(params):
    pageIndex = int(params.get('pageNo', params.get('pageIndex', 1)))
    pageSize = int(params.get('pageSize', 10))
    key = ""
    if "keyword" in params:
        key = params.get('keyword', '')
    if "inputKeyword" in params:
        key = params.get('inputKeyword', '')
    return key, pageIndex, pageSize
