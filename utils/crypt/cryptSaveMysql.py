#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
@Title:     cryptSaveMysql.py
@Date:      2024/11/25 11:24
@Author:    wanglh
@Desc:      保存文件到数据库
"""

import logging
import os
import re
import sys
from concurrent.futures import ThreadPoolExecutor
from pathlib import Path

import pymysql
from dbutils.persistent_db import PersistentDB

logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')

db_config = {
    'host': "***********",
    'user': "loonflow",
    'password': "Ccwo17k92Zw5",
    'database': "wego_ops"
}

pool = PersistentDB(
    creator=pymysql,
    maxusage=100,
    host="***********",
    user="loonflow",
    password="Ccwo17k92Zw5",
    db="wego_ops",
    cursorclass=pymysql.cursors.DictCursor,
)


def delete_file_into_db(db_config, file_id):
    try:
        with pymysql.connect(**db_config) as cnx:
            cursor = cnx.cursor()
            delete_sql = f"DELETE FROM applog_list WHERE id={int(file_id)};"
            try:
                cursor.execute(delete_sql)
                cnx.commit()
            except pymysql.Error as err:
                print(f"数据删除出错: {err}")
                cnx.rollback()
                return False
            finally:
                cursor.close()
        return True

    except Exception as e:
        print(f"Unexpected error: {e}")
        return False


def parse_entry(line):
    pattern = r'\[([A-Z])\]\[(\d{4})-(\d{2})-(\d{2})\s+[+-]\d+\.?\d*\s+(\d{2}):(\d{2}):(\d{2})\.(\d{3})\]\[(\d+),\s*(\d+[\w*]*)\]\[([^\]]*)\]\[([^,\]]+)(?:,\s*([^,\]]+)(?:,\s*(\d+))?)?\]'

    match = re.match(pattern, line)

    if match:
        log_type_module = match.group(11)
        log_type = log_type_module.split('|')[0] if '|' in log_type_module else log_type_module
        module = log_type_module.split('|')[1] if '|' in log_type_module else None

        data = {
            'log_level': match.group(1),
            'log_time': f"{match.group(2)}-{match.group(3)}-{match.group(4)} {match.group(5)}:{match.group(6)}:{match.group(7)}",
            'thread': match.group(9),
            'process': match.group(10),
            'log_type': log_type,
            'module': module,
            'part_id': None,
            'class_name': match.group(12),
            'method_name': match.group(13),
            'line_number': match.group(14),
            'log_details': ""
        }
        return data
    else:
        logging.error("match error")
        logging.error(line)
    return None


def process_item(batch):
    entries = []
    current_entry = None

    for line in batch:
        line = line.strip()
        if line.startswith('['):
            if current_entry:
                entries.append(current_entry)
            current_entry = parse_entry(line)
        elif current_entry is not None:
            if not current_entry['log_details']:
                current_entry['log_details'] = line
            else:
                current_entry['log_details'] += '\n' + line

    if current_entry:
        entries.append(current_entry)
    return entries


def process_logs(file_path, encoding='utf-8'):
    logging.info("开始逐行对日志进行解析")
    entries = []
    batch_size = 1000

    with ThreadPoolExecutor(max_workers=8) as executor:
        futures = []
        with open(file_path, 'r', encoding=encoding, errors='ignore') as f:
            lines = f.readlines()
            total_lines = len(lines)
            for start in range(0, total_lines, batch_size):
                batch = lines[start:start + batch_size]
                futures.append(executor.submit(process_item, batch))

        for future in futures:
            result = future.result()
            if result:
                entries.extend(result)

    logging.info("日志解析完成")
    return entries


def insert_log_entries(file_id, entries):
    try:
        connection = pool.connection()
        with connection.cursor() as cursor:

            add_log_entry = (
                "INSERT INTO applog_details (file_id, log_level, log_time, thread, process, log_type, module, part_id, class_name, method_name, line_number, log_details) "
                "VALUES (%s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s)"
            )

            data_log_entries = [(
                file_id, entry['log_level'], entry['log_time'], entry['thread'], entry['process'], entry['log_type'],
                entry['module'], entry['part_id'], entry['class_name'], entry['method_name'], entry['line_number'], entry['log_details']
            )
                for entry in entries]

            cursor.executemany(add_log_entry, data_log_entries)
            connection.commit()

    except pymysql.MySQLError as db_err:
        logging.error(f"Database error inserting log entry: {db_err}")
    finally:
        if connection:
            connection.close()


def process_log_file(file_path, db_config, file_id):
    try:
        filename = os.path.basename(file_path)
        logging.info(f"file_path: {file_path}")
        logging.info(f"filename: {filename}")

        if file_id:
            log_entries = process_logs(file_path, encoding="utf-8")

            batch_size = 1000

            with ThreadPoolExecutor(max_workers=8) as executor:
                futures = []
                for i in range(0, len(log_entries), batch_size):
                    batch = log_entries[i:i + batch_size]
                    futures.append(executor.submit(insert_log_entries, file_id, batch))
                for future in futures:
                    future.result()
        else:
            logging.warning(f"Failed to insert file information for: {file_path}")
        update_file_into_db(file_id, db_config)

    except FileNotFoundError:
        logging.error(f"File not found: {file_path}")
        delete_file_into_db(db_config, file_id)
    except Exception as e:
        logging.error(f"Error processing file: {file_path}. Error: {e}")
        delete_file_into_db(db_config, file_id)


def update_file_into_db(file_id, db_config, status=1):
    try:
        cnx = pymysql.connect(**db_config)
        cursor = cnx.cursor()

        add_file = "update applog_list set status = (%s) where id = (%s)"
        data_file = (status, file_id)
        cursor.execute(add_file, data_file)
        cnx.commit()
        cursor.close()
        cnx.close()
        return file_id

    except pymysql.Error as err:
        logging.error(f"数据库错误: {err}")
        return None


def xlogdetail_main(db_config, filename, file_id):
    current_path = Path(__file__).resolve()
    current_dir = current_path.parent.parent
    CRYPT_LOGS_PATH = current_dir.parent / 'static' / 'crypt'  # 上级目录的 'static/crypt'
    log_filePath = CRYPT_LOGS_PATH / str(file_id) / f"{filename}.log"  # 使用 Path 对象创建路径
    logging.info(f'SaveMySQL xlogdetail_main的logfile地址: {log_filePath}')
    process_log_file(log_filePath, db_config, file_id)


def process_log_async(filename, file_id):
    try:
        logging.info("日志文件解析数据：写入数据库中...")
        xlogdetail_main(db_config, filename, file_id)
        logging.info("日志文件解析数据：写入数据库完成...")

    except Exception as e:
        delete_file_into_db(db_config, file_id)
        logging.error(f"Error processing log file asynchronously: {e}")


if __name__ == "__main__":
    if len(sys.argv) != 3:
        logging.warning("Usage: python script.py <file_name> <file_id>")
        sys.exit(1)
    fileName = sys.argv[1]
    file_id = sys.argv[2]
    process_log_async(fileName, file_id)
    # process_log_async("xlog_2d1_20241123.xlog", "5")
