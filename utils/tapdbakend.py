#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
@Title:   tapdbakend.py
@Date:    2022/4/28 19:26
@Author:  wanglh
@Desc:    TAPD相关接口执行函数
"""
import json
import re
import requests
from flask import current_app
from sqlalchemy.dialects.mysql import insert

from model import *

API_ID = "O3cf4cmu"
API_SECRET = "6DB9C36E-5674-8C9B-ED78-DDC18BEBF049"
COMPANY_ID = "21302891"
# 项目列表黑名单
BLACK_LIST = ("深圳微购科技有限公司", "Test", "test", "面试反馈信息表", "招聘面试反馈", "wanli的wego之旅")


def get_all_project():
    """
    获取项目信息
    :return: 所有项目
    """
    url = 'https://api.tapd.cn/workspaces/projects'
    params = {"company_id": COMPANY_ID}
    res = requests.get(url=url, params=params, auth=(API_ID, API_SECRET))
    res = res.json()
    with open("project.json", 'w+', encoding='utf8') as f:
        f.write(json.dumps(res, ensure_ascii=False))
    if res.get('data'):
        items = [{"tapd_name": data["Workspace"]["name"], "tapd_id": data["Workspace"]["id"],
                  "description": data["Workspace"]["description"]} for data in res["data"] if
                 data["Workspace"]["status"] != "closed" and data["Workspace"]["name"] not in BLACK_LIST]
    else:
        return []
    try:
        for item in items:
            if item['tapd_id'] and item['tapd_name']:
                insert_stmt = insert(CiBusinessline).values(**item)
                on_duplicate_key_stmt = insert_stmt.on_duplicate_key_update(**item)
                db.engine.execute(on_duplicate_key_stmt)
    except Exception as e:
        current_app.logger.error(f"Func Name: get_all_project, {e}")
        db.session.rollback()
    finally:
        db.session.close()
    return items


def insert_iterations(items):
    try:
        for item in items:
            insert_stmt = insert(CiTapdIterations).values(**item)
            on_duplicate_key_stmt = insert_stmt.on_duplicate_key_update(**item)
            db.engine.execute(on_duplicate_key_stmt)
    except Exception as e:
        current_app.logger.error(f"Func Name：insert_iterations, {e}")
        db.session.rollback()
    db.session.close()
    return items


def get_iterations(workspace_id: str):
    """
    获取迭代列表
    :param workspace_id: 项目id
    :return: 没有完成的迭代信息
    """
    # get_iterations_count(workspace_id)
    url = "https://api.tapd.cn/iterations"
    params = {
        "workspace_id": workspace_id,
        "limit": 200,
    }
    res = requests.get(url=url, params=params, auth=(API_ID, API_SECRET)).json()
    result = [item["Iteration"] for item in res.get("data", '') if
              item["Iteration"]["completed"] is None and item["Iteration"]["status"] == "open"]
    items = []
    for item in result:
        for key in list(item.keys()):
            if "custom_field" in key or key == "release_id":
                del item[key]
            if key == "description":
                try:
                    item["description"] = re.sub('<[^<]+?>', '', item["description"]).replace('\n', '').strip()
                except Exception as e:
                    item["description"] = ""
        items.append(item)
    return items


def get_stories(workspace_id: str, iteration_id: str):
    """
    获取需求列表
    :param workspace_id: 项目id
    :param iteration_id: 迭代id
    :return: 需求列表信息清单,并更新当前迭代ID的需求
    """
    url = "https://api.tapd.cn/stories"
    params = {
        "page": 1,
        "limit": 200,
        "workspace_id": workspace_id,
        "iteration_id": iteration_id,
        "fields": "name,priority,status,creator,iteration_id,workspace_id"
    }
    res = requests.get(url=url, params=params, auth=(API_ID, API_SECRET))
    res = res.json()["data"]
    items = [item["Story"] for item in res]
    try:
        CiTapdStories.query.filter_by(iteration_id=str(iteration_id)).delete()
    except Exception as e:
        current_app.logger.error(f"Func Name：get_stories, {e}")
        db.session.rollback()
    finally:
        db.session.execute(CiTapdStories.__table__.insert(), items)  # 数据存储
        db.session.commit()
    db.session.close()
    return items


def get_iterations_count(workspace_id: int):
    """
    迭代统计
    :param workspace_id: 项目id
    :return: count
    """
    url = "https://api.tapd.cn/iterations/count"
    params = {"workspace_id": workspace_id}
    res = requests.get(url, params=params, auth=(API_ID, API_SECRET))
    count = res.json()["data"]["count"]
    return count


def get_stories_count(workspace_id: int):
    """
    需求统计
    :param workspace_id: 项目id
    :return: count
    """
    url = "https://api.tapd.cn/stories/count"
    params = {"workspace_id": workspace_id}
    res = requests.get(url, params=params, auth=(API_ID, API_SECRET))
    try:
        count = res.json()["data"]["count"]
    except Exception as e:
        current_app.logger.error(f"Func Name：get_stories_count, {e}")
        return 0
    return count


def get_fields_info(workspace_id: int):
    """
    需求字段详情
    :param workspace_id: 项目id
    :return: 需求字段列表字典信息
    """
    url = "https://api.tapd.cn/stories/get_fields_info"
    params = {
        "workspace_id": workspace_id,
        "limit": 200,
    }
    res = requests.get(url=url, params=params, auth=(API_ID, API_SECRET))
    res = res.json()["data"]
    with open("fields_info.json", 'w+', encoding='utf8') as f:
        f.write(json.dumps(res, ensure_ascii=False))
    print(res)


def get_bugs_custom_fields(workspace_id: int):
    url = f"https://api.tapd.cn/bugs/custom_fields_settings?workspace_id={workspace_id}"
    res = requests.get(url, auth=(API_ID, API_SECRET)).json()["data"]
    for i in res:
        if i["CustomFieldConfig"]["name"] == "缺陷环境":
            return i["CustomFieldConfig"]["custom_field"], i["CustomFieldConfig"]["options"]
    return None, None


def get_bugs_count(workspace_id: int, version_report: str):
    """
    获取缺陷总数
    :param workspace_id: 项目id
    :param version_report: 发现版本
    """
    SEVERITY = {

        "FATAL": "fatal",  # 致命缺陷
        "SERIOUS": "serious",  # 严重缺陷
        "NORMAL": "normal",  # 一般缺陷
        # "PROMPT": "prompt",  # 提示
        # "ADVICE": "advice"   # 建议
    }

    custom_field_one, env = get_bugs_custom_fields(workspace_id)
    if custom_field_one and env:
        url = "https://api.tapd.cn/bugs/count?status=!=rejectes"
        params = {
            "workspace_id": workspace_id,
            "version_report": f"{version_report if version_report != '' else ''}",
            custom_field_one: f"{'测试环境' if '测试环境' in env else ''}"
        }
        bug_datas = {}
        for k, v in SEVERITY.items():
            params["severity"] = v
            res = requests.get(url=url, params=params, auth=(API_ID, API_SECRET))
            res = res.json()
            bug_datas[k] = res['data']['count']
        bug_datas["TOTAL"] = sum(bug_datas.values())
        return bug_datas
    return {}
