#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
@Title:   other.py
@Date:    2022/04/09 09:58
@Update:  2023/05/05 10:24
@Author:  wa<PERSON><PERSON><PERSON>
@Desc:    
"""

import base64
import calendar
import difflib
import hashlib
import random
import re
import string
import uuid
from base64 import b64decode, b64encode
from datetime import date, time
from decimal import Decimal
from typing import Dict, Union

import pandas as pd
import pymysql
import redis
from Crypto.Cipher import AES
from flask import jsonify
from sqlalchemy import func

from model import *
from .apolloapi import ApolloWego
from .messagepub import *
from .mysql import acs_select_all

array = ["0", "1", "2", "3", "4", "5", "6", "7", "8", "9",
         "a", "b", "c", "d", "e", "f", "g", "h", "i", "j", "k", "l", "m", "n", "o", "p", "q", "r", "s", "t", "u", "v",
         "w", "x", "y", "z",
         ]


def get_file_md5(file_name):
    """
    计算文件的md5
    :param file_name:
    :return:
    """
    m = hashlib.md5()  # 创建md5对象
    with open(file_name, 'rb') as flbj:
        while True:
            data = flbj.read(4096)
            if not data:
                break
            m.update(data)  # 更新md5对象
    return m.hexdigest()  # 返回md5对象


def get_str_md5(content):
    """
    计算字符串md5
    :param content:
    :return:
    """
    m = hashlib.md5(content)  # 创建md5对象
    return m.hexdigest()


# base64编码加解密
def str_to_code(context: str) -> str:
    """
    将给定的字符串转换为对应的Base64编码字符串。
    :param context: str
    :return: str
    """
    return str(b64encode(bytes(context, 'utf8')), encoding='utf8')


def code_to_str(decode: str) -> str:
    """
    将给定的Base64编码字符串转换为对应的普通字符串
    :param decode: str
    :return: str
    """
    if len(decode) % 3 == 1:
        decode += '=='
    elif len(decode) % 3 == 2:
        decode += '='
    return b64decode(decode).decode('utf8')


def generate_random_string(length):
    """
    生成指定长度的随机字符串。

    参数:
        length (int): 要生成的随机字符串的长度。

    返回:
        str: 由字母和数字组成的随机字符串。
    """
    characters = string.ascii_letters + string.digits
    return ''.join(random.choice(characters) for i in range(length))


def get_time_stamp(start_time, format_str='%Y-%m-%d %H:%M:%S'):
    """
    获取str格式的时间
    :param start_time:
    :param format_str:
    :return:
    """

    if start_time == "":
        return ""
    if isinstance(start_time, str) and "CST" in start_time:
        strf_time = datetime.strptime(start_time, '%Y-%m-%d %H:%M:%S +0800 CST').strftime(format_str)
    elif isinstance(start_time, str) and "T" in start_time and "Z" in start_time:
        strf_time = datetime.strptime(start_time, '%Y-%m-%dT%H:%M:%S.%fZ').strftime(format_str)
    elif isinstance(start_time, str) and "T" in start_time and "+0000" in start_time:
        strf_time = datetime.strptime(start_time, '%Y-%m-%dT%H:%M:%S+0000').strftime(format_str)
    elif isinstance(start_time, datetime):
        strf_time = start_time.strftime(format_str)
    else:
        strf_time = start_time
    return strf_time


def get_classdatetime(start_time: str):
    """
    获取datetime的年月日
    :param :
        start_time: (str) 传入时间
    :return :
        cl_datetime: (datetime) year-month-day 年月日
    """

    if start_time == "":
        return ""
    try:
        cl_datetime = datetime.strptime(start_time, '%Y-%m-%d')
        return cl_datetime
    except Exception as e:
        return ""


def get_class_time(start_time: str):
    """
   获取datetime的日期
   :param :
       start_time: (str) 传入时间
   :return :
       cl_datetime: (datetime) Hour:Minutes:Seconds 时分秒
   """
    if start_time == "":
        return ""
    try:
        cl_time = datetime.strptime(start_time, '%H:%M:%S')
        return cl_time
    except Exception as e:
        return ""


def get_month_range(start_time, end_time):
    """
    获取两日期间的月份列表
    :param :
        start_time (datetime): 开始时间
        end_time (datetime): 结束时间
    :return :
        month_data (list): 月份列表
    """
    months = (end_time.year - start_time.year) * 12 + end_time.month - start_time.month
    month_range = ['%s-%s' % (start_time.year + mon // 12, mon % 12 + 1)
                   for mon in range(start_time.month - 1, start_time.month + months)]
    month_data = []
    for month in month_range:
        year = month.split('-')[0]
        mon = month.split('-')[-1]
        if len(mon) == 1:
            mon = "0" + mon
        month_data.append("{}-{}".format(year, mon))
    return month_data


def get_month_start_end(now=datetime.now()):
    """
    获取now所在月的第一天和最后一天
    :param datetime now: 默认=当前时间
    :return:
        month_start: (datetime) example: 2022-07-01 00:00:00,
        month_end: (datetime) example: 2022-07-31 00:00:00,
        days: (int) example: 30
    """
    month_start = datetime(now.year, now.month, 1)
    month_end = datetime(now.year, now.month, calendar.monthrange(now.year, now.month)[1])
    month_len = calendar.monthrange(now.year, now.month)[1]
    return month_start, month_end, month_len


def compare_min(start_time, end_time):
    """
    获取开始和结束时间的分钟数
    :param start_time: (datetime)
    :param end_time: (datetime)
    :return: mins (int)
    """
    if isinstance(start_time, datetime) and isinstance(end_time, datetime):
        total_seconds = (end_time - start_time).total_seconds()
        mins = total_seconds / 60
        return int(mins)
    return None


def validate_date_time_string(date_time_string):
    pattern = r'^\d{4}-\d{2}-\d{2} \d{2}:\d{2}:\d{2}$'
    return bool(re.match(pattern, date_time_string))


def get_short_id(beginStr="t"):
    """
    生成一个短唯一标识符。

    返回:
        str: 生成的短唯一标识符。
    """
    id = str(uuid.uuid4()).replace("-", '')  # 注意这里需要用uuid4
    buffer = [beginStr]
    for i in range(1, 8):
        start = i * 4
        end = i * 4 + 4
        val = int(id[start:end], 16)

        buffer.append(array[val % 36])

    return "".join(buffer)


def get_response(request_object) -> dict:
    global datas
    form_data = ['multipart/form-data', 'application/x-www-form-urlencoded']
    json_data = ['application/json']
    if request_object.method in ['POST', 'DELETE']:
        content_type = request_object.headers.get('content_type')
        try:
            if content_type in json_data:
                try:
                    datas = request_object.get_json()
                except Exception as e:
                    current_app.logger.warning(f'Func Name: get_response, 具体报错：\n{e}')
                    datas = request_object.json
            elif content_type in form_data:
                datas = request_object.form.to_dict()
            else:
                datas = request_object.get_json()
        except Exception as e:
            current_app.logger.error(f'Func Name: get_response, 具体报错：\n{e}')
            datas = dict()
    if request_object.method == 'GET':
        datas = dict(request_object.args)
    return datas


def get_all_request_data(request_object) -> dict:
    combined_data = {}

    # 尝试获取查询字符串参数
    try:
        combined_data['query_params'] = request_object.args.to_dict()
    except Exception as e:
        combined_data['query_params'] = str(e)

    # 尝试获取表单数据
    try:
        combined_data['form_data'] = request_object.form.to_dict()
    except Exception as e:
        combined_data['form_data'] = str(e)

    # 尝试获取 JSON 数据
    try:
        combined_data['json_data'] = request_object.get_json(silent=True)
    except Exception as e:
        combined_data['json_data'] = str(e)

    # 尝试获取原始数据
    try:
        raw_data = request_object.data
        combined_data['raw_data'] = raw_data.decode('utf-8') if raw_data else ''
    except Exception as e:
        combined_data['raw_data'] = str(e)

    # 尝试获取文件（如果有）
    try:
        files = request_object.files.to_dict()
        combined_data['files'] = list(files.keys())
    except Exception as e:
        combined_data['files'] = str(e)

    return combined_data


def new_get_response(request_object) -> Dict[str, Union[str, int, float]]:
    """
    从 HTTP 请求中获取数据并返回 Python 字典

    Args:
        request_object (request): request请求对象

    Returns:
        一个 Python 字典，包含从 HTTP 请求中获取的数据。字典的键是字符串类型，值可以是字符串、整数或浮点数类型。
    """
    content_type = request_object.headers.get('Content-Type')
    if request_object.method == 'GET':
        return dict(request_object.args)
    elif request_object.method in ['POST', 'DELETE']:
        if content_type == 'application/json':
            return request_object.json
        elif content_type in ['application/x-www-form-urlencoded', 'multipart/form-data']:
            return request_object.form.to_dict()
        elif content_type.startswith('text/') or content_type == 'application/xml':
            data = request_object.data.decode('utf-8')
            return {'data': data}
    else:
        return {}


def get_stamp_id():
    nowTime = datetime.now().strftime("%Y%m%d%H%M%S")
    randomNum = random.randint(0, 99)
    if randomNum <= 10:
        randomNum = str(randomNum) + str(0)
    uniqueNum = str(nowTime) + str(randomNum)
    uniqueNum = int(uniqueNum)
    return uniqueNum


class DiffFile:
    def __init__(self, base_content, new_content, service, filename):
        self.base_content = base_content.splitlines() if base_content else ""
        self.new_content = new_content.splitlines() if new_content else ""
        self.filename = f'{service}/{filename}'

    def read_diff(self):
        diff = difflib.unified_diff(
            self.base_content,
            self.new_content,
            fromfile=self.filename,
            tofile=self.filename,
            lineterm='',
        )
        diff_str = '\n'.join(diff)
        return diff_str


def compare_nacos(request):
    """
    nacos文件对比
    :param request:
    :return:
    """
    dataraw = get_response(request)
    new_namespace = dataraw.get('sbx_uuid', 'test-3')
    file_name = dataraw.get('filename', 'redis.yml')
    service_name = dataraw.get('service')
    conn = pymysql.Connection(user=current_app.config['NACOS_V3_USER'], password=current_app.config['NACOS_V3_PWD'],
                              host=current_app.config['NACOS_V3_HOST'], database=current_app.config['NACOS_V3_DATABASE'],
                              port=3306, cursorclass=pymysql.cursors.DictCursor)
    cur = conn.cursor()
    try:
        sql = """SELECT
                    new.data_id AS filename,
                    new.group_id AS service,
                    new.content AS new_content,
                    base.content AS base_content 
                FROM
                    config_info new
                    LEFT JOIN config_info base ON ( new.data_id = base.data_id AND new.group_id = base.group_id AND base.tenant_id = 'uat' ) 
                WHERE
                    new.tenant_id = '{}' 
                AND 
                    new.data_id = '{}'
                AND
                    new.group_id = '{}'
                AND (
                    new.md5 <> base.md5 
                OR base.md5 IS NULL)""".format(new_namespace, file_name, service_name)
        cur.execute(sql)
        sql_info = cur.fetchall()
        data = []
        for info in sql_info:
            base_content = info.setdefault('base_content', '')
            new_content = info.setdefault('new_content', '')
            diff = DiffFile(base_content, new_content, info['service'], info['filename'])
            result = diff.read_diff()
            data.append(result)
        data = "\n\n\n".join(data)
        current_app.logger.info(f"nacos - {new_namespace} - {service_name} - {file_name} - 对比完成")
        return jsonify({"code": 200, "data": data, "msg": "获取成功"})
    except Exception as e:
        current_app.logger.error(f"nacos - {new_namespace} - {service_name} - {file_name} - 对比失败")
        return jsonify({"code": 500, "msg": str(e)})
    finally:
        cur.close()
        conn.close()


def compare_nacos_servicename(request):
    """
    nacos差异文件中的列表项
    :param request:
    :return:
    """
    dataraw = get_response(request)
    new_namespace = dataraw.get('sbx_uuid', 'test-3')
    conn = pymysql.Connection(user=current_app.config['NACOS_V3_USER'], password=current_app.config['NACOS_V3_PWD'],
                              host=current_app.config['NACOS_V3_HOST'], database=current_app.config['NACOS_V3_DATABASE'],
                              port=3306, cursorclass=pymysql.cursors.DictCursor)
    cur = conn.cursor()
    try:
        sql = """SELECT
                    new.data_id AS filename,
                    new.group_id AS service,
                    new.content AS new_content,
                    base.content AS base_content 
                FROM
                    config_info new
                    LEFT JOIN config_info base ON ( new.data_id = base.data_id AND new.group_id = base.group_id AND base.tenant_id = 'uat' ) 
                WHERE
                    new.tenant_id = '{}' 
                AND (
                    new.md5 <> base.md5 
                    OR base.md5 IS NULL)""".format(new_namespace)
        cur.execute(sql)
        sql_info = cur.fetchall()

        services = [{"service": info['service'], "filename": info['filename']} for info in sql_info]
        data = {}
        for service in services:
            if service['service'] in data:
                data[service["service"]].append(service["filename"])
            else:
                data[service["service"]] = [service["filename"]]
        total = {"service_total": len(data), "file_total": len(sql_info)}
        current_app.logger.info(f"nacos - {new_namespace} 配置改动的文件列表获取完成")
        return jsonify({"code": 200, "data": data, "total": total, "msg": "获取成功"})
    except Exception as e:
        current_app.logger.error(f"nacos - {new_namespace} 配置改动的文件列表获取失败,具体信息:\n{e}")
        return jsonify({"code": 500, "msg": str(e)})
    finally:
        cur.close()
        conn.close()


def compare_nacos_writeback(request):
    """
    回写nacos-uat
    :param request:
    :return:
    """
    dataraw = get_response(request)
    new_namespace = dataraw.get('sbx_uuid')
    file_name = dataraw.get('filename')
    service_name = dataraw.get('service')
    user_name = request.headers.get('X-User')
    url = request.full_path
    client_ip = request.remote_addr
    conn = pymysql.Connection(user=current_app.config['NACOS_V3_USER'], password=current_app.config['NACOS_V3_PWD'],
                              host=current_app.config['NACOS_V3_HOST'], database=current_app.config['NACOS_V3_DATABASE'],
                              port=3306, cursorclass=pymysql.cursors.DictCursor)
    cur = conn.cursor()
    if new_namespace and file_name and service_name:
        try:
            select_sql = """
            SELECT
                new.data_id AS filename,
                new.group_id AS service,
                new.content AS new_content,
                new.md5 AS new_md5,
                base.md5 AS base_md5,
                base.content AS base_content 
            FROM
                config_info new
                LEFT JOIN config_info base ON ( new.data_id = base.data_id AND new.group_id = base.group_id AND base.tenant_id = 'uat' ) 
            WHERE
                new.tenant_id = '{}' 
                AND 
                new.data_id = '{}'
                AND
                new.group_id = '{}'
                AND (
                new.md5 <> base.md5 
                OR base.md5 IS NULL)""".format(new_namespace, file_name, service_name)
            cur.execute(select_sql)
            sql_info = cur.fetchall()
            new_content = sql_info[0]['new_content']
            base_content = sql_info[0]['base_content']
            select_file = """SELECT data_id from config_info WHERE tenant_id='uat' AND group_id='{}'""".format(
                service_name)
            cur.execute(select_file)
            sql_info = cur.fetchall()
            filename_list = [line['data_id'] for line in sql_info]

            if file_name in filename_list:
                insert_update_sql = """
                UPDATE config_info a
                INNER JOIN (
                    SELECT
                        new.data_id AS filename,
                        new.group_id AS service,
                        new.content AS new_content,
                        new.md5 AS new_md5 
                    FROM
                        config_info new 
                    WHERE
                        new.tenant_id = '{}' 
                        AND new.data_id = '{}' 
                        AND new.group_id = '{}' 
                    ) c ON a.group_id = c.service AND a.data_id = c.filename 
                    SET a.content = c.new_content, a.md5 = c.new_md5 
                WHERE
                    a.tenant_id = 'uat';
                """.format(new_namespace, file_name, service_name)
            else:
                insert_update_sql = """
                INSERT INTO config_info ( data_id, group_id, content, md5, tenant_id, gmt_create, gmt_modified )
                SELECT
                    new.data_id AS filename,
                    new.group_id AS service,
                    new.content AS new_content,
                    new.md5 AS new_md5,
                    'uat',
                    NOW(),
                    NOW() 
                FROM
                    config_info new 
                WHERE
                    new.tenant_id = '{}' 
                    AND new.group_id = '{}' 
                    AND new.data_id = '{}'""".format(new_namespace, service_name, file_name)
            cur.execute(insert_update_sql)

            current_app.logger.info(f"nacos - {new_namespace} - {service_name} - {file_name} - 回写uat成功")

            parameters = new_content if base_content is None else base_content + "\n" + new_content

            operation_log = {"username": user_name, "occur_time": datetime.now(), "client_ip": client_ip,
                             "action": "nacos配置文件回写", "url": url, "parameters": parameters,
                             "result": 200}

            db.session.execute(OperationLog.__table__.insert(), [operation_log])  # 插入数据
            current_app.logger.info(f"nacos - {new_namespace} - {service_name} - {file_name} - 配置重写日志写入完成")

            conn.commit()
            db.session.commit()
            return jsonify({"code": 200, "msg": f"nacos - {new_namespace} - {service_name} - {file_name} - 回写成功"})
        except Exception as e:
            current_app.logger.error(f"nacos - {new_namespace} - {service_name} - {file_name} - 配置重写日志写入失败。具体报错信息：\n{e}")
            return jsonify({"code": 500, "msg": str(e)})
        finally:
            cur.close()
            conn.close()
            db.session.close()
    return jsonify({"code": 500, "msg": "参数不全，取消更新"})


def compare_apollo_base(request):
    """
    apollo 配置对比基线
    :param request:
    :return:
    """
    try:
        dataraw = get_response(request)
        clustername = dataraw.get('sbx_uuid', '3a')
        clustername = clustername.split('-')[-1]
        uat_Apo = ApolloWego(envid="test", clustername="uat")
        new_Apo = ApolloWego(envid="test", clustername=clustername)
        uat_dict, new_dict = [[] for x in range(2)]
        for app in uat_Apo.appId:
            uat_dict.extend(uat_Apo.clusters_all_namespace(appId=app))
        for app in new_Apo.appId:
            new_dict.extend(new_Apo.clusters_all_namespace(appId=app))
        return uat_dict, new_dict, clustername
    except Exception as e:
        current_app.logger.error(f"apollo - {clustername} 配置对比基线失败，具体报错信息：\n{e}")
        return jsonify({"code": 500, "msg": str(e)})


def compare_apollo(request):
    """
    apollo 文件对比
    :param:
        request：请求对象,filename,service
    :return: 获
    """
    dataraw = get_response(request)
    service = dataraw['service']
    filename = dataraw['filename']
    uat_apo = ApolloWego(envid='test', clustername='uat')
    new_apo = ApolloWego(envid='test', clustername=dataraw['sbx_uuid'].split('-')[-1])
    uat_content = uat_apo.get_cluster_namespace(appid=service, filename=filename)
    new_content = new_apo.get_cluster_namespace(appid=service, filename=filename)
    diff = DiffFile(uat_content, new_content, service, filename)
    result = diff.read_diff()
    current_app.logger.info(f"apollo - {dataraw['sbx_uuid']} 配置改动的文件列表获取完成")
    return jsonify({"code": 200, "data": result, "msg": "获取成功", })


def compare_apollo_servicename(request):
    """
    apollo 差异文件列表项
    :param request:
    :return:
    """
    uat_dict, new_dict, clustername = compare_apollo_base(request)
    uat_df = pd.DataFrame(uat_dict)
    new_df = pd.DataFrame(new_dict)
    result = pd.merge(uat_df, new_df, on=['service', 'filename'])

    def function(a, b):
        num = 1 if a == b else 0
        return num

    result['bool'] = result.apply(lambda x: function(x['uat_content'], x['new_content']), axis=1)  # 获取差异
    company_df = result.loc[result['bool'] == 0, ['service', 'filename']]  # 获取差异的内容
    company_list = company_df.to_dict(orient='records')  # 获取全部的[{service:file}]
    company_dict = {}
    for service in company_list:
        if service['service'] in company_dict:
            company_dict[service["service"]].append(service["filename"])
        else:
            company_dict[service["service"]] = [service["filename"]]
    total = {"service_total": len(company_dict), "file_total": len(company_df)}
    current_app.logger.info(f"apollo - {clustername} 配置改动的文件列表获取完成")
    return jsonify({"code": 200, "data": company_dict, "msg": "获取成功", "total": total})


def compare_apollo_writeback(request):
    """
    apollo 文件回写 test-uat
    :param request：service, filename,sbx_uuid
    :return:
    """
    dataraw = get_response(request)
    service = dataraw['service']
    filename = dataraw['filename']
    user_name = request.headers.get('X-User')
    client_ip = request.remote_addr
    url = request.full_path
    try:
        uat_apo = ApolloWego(envid='test', clustername='uat')
        new_apo = ApolloWego(envid='test', clustername=dataraw['sbx_uuid'].split('-')[-1])
        uat_content = uat_apo.get_cluster_namespace(appid=service, filename=filename)
        new_content = new_apo.get_cluster_namespace(appid=service, filename=filename)
        if uat_content != "":
            current_app.logger.info("调用change_namespace")
            rel_code = uat_apo.change_namespace(appid=service, filename=filename, new_content=new_content)
        else:
            current_app.logger.info("调用add_namespace")
            rel_code = uat_apo.add_namespace(appid=service, filename=filename, new_content=new_content)
        if "ERROR" in str(rel_code):
            current_app.logger.warning(rel_code)
            return jsonify({"code": 500, "msg": f"{rel_code}"})
        # 写入到operation_log信息
        parameters = new_content if uat_content is None else uat_content + "\n" + new_content

        operation_log = {"username": user_name, "occur_time": datetime.now(), "client_ip": client_ip,
                         "action": "apollo配置文件回写", "url": url, "parameters": parameters,
                         "result": 200}

        db.session.execute(OperationLog.__table__.insert(), [operation_log])  # 插入数据
        current_app.logger.info(f"apollo - {dataraw['sbx_uuid']} - {service} - {filename} - 配置重写日志写入完成")
        db.session.commit()
        db.session.close()
        current_app.logger.info(f"apollo - {dataraw['sbx_uuid']} - {service} - {filename} - 回写成功")
        return jsonify({"code": 200, "msg": f"apollo - {dataraw['sbx_uuid']} - {service} - {filename} - 回写成功"})

    except Exception as e:
        current_app.logger.error(f"apollo - {dataraw['sbx_uuid']} - {service} - {filename} - 回写失败 - '\n' + {e}")
        return jsonify({"code": 500, "msg": f"apollo - {dataraw['sbx_uuid']} - {service} - {filename} - 回写失败：{e}"})


"""
Redis 接口函数
"""


class MyRedis(redis.Redis):
    def __init__(self, host, port=6379):
        super().__init__()
        self.r_conn = redis.Redis(host=host, port=port, decode_responses=True)

    def close(self):
        self.r_conn.close()

    def mget(self, keys):
        if isinstance(keys, list):
            return dict(zip(keys, self.r_conn.mget(keys)))

    def mset(self, keys_dict):
        if isinstance(keys_dict, dict):
            return self.r_conn.mset(keys_dict)


def skywalking_redis_mget(host="**********", port=6379):
    keys = {"prdCS": "skywalking:cs:sample", "prdA": "skywalking:A:sample", "prdB": "skywalking:B:sample"}
    cs_redis = MyRedis(host, port)
    result = cs_redis.mget(list(keys.values()))
    result = {k: (10 if v is None else int(v)) for (k, v) in result.items()}
    cs_redis.close()
    result = {key: int(result[keys[key]]) for key in keys}
    return result


def skywalking_redis_mset(keys_dict, username, host="**********", port=6379):
    keys = {"prdCS": "skywalking:cs:sample", "prdA": "skywalking:A:sample", "prdB": "skywalking:B:sample"}
    redis_keys = {keys[key]: keys_dict[key] for key in keys}
    cs_redis = MyRedis(host, port)
    result = cs_redis.mset(redis_keys)
    cs_redis.close()
    value_info = {10: "精简模式", 60: "观察模式", 100: "全量模式"}
    values = {
        "msgtype": "markdown",
        "markdown": {
            "content": "## <font color=\"warning\">SkyWalking采样率设置更新</font> \n"
                       "> A环境： %s\n"
                       "> B环境： %s\n"
                       "> 长沙环境： %s\n"
                       "> 操作人： %s" % (
                           value_info[keys_dict["prdA"]], value_info[keys_dict["prdB"]],
                           value_info[keys_dict["prdCS"]], username)
        }
    }
    bot_msg(values, "group_bk")
    return result


"""
用户部门信息判断
"""


def user_in_operations(username: str, deptname: str) -> str:
    """
    运维部门信息
    :param username:  用户名id
    :param deptname:  部门名
    :return str:
    """
    select_query = db.session.execute(f"SELECT ul.user_code,ul.user_id, ul.name, dl.dept_name FROM user_list ul LEFT JOIN user_dept ud ON  ul.user_code = ud.usercode LEFT JOIN dept_list dl ON ud.dept_id = dl.id WHERE ul.user_id LIKE '%{username}%' ")
    select_query = select_query.first()
    if select_query.dept_name == deptname:
        return "success"
    return "failed"


"""
COS 存储桶
"""


def object_push_cos(filename, bucket, path):
    """
    COS存储桶上传
    :param filename:
    :param bucket:
    :param path:
    :return:
    """
    global response
    from qcloud_cos import CosConfig
    from qcloud_cos import CosS3Client
    from qcloud_cos.cos_exception import CosClientError, CosServiceError
    import os
    """
    高级上传功能
    :param secret_id: 鉴权的ID
    :param secret_key: 鉴权的key
    :param bucket: 存储桶名
    :param filename: 文件名
    """

    secret_id = current_app.config['TX_OLD_SECRETID']
    secret_key = current_app.config['TX_OLD_SECRETKEY']
    region = current_app.config['BUCKET_REGION']
    token = None
    scheme = 'https'  # 指定使用 http/https 协议来访问 COS，默认为 https，可不填
    config = CosConfig(Region=region, SecretId=secret_id, SecretKey=secret_key, Token=token, Scheme=scheme)
    client = CosS3Client(config)

    # 使用高级接口断点续传，失败重试时不会上传已成功的分块(这里重试10次)
    for i in range(0, 10):
        try:
            response = client.upload_file(
                Bucket=bucket,
                Key=f'{path}/{filename}',
                LocalFilePath=current_app.config['UPLOAD_PATH'] + os.sep + filename,
                EnableMD5=False,
                progress_callback=None
            )
            response['filename'] = filename
            current_app.logger.info(response)
            break
        except CosClientError or CosServiceError as e:
            current_app.logger.error(e)
            return {'error': e}
    return {"ETag": response['ETag']}


def purge_urls_cache(urls: list):
    """
    COS URL and Quota Cache
    :param urls:
    :return:
    """
    from tencentcloud.common import credential
    from tencentcloud.common.profile.client_profile import ClientProfile
    from tencentcloud.common.profile.http_profile import HttpProfile
    from tencentcloud.common.exception.tencent_cloud_sdk_exception import TencentCloudSDKException
    from tencentcloud.cdn.v20180606 import cdn_client, models
    secret_id = current_app.config['TX_OLD_SECRETID']
    secret_key = current_app.config['TX_OLD_SECRETKEY']
    """刷新URL"""
    try:
        cred = credential.Credential(secret_id, secret_key)
        httpProfile = HttpProfile()
        httpProfile.endpoint = "cdn.tencentcloudapi.com"
        clientProfile = ClientProfile()
        clientProfile.httpProfile = httpProfile
        client = cdn_client.CdnClient(cred, "", clientProfile)
        req = models.PurgeUrlsCacheRequest()
        params = {
            "Urls": urls,  # 是一个列表
            "UrlEncode": True
        }
        req.from_json_string(json.dumps(params))

        resp = client.PurgeUrlsCache(req)
        current_app.logger.info(f"刷新URL调用成功:{resp.to_json_string()}")
        return resp.to_json_string()
    except TencentCloudSDKException as err:
        current_app.logger.error(err)
        return err


def describe_purge_quota():
    """
    查询刷新用量配额
    """

    from tencentcloud.common import credential
    from tencentcloud.common.profile.client_profile import ClientProfile
    from tencentcloud.common.profile.http_profile import HttpProfile
    from tencentcloud.common.exception.tencent_cloud_sdk_exception import TencentCloudSDKException
    from tencentcloud.cdn.v20180606 import cdn_client, models
    secret_id = current_app.config['TX_OLD_SECRETID']
    secret_key = current_app.config['TX_OLD_SECRETKEY']

    try:
        cred = credential.Credential(secret_id, secret_key)
        httpProfile = HttpProfile()
        httpProfile.endpoint = "cdn.tencentcloudapi.com"
        clientProfile = ClientProfile()
        clientProfile.httpProfile = httpProfile
        client = cdn_client.CdnClient(cred, "", clientProfile)
        req = models.DescribePurgeQuotaRequest()
        params = {}
        req.from_json_string(json.dumps(params))
        resp = client.DescribePurgeQuota(req)
        current_app.logger.info(f"查询COS刷新用量配额完成，结果：{resp.to_json_string()}")
        return resp.to_json_string()

    except TencentCloudSDKException as err:
        current_app.logger.error(err)
        return err


"""
日志记录
"""


def save_to_operation_log(request_object, action, result):
    """
    日志信息记录到数据库表：operation_log
    :param request_object: requests请求对象
    :param action: 类别：如sso同步配置
    :param result: 接口的调用接口：200还是500
    :return:
    """
    user_name = request_object.headers.get('X-User', '接口调用')
    client_ip = request_object.headers.get('X-Real-Ip', '')
    url = request_object.path
    method = request_object.method
    parameters = json.dumps(get_all_request_data(request_object))
    # parameters = bytes.decode(request_object.get_data())

    operation_log = {"username": user_name, "occur_time": datetime.now(), "client_ip": client_ip,
                     "action": action, "url": url, "parameters": parameters,
                     "result": result, "method": method}
    try:
        db.session.execute(OperationLog.__table__.insert(), [operation_log])  # 插入数据
        db.session.commit()
    except Exception as e:
        current_app.logger.error(f"{e}")
        db.session.rollback()
    finally:
        db.session.close()


def save_nss_operation_log(log_dict):
    """
    将nss操作信息保存到log中
    :param log_dict:
    :return:
    """

    operation_log = log_dict
    try:
        db.session.execute(NssOperationLog.__table__.insert(), [operation_log])
        db.session.commit()
    except Exception as e:
        current_app.logger.error(e)
        db.session.rollback()
    finally:
        db.session.close()


def into_ip_get_result(keyword, sql=""):
    """
    IP所属信息查询
    :param keyword:
    :return:
    """
    if sql == "":
        sql = """SELECT distinct private_ip,name,type,namespace,commit,cluster_name,public_ip,deployment_name 
        FROM `ip_list` WHERE
        (( `private_ip` = '{str}' OR `public_ip` = '{str}' OR `cluster_ip` = '{str}' OR cluster_name LIKE '%{str}%')
            OR (( namespace = '{str}' OR `name` LIKE '%{str}%') AND `type` = 'services' )
        OR concat(name,'.',namespace)='{str}' OR (`type` = '{str}')) AND namespace != 'kube-system';""".format(str=keyword)
    try:
        result = acs_select_all(sql)
        return result
    except Exception as e:
        current_app.logger.error(f"{e}")
        return list()


def compare_list(src: list, dest: list) -> list:
    """
    两个列表中的交集获取
    :param src:
    :param dest:
    :return:
    """
    if len(src) == 0:
        return dest
    r = [x for x in src if x in dest]
    return r


def add_to_32(value):
    while len(value) % 32 != 0:
        value += b'\x00'
    return value  # 返回bytes对象


def cut_value(org_str):
    org_bytes = str.encode(org_str)  # 转换为bytes字符串
    n = int(len(org_bytes) / 32)
    i = 0
    new_bytes = b""
    while n >= 1:
        i = i + 1
        new_byte = org_bytes[(i - 1) * 32:32 * i - 1]
        new_bytes += new_byte
        n = n - 1
    if len(org_bytes) % 32 == 0:
        all_bytes = org_bytes
    elif len(org_bytes) % 32 != 0 and n > 1:
        all_bytes = new_bytes + add_to_32(org_bytes[i * 32:])
    else:
        all_bytes = add_to_32(org_bytes)
    return all_bytes


"""
加解密
"""


def AES_encrypt(org_str, key):
    aes = AES.new(cut_value(key), AES.MODE_ECB)  # 初始化加密器
    encrypt_aes = aes.encrypt(cut_value(org_str))  # aes加密
    encryptd_text = str(base64.encodebytes(encrypt_aes), encoding='utf-8')
    encryptd_text = encryptd_text.strip()
    return encryptd_text


def AES_decrypt(secret_str, key):
    aes = AES.new(cut_value(key), AES.MODE_ECB)  # 初始化解密器
    base64_decrytes = base64.decodebytes(secret_str.encode(encoding='utf-8'))  # 逆向解密bytes为str
    decrypt_text = str(aes.decrypt(base64_decrytes), encoding='utf-8').replace('\0', '')  # 执行解密转码为str
    return decrypt_text


class CustomJSONEncoder(json.JSONEncoder):
    def default(self, obj):
        if isinstance(obj, datetime):
            return obj.strftime('%Y-%m-%d %H:%M:%S')
        elif isinstance(obj, date):
            return obj.isoformat()
        elif isinstance(obj, time):
            return obj.strftime('%H:%M:%S')
        elif isinstance(obj, Decimal):
            return float(obj)
        return super().default(obj)


def handle_special_chars(value):
    """url 特殊字符处理"""
    if value:
        special_chars = {'+': '%2B', '%': '%25', '&': '%26', '#': '%23', '=': '%3D'}
        for char, encoded_char in special_chars.items():
            value = value.replace(char, encoded_char)
        value = urllib.parse.unquote(value)
    return value


def get_time_format(granularity, objectTime):
    """
    时间格式化
    :param granularity: 时间区间 如 10m 5m 1h
    :param objectTime: Module 对象的时间字段
    :return:
    """
    # interval单位：分钟
    if "m" in granularity:
        interval = int("".join(filter(str.isdigit, granularity)))
        if interval == 1:
            return "%Y-%m-%d %H:%i:00"
        return func.concat(func.DATE(objectTime),
                           ' ',
                           func.LPAD(func.HOUR(objectTime), 2, '0'), ':',
                           func.LPAD(func.FLOOR(func.MINUTE(objectTime) / interval) * interval, 2, '0'),
                           ':00')
    elif "h" in granularity:
        return "%Y-%m-%d %H:00:00"
    elif "d" in granularity:
        return "%Y-%m-%d 00:00:00"
    else:
        return "%Y-%m-%d 00:00:00"
