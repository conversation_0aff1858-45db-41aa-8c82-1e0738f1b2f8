#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
@Title:   getnotices.py
@Date:    2023/12/28 10:21
@Author:  wanglh
@Desc:    主要功能：消息的发送，数据库的查询与插入
"""

import datetime
import json

import requests
import timeago

from . import some_variable
from .devMysql import MySQL, select


# from user.MySQL import MySQL, select


class Select():
    def __init__(self, role="all", user="none"):
        self.role = role
        self.user = user
        self.now = datetime.datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        if self.role is None:
            self.role = "all"
        if self.user is None:
            self.user = "none"

    def select_noticeslist(self):
        sql = 'SELECT info,date FROM `notices_list` where (role="{}" or user="{}") and date > curdate()  ORDER BY date DESC limit 10'.format(self.role, self.user)
        try:
            data = select(sql)
            data = [{"title": i['info'], "date": timeago.format(i['date'], self.now, "zh_CN")} for i in data]
            return data
        except Exception as e:
            print(e)
            return "查询失败"

    def select_userlist(self, iphone):
        sql = 'SELECT `user_id` FROM `user_list` where mobile = "{iphone}" or telephone = "{iphone}"'.format(iphone=iphone)
        try:
            user_ids = select(sql)
            user_id = user_ids[0]['user_id']
            user_id = user_id.replace("<EMAIL>", "gufenglian")
            user_id = user_id.replace("<EMAIL>", "xuchang")
            user_id = user_id.replace("wg-linyingjie", "linyingjie")
            print(user_id)
            return user_id

        except BaseException as b:
            print(b)
            print("查询失败")
            return "查询失败"


def ipSearch(src_str):
    sqlAvg = """SELECT distinct private_ip,NAME,type,namespace,cluster_name
        FROM `ip_list` 
        WHERE
	        (( `private_ip` = '{str}' OR `public_ip` = '{str}' OR `cluster_ip` = '{str}' ) 
	        OR (( namespace = '{str}' OR `name` LIKE '%{str}%' ) AND `type` = 'services' ) 
	        or concat(name,".",namespace)='{str}')
	        AND namespace != 'kube-system';""".format(str=src_str)
    try:
        data = select(sqlAvg)
        info = str()
        for i in data:
            value = """
                <tr>
                    <td align="center">{}</td>
                    <td align="center">{}</td>
                    <td align="center">{}</td>
                    <td align="center">{}</td>
                    <td align="center">{}</td>
                </tr>
            """.format(i['private_ip'], i['NAME'], i['type'], i['namespace'], i['cluster_name'])
            info += value
        infos = f"""
            <p>查询内容：{src_str}</p>
            <style>
                .table{{width:100%;table-layout:fixed;}}
                .td{{width:100%;overflow:hidden;}}
            </style>
            <div style="max-height: 500px;overflow-y:scroll;overflow-x:none;">
            <table cellspacing="0" cellpadding="2" width="100%" border="1" style="table-layout:fixed;" align="center">
                <tr>
                    <th  align="center" width="40">IP</th>
                    <th  align="center" width="45">名称</th>
                    <th  align="center" width="16">类型</th>
                    <th  align="center" width="29">命名空间</th>
                    <th  align="center" width="24">备注</th>
                </tr>
                {info}
            </table>
            </div>"""
        return infos

    except BaseException as e:
        print(e)
        return '查询失败'


def resulthtml(value):
    infos = f"""
        <style>
            .table{{width:100%;table-layout:fixed;}}
            .td{{width:100%;overflow:hidden;}}
        </style>
        <div style="max-height: 500px;overflow-y:scroll;overflow-x:none;">
        <table cellspacing="0" cellpadding="2" width="100%" border="1" style="table-layout:fixed;" align="right">
            <tr>
                <td width="10">请求uri</td>
                <td width="40">{value["request_uri"]}</td>
            </tr>
            <tr>
                <td width="10">后端</td>
                <td width="40">{value["upstream"]}</td>
            </tr>
            <tr>
                <td width="10">转发uri</td>
                <td width="40">{value["map"]}</td>
            </tr>
            <tr>
                <td width="10">白名单</td>
                <td width="40">{value["whitelist"]}</td>
            </tr>
            <tr>
                <td width="10">环境</td>
                <td width="40">{value["env"]}</td>
            </tr>
        </table>
        </div>"""
    print(infos)
    return infos


def Seearch_podis(client_ip, namespace):
    sqlAvg = "SELECT name FROM `ip_list` WHERE (`private_ip` = '{ip}' or `public_ip` = '{ip}' or `cluster_ip` = '{ip}') and namespace = '{namespace}';".format(ip=client_ip, namespace=namespace)
    try:
        data = select(sqlAvg)
        if len(data) == 1:
            pod_id = data[0]['name']
        return pod_id
    except BaseException as e:
        print(e)
        return '查询失败'


def select_scenario():
    sql = 'SELECT `scenario` FROM `system_config` ORDER BY `cre_time` DESC LIMIT 1;'
    try:
        data = select(sql)
        scenario = data[0]['scenario']  # 结果为int
        return scenario
    except BaseException as e:
        print(e)
        print('查询失败')
        return None


def select_user(user):
    try:
        sql = 'SELECT name FROM `user_list` WHERE `user_id` LIKE "{}%";'.format(user)
        data = select(sql)
        name = data[0]['name']
        return name
    except:
        return '查询失败'


def insert_scenario(scenario):
    sql = 'INSERT INTO system_config (scenario,cre_time) values ({},now());'.format(int(scenario))
    mysql = MySQL()
    try:
        mysql.write(sql)
        mysql.commit()
        return "数据插入成功"
    except BaseException:
        mysql.rollback()
        return "数据插入失败"
    finally:
        mysql.__disconnect__()


def insert_notice(values):
    '''
    消息信息保存到mysql中
    '''
    sql = 'INSERT INTO `notices_list` (info,role,user,date,level) VALUES {}'.format(values)
    mysql = MySQL()
    try:
        mysql.write(sql)
        mysql.commit()
        return "数据插入成功"
    except BaseException:
        mysql.rollback()
        return "数据插入失败"
    finally:
        mysql.__disconnect__()


def insert_env_reservation_details(sql):
    mysql = MySQL()
    try:
        mysql.write(sql)
        mysql.commit()
        result = 0
    except BaseException:
        mysql.rollback()
        result = 1
    finally:
        mysql.__disconnect__()
    return result


def sendToMagMd_mk(value):
    content = value["markdown"]["content"]
    content = content.replace("gufenglian", "<EMAIL>")
    content = content.replace("xuchang", "<EMAIL>")
    content = content.replace("linyingjie", "wg-linyingjie")
    content = content.replace("lingxinggang", "linghanggang")
    content = content.replace("yangxi", "wg-yangxi")
    content = content.replace("gaoquan", "justin")
    value["markdown"]["content"] = content

    send = json.dumps(value)
    # 版本发布机器人
    webhook = "https://qyapi.weixin.qq.com/cgi-bin/webhook/send?key={}".format(
        some_variable.group_names["group_bk"])
    sendto = requests.post(webhook, send)
    if sendto.status_code == 200:
        return sendto.status_code
    else:
        return "消息通知发送失败"


def sendtoMerge(value):
    send = json.dumps(value)
    # 版本发布机器人
    webhook = "https://qyapi.weixin.qq.com/cgi-bin/webhook/send?key={}".format(some_variable.group_names["group_deploy"])
    sendto = requests.post(webhook, send)
    if sendto.status_code == 200:
        return sendto.status_code
    else:
        return "消息通知发送失败"


# def sso_logs(values):
#     print("X-Users:", values.gets('X-User'))
# pass

def selenvprod(deploy_env, creator):
    deploy_name = some_variable.deploy_name[deploy_env]
    hourse = datetime.datetime.now().strftime('%H')
    time = 0 if int(hourse) < 13 else 1

    SQL = f"""SELECT
            # r.env_name 'name',
            # resTime 'time',
            resUser 'user'
        FROM
            env_reservation_details AS er
            LEFT JOIN env_reservation r ON r.id = er.env_id 
        WHERE
            er.resDate = DATE(
            CURDATE()) 
            AND r.env_name = '{deploy_name}' AND resTime = {time};
    """
    result = select(SQL)
    users = list(set([i['user'] for i in result]))
    if len(users) == 0:
        user = ""
    else:
        user = users[0]

    creator = creator.replace('gufenglian', '<EMAIL>').replace('xuchang', '<EMAIL>')
    ali_user = user.replace('gufenglian', '<EMAIL>').replace('xuchang', '<EMAIL>')
    value_one = {
        "touser": creator,
        "toparty": "",
        "msgtype": "markdown",
        "agentid": "1000031",
        "markdown": {
            "content": "## <font color=\"warning\">【%s】 已被人预定</font> \n"
                       "> 预订人：<font color=\"info\">%s</font>\n"
                       "> 请通知预订人<@%s>环境情况！\n" % (deploy_name, ali_user, ali_user)
        }
    }
    value_two = {
        "msgtype": "markdown",
        "markdown": {
            "content": "## <font color=\"warning\">【%s】 已被人预定</font> \n"
                       # "> 预订人：<font color=\"info\">%s</font>\n"
                       # "> 请通知预订人<@%s>环境情况！ \n"
                       "> <@%s>" % (deploy_name, creator)
        }
    }

    return user, value_one, value_two
