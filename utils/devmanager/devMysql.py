#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
@Title:   devMysql.py
@Date:    2023/12/28 10:21
@Author:  wanglh
@Desc:    mysql封装类
"""

import pymysql


class MySQL:
    def __init__(self):
        self.host = "***********"
        self.user = "loonflow"
        self.password = "Ccwo17k92Zw5"
        self.db = "wego_ops"
        self.con = pymysql.connect(host=self.host, user=self.user, password=self.password, db=self.db,
                                   cursorclass=pymysql.cursors.DictCursor)
        self.cur = self.con.cursor()

    def __disconnect__(self):
        self.cur.close()
        self.con.close()

    # 获取数据库数据
    def select(self, sql):
        self.cur.execute(sql)
        data = self.cur.fetchall()
        return data

    # 写入更新数据
    def write(self, sql):
        self.cur.execute(sql)

    def commit(self):
        self.con.commit()

    def rollback(self):
        self.con.rollback()


def select(sql):
    commit = MySQL()
    sql_data = commit.select(sql)
    commit.__disconnect__()
    return sql_data


def insert(sql, action):
    mysql = MySQL()
    try:
        mysql.write(sql)
        mysql.commit()
        print(f"{action}日志写入成功")
    except Exception as e:
        mysql.rollback()
        print(f"{action}日志写入失败")
    finally:
        mysql.__disconnect__()


class MysqlAcs:
    def __init__(self):
        self.host = "***********"
        self.user = "loonflow"
        self.password = "Ccwo17k92Zw5"
        self.db = "wego_ops"
        self.con = pymysql.connect(host=self.host, user=self.user, password=self.password, db=self.db,
                                   cursorclass=pymysql.cursors.DictCursor)
        self.cur = self.con.cursor()

    def __disconnect__(self):
        self.cur.close()
        self.con.close()

    # 写入更新数据
    def execute(self, sql):
        self.cur.execute(sql)

    def commit(self):
        self.con.commit()

    def rollback(self):
        self.con.rollback()


def acs_write(sql):
    global message
    acs_mysql = MysqlAcs()
    try:
        acs_mysql.execute(sql)
        acs_mysql.commit()
        message = "SUCCESS"
    except Exception as e:
        acs_mysql.rollback()
        message = str(e)
    finally:
        acs_mysql.__disconnect__()
        return message


def acs_select_all(sql):
    """
    获取的是一个字典类型的列表
    """
    acs_mysql = MysqlAcs()
    acs_mysql.execute(sql)
    result = acs_mysql.cur.fetchall()
    acs_mysql.__disconnect__()
    return result


def acs_select_one(sql):
    """
    获取的是一个字典
    """
    acs_mysql = MysqlAcs()
    acs_mysql.execute(sql)
    result = acs_mysql.cur.fetchone()
    acs_mysql.__disconnect__()
    return result
