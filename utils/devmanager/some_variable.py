#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
@Title:   some_variable.py
@Date:    2023/12/28 10:21
@Author:  wanglh
@Desc:    变量维护
"""

servers_names = {'acs': '新相册', 'bcs': '新订单', 'cms': '新商品', 'album': '老相册',
                 'order': '老订单', 'pts': '支付', 'lis': '物流', "ccs": "内容违规检测", "cas": "图片资源违规检测", "xway": "上下游渠道铺货"}

setkwargs = {"acs": "wego-album", "album": "wego-legacy-album", "bcs": "wego-order", "order": "wego-legacy-order",
             "pts": "wego-payment", "lis": "wego-logistics", "job": "wego-job", "admin": "lookbook",
             "ccs": "wego-content-check", "cas": "wego-content-album", "cms": "wego-commodity", "xway": "wego-xway",
             "adapter": "adapter", "identification": "identification", "shop": "shop", "increase": "increase", "commodity": "commodity", "check": "check", "ai": "ai",
             "bifrost": "bifrost", "commoditysearch": "commoditysearch", "xwayshare": "xwayshare", "marketing": "marketing", "statistics": "statistics", "flygeesecenter": "flygeesecenter",
             "flygeeseadmin": "flygeeseadmin", "xwaypush": "xwaypush", "increaseadmin": "increaseadmin"}

consumers = {"batchmodfiy-consumer": "新相册-批量编辑消费者", "commodity-consumer": "新相册-商品编辑消费者",
             "batch-load-consumer": "新相册-批量上传消费者", "es-migrate-consumer": "新相册-ES转储消费者", "vip-order-cs": "新相册-专享小程序消费者",
             "imgsearch": "新相册-图搜独立服务",
             "textsearch": "新相册-文搜独立服务", "batchedit-job": "新相册-批量编辑任务", "es-crontable-job": "新相册-ES任务",
             "imgsearch-consumer": "老相册-图搜消费者", "oldadmin": "老相册-老后台工具页", "crontools": "老相册-定时任务",
             "bcs-job": "新订单-定时任务"}

jenkins_ser_project = {
    "现网-上下游渠道铺货": ["xway"],
    "现网-后端-容器": ["acs", "bcs", "pts", "album", "order", "lis", "cms"],
    "现网-数据中心": ["das", "dasjob"],
    "现网-新后台": ["lookbook"],
    "违规检测项目": ["cas", "ccs"],
    "现网-wego-job": ["pro-job"],
    "现网-输入法话术": ["wego-script"],
    "现网-微购3.0流水线-B": ["commodity"],
    "现网-微购3.0流水线": ["adapter", "identification", "shop", "increase", "check", "ai",
                           "commoditysearch", "xwayshare", "marketing", "statistics", "flygeesecenter",
                           "flygeeseadmin", "xwaypush", "increaseadmin", 'imagesearch', "albums", "bifrost", "searching"]
}

jenkins_pre_project = {
    "预发布-微购3.0流水线-B": ["commodity"],
    "预发布-微购3.0流水线": ["adapter", "identification", "shop", "increase", "imagesearch", "check", "ai",
                             "commoditysearch", "xwayshare", "marketing", "statistics", "flygeesecenter",
                             "flygeeseadmin", "xwaypush", "increaseadmin", "albums", "bifrost", "searching"]
}

git_project = "wsxc/wego-basic,wsxc/wego-partner,wsxc/wego-album,wsxc/wego-legacy-album,wsxc/wego-payment,wsxc/wego-legacy-order,wsxc/wego-order,wsxc/wego-job,wsxc/lookbook,wsxch5/wsxc_portal,wsxc/wego-data-analysis,wsxc/wego-logistics,wsxc/wego-commodity,wsxc/wego-xway,wsxch5/wsxc_xway,wsxch5/wsxc_album,wsxch5/wsxc_order,wsxch5/wsxc_biz,wsxc/szwego-identification,wsxc/szwego-adapter,wsxc/szwego-shop,wsxc/szwego-commodity,wsxc/szwego-xway,wsxc/szwego-increase,wsxc/szwego-merchants,wsxc/szwego-lottery,wsxc/szwego-coupon,wsxc/szwego-center,wsxc/szwego-ai,wsxc/szwego-check,wsxc/szwego-searching,wsxc/szwego-imagesearch,wsxc/szwego-commoditysearch,wsxc/szwego-bifrost"

# git_project_list = re.split(',',git_project)

# 群机器人
group_names = {
    # 运维组群机器人
    'group_ops': 'ef41f3c9-ee62-4eca-b348-9e71c240663e',
    # 中后台群机器人
    'group_bk': 'ae433d87-e905-4946-8dcf-b519b883060e',
    # jenkins打包群
    'group_backage': '85c1d995-c9e5-469f-b1b0-9ad65e2db775',
    # 版本发布群机器人
    'group_deploy': '3ac25e7d-b05d-4755-94db-5f92aef8eea2'
}

jkmaster = "http://*********:8088/jenkins"
jkuser = 'devops'
jkpassword = "WEgo%402019%28%29*"

# 电话通知信息人
Iphone = {"wanglinhao": "+8617628337189", "wuxuan": "+8613076930237", "dongxin": "+8613480787903"}
Iphone_other = {"jiyelin": "+8613632807795", "yuanzhongxing": "+8618011308162", "<EMAIL>": "+8617722698031"}
level = {"info": 0, "WARN": 1, "ERROR": 2, "FATAL": 3}

# 运维人员管理
devops_users = "dongxin|wuxuan|wanglinhao|lijiana"

# 腾讯相关参数
tc_deployenv = {
    "apm": "https://console.cloud.tencent.com/apm?hideWidget=true&hideTopNav=true",
    "streammanage": "https://console.cloud.tencent.com/live/streammanage?hideWidget=true&hideTopNav=true&hideLeftNav=true"
}

tc_SecretId = "AKIDtWFh04sSothLjro8sLXswl22uoSptaZK"
tc_SecretKey = "dGdka9SlrhTzFRMEpVnqdAlqxoqcm12I"
deploy_name = {"pre_1": "预发布-1", "pre_2": "预发布-2"}


class bcolors:
    HEADER = '\033[95m'
    OKBLUE = '\033[94m'
    OKGREEN = '\033[92m'
    WARNING = '\033[93m'
    FAIL = '\033[91m'
    ENDC = '\033[0m'
    BOLD = '\033[1m'
    UNDERLINE = '\033[4m'
