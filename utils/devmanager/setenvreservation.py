#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
@Title:   setenvreservation.py
@Date:    2023/12/28 10:21
@Author:  wanglh
@Desc:    接口地址-usr/api/setEnvReservation   预定环境
"""

import datetime

from . import devMysql, getnotices, some_variable


# from user import getnotices


def request_parse(req_data):
    '''解析请求数据并以json形式返回'''
    if req_data.method == 'POST':
        data = req_data.json
        user = req_data.headers['X-User']
    elif req_data.method == 'GET':
        data = req_data.args
        user = req_data.headers['X-User']
    return data, user


def parse_value(data, user):
    year = datetime.date.today().year
    resName = data.get('resName').strip('"')
    startTime = data.get('startTime').replace("上午", "0").replace("下午", "1").replace("月", "-").replace("日", "").strip('"')
    endTime = data.get('endTime').replace("上午", "0").replace("下午", "1").replace("月", "-").replace("日", "").strip('"')
    startTime = "-".join([str(year), startTime])
    endTime = "-".join([str(year), endTime])

    resUser = user
    sday = datetime.datetime.strptime(startTime.split(" ")[0].strip(), "%Y-%m-%d").date()
    eday = datetime.datetime.strptime(endTime.split(" ")[0].strip(), "%Y-%m-%d").date()
    stime = int(startTime.split(" ")[-1])
    etime = int(endTime.split(" ")[-1])
    tempo = (eday - sday).days  # 日期差
    if stime < etime:  # 上下午判断
        count = tempo * 2 + 2
    elif stime == etime:
        count = tempo * 2 + 1
    elif stime > etime:
        count = tempo * 2

    num = 0
    info = {"user": resUser, "env": resName, "strTime": "{} {}".format(sday, str(stime).replace("0", "上午").replace("1", "下午"))}
    for i in range(count):
        date_str = sday
        time = stime

        sql = '''insert into env_reservation_details select null,id,"{date}",{time},"{user}","" from env_reservation where env_name = "{name}";'''.format(date=date_str, time=time, user=resUser, name=resName)
        result = getnotices.insert_env_reservation_details(sql)
        if result == 1:  # 判断是否执行正常
            break
        if time == 1:
            sday = date_str + datetime.timedelta(days=1)
            stime = time - 1
        if time == 0:
            sday = date_str
            stime = time + 1
        print(some_variable.bcolors.HEADER)
        print(sql)
        print(some_variable.bcolors.ENDC)

        num += 1  # 标识作用
    info["endTime"] = "{} {}".format(date_str, str(time).replace("0", "上午").replace("1", "下午"))
    if num == count:
        return 0, info
    else:
        return 1, info


def delete_env(data, user):
    year = datetime.date.today().year
    resName = data.get('resName').strip('"')
    startTime = data.get('startTime').replace("上午", "0").replace("下午", "1").replace("月", "-").replace("日", "").strip('"')
    endTime = data.get('endTime').replace("上午", "0").replace("下午", "1").replace("月", "-").replace("日", "").strip('"')
    startTime = "-".join([str(year), startTime])
    endTime = "-".join([str(year), endTime])

    resUser = user
    sday = datetime.datetime.strptime(startTime.split(" ")[0].strip(), "%Y-%m-%d").date()
    eday = datetime.datetime.strptime(endTime.split(" ")[0].strip(), "%Y-%m-%d").date()
    stime = int(startTime.split(" ")[-1])
    etime = int(endTime.split(" ")[-1])
    tempo = (eday - sday).days  # 日期差
    if stime < etime:  # 上下午判断
        count = tempo * 2 + 2
    elif stime == etime:
        count = tempo * 2 + 1
    elif stime > etime:
        count = tempo * 2

    num = 0
    info = {"user": resUser, "env": resName, "strTime": "{} {}".format(sday, str(stime).replace("0", "上午").replace("1", "下午"))}
    for i in range(count):
        date_str = sday
        time = stime
        sql = '''DELETE FROM env_reservation_details WHERE resDate = "{date}" AND resTime = "{time}" AND resUser = "{resUser}" AND env_id = ( SELECT env_id FROM env_reservation WHERE env_name = "{name}" );'''.format(date=date_str, time=time, user=resUser, name=resName)
        action = "删除预定环境"
        devMysql.insert(sql, action)
        result = 1
        if result == 1:  # 判断是否执行正常
            break
        if time == 1:
            sday = date_str + datetime.timedelta(days=1)
            stime = time - 1
        if time == 0:
            sday = date_str
            stime = time + 1
        print(some_variable.bcolors.HEADER)
        print(sql)
        print(some_variable.bcolors.ENDC)
        num += 1  # 标识作用
    info["endTime"] = "{} {}".format(date_str, str(time).replace("0", "上午").replace("1", "下午"))
    if num == count:
        return 0, info
    else:
        return 1, info


def Sendto(info, num):
    if num == 0:
        values = {
            "msgtype": "markdown",
            "markdown": {
                "content": "## 预定环境通知：<font color=\"info\">预定成功</font> \n"
                           "> 环境：%s\n"
                           "> 时间：%s---%s\n"
                           "> [查看预定信息](https://oa.in.szwego.com/wx.html?url=https://envres.in.szwego.com/) \n"
                           "> 预订人：<@%s>\n" % (info["env"], info["strTime"], info["endTime"], info["user"])
            }
        }
    if num == 2:
        values = {
            "msgtype": "markdown",
            "markdown": {
                "content": "## 预定环境通知：<font color=\"info\">删除成功</font> \n"
                           "> 环境：%s\n"
                           "> 时间：%s---%s\n"
                           "> [查看预定信息](https://oa.in.szwego.com/wx.html?url=https://envres.in.szwego.com/) \n"
                           "> 删除人：<@%s>\n" % (info["env"], info["strTime"], info["endTime"], info["user"])
            }
        }
    getnotices.sendToMagMd_mk(values)
    name = getnotices.select_user(user=info["user"])
    message = "{}:{}--{},预定人：{}".format(info["env"], info["strTime"], info["endTime"], name)
    date = datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S')
    value = (message, "all", info["user"], date, some_variable.level["FATAL"])
    getnotices.insert_notice(value)
