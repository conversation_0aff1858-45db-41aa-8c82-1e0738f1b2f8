import pymysql

from utils.repairOrder import config_read, env


class PayMySqlPro:
    def __init__(self):
        self.host = config_read.get(f"PayMysql-{env}", "host")
        self.port = int(config_read.get(f"PayMysql-{env}", "port"))
        self.user = config_read.get(f"PayMysql-{env}", "user")
        self.password = config_read.get(f"PayMysql-{env}", "password")
        self.database = config_read.get(f"PayMysql-{env}", "database")

    def get_cursor(self):
        self.mysqlConn = pymysql.connect(host=self.host, port=self.port, user=self.user, passwd=self.password, db=self.database, charset='utf8mb4')
        self.cursor = self.mysqlConn.cursor(pymysql.cursors.DictCursor)
        return self.cursor

    def mysqlClose(self):
        self.mysqlConn.commit()
        self.cursor.close()
        self.mysqlConn.close()
