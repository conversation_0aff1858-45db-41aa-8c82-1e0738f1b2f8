#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
@Title:     __init__.py
@Date:      2023/6/25 10:46
@Author:    wanglh
@Desc：     
"""
import os
from configparser import ConfigParser

# 获取当前文件所在的绝对路径
current_path = os.path.abspath(__file__)

# 获取当前文件所在目录的路径
current_dir = os.path.dirname(current_path)

# 将当前文件所在目录的父目录与 config.ini 进行拼接，得到 config.ini 文件的绝对路径
config_path = os.path.join(os.path.dirname(current_dir), '..', 'config.ini')
config_read = ConfigParser()
config_read.read(config_path)
env = os.environ.get('FLASK_ENV')
