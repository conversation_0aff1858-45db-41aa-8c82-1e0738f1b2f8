from .OrderMySqlPro import OrderMySqlPro
from .OrderMysqlALi import OrderMySqlALi
from .PayMySqlPro import PayMySqlPro
from .PayMysqlALi import PayMySqlALi
from .RepairOneOrderBase import RepairOneOrderBase

"""
处理支付业务
"""


class RepairOneOrder(RepairOneOrderBase):
    def processPay(self, albumId, orderId):
        logHeader = "【albumId：" + albumId + "，orderId:" + orderId + "】"
        # 获取支付账户ID，没有找到直接return
        accountId = self.getAccountId(albumId)
        if accountId is None:
            print(logHeader + "恢复失败，没找到支付账户")
            return "恢复失败，没找到支付账户"

        # 获取支付订单ID，没找到直接返回 - 从备份库
        payOrderId = self.getPayOrderId(orderId)
        if payOrderId is None:
            print(logHeader + "没找到支付订单ID")
            return "恢复失败，没找到该订单"

        # check支付现网是否已存在
        if self.checkPayProExist(payOrderId):
            return "支付数据已存在"

        # 构建insert语句
        mysql: PayMySqlALi = PayMySqlALi()
        cursor = mysql.get_cursor()
        insertSql = []
        try:
            tabs: [dict] = [
                {"name": "tb_pay_order", "sql": "select * from tb_pay_order where c_pay_order_id = '" + payOrderId + "'"},
                {"name": "tb_join_pay_order", "sql": "select * from tb_join_pay_order where c_pay_order_id = '" + payOrderId + "'"},
                {"name": "tb_heli_pay_order", "sql": "select * from tb_heli_pay_order where c_pay_order_id = '" + payOrderId + "'"},
                {"name": "tb_ledger_account_record", "sql": "select * from tb_ledger_account_record where c_transaction_id = '" + payOrderId + "'"},
                {"name": "tb_account_bill", "sql": "select * from tb_account_bill where c_transaction_id = '" + payOrderId + "'"},
                {"name": "tb_sub_account_bill", "sql": "select * from tb_sub_account_bill where c_transaction_id = '" + payOrderId + "'"},
            ]

            for item in tabs:
                # print("----查询sql："+item.get("sql"))
                cursor.execute(item.get("sql"), ())
                dataList = cursor.fetchall()
                if dataList is None:
                    continue
                sqls = self.buildInsertSql(item.get("name"), dataList)
                if sqls is not None:
                    insertSql.extend(sqls)
            # print(insertSql)
            for sql in insertSql:
                print(sql)
        except Exception as e:
            print(logHeader + "组装insert出现异常：" + e)
            return "组装insert出现异常"
        finally:
            mysql.mysqlClose()

        # 恢复到现网数据库
        payMySqlPro: PayMySqlPro = PayMySqlPro()
        try:
            payMyProCursor = payMySqlPro.get_cursor()

            for sql in insertSql:
                payMyProCursor.execute(sql, ())
        except Exception as e:
            print(logHeader + "恢复数据出现异常：" + e)
            return "恢复数据出现异常"
        finally:
            payMySqlPro.mysqlClose()

        return "处理成功"

    """
    处理订单业务
    """

    def processOrder(self, albumId, orderId):
        # 查询订单是否已存在，已存在则不处理
        result: bool = self.checkOrderInPro(orderId)
        if result:
            return "订单已存在"

        mysql: OrderMySqlALi = OrderMySqlALi()
        cursor = mysql.get_cursor()

        try:
            tabs: [dict] = [
                {"name": "tb_sale_order", "sql": "select * from tb_sale_order where c_sale_order_id = '" + orderId + "'"},
                {"name": "tb_sale_order_detail", "sql": "select * from tb_sale_order_detail where c_sale_order_id = '" + orderId + "'"},
                {"name": "order_bonus_record", "sql": "select * from order_bonus_record where order_id = '" + orderId + "'"},
                {"name": "order_stock_record", "sql": "select * from order_stock_record where order_id = '" + orderId + "'"},
                {"name": "tb_order_2_xway", "sql": "select * from tb_order_2_xway where c_order_id = '" + orderId + "'"},
                {"name": "tb_order_bill", "sql": "select * from tb_order_bill where c_transaction_id = '" + orderId + "' and c_album_id = '" + albumId + "' and c_transaction_id like '120%%'"},

                {"name": "tb_order_preferential_info", "sql": "select * from tb_order_preferential_info where c_sale_order_id = '" + orderId + "'"},
                {"name": "tb_order_refund_distribution_agent_info", "sql": "select * from tb_order_refund_distribution_agent_info where c_sale_order_id = '" + orderId + "'"},
                {"name": "tb_order_send", "sql": "select * from tb_order_send where c_sale_order_id ='" + orderId + "'"},
                {"name": "tb_order_send_detail", "sql": "select * from tb_order_send_detail where c_send_order_id = '" + orderId + "'"},
                {"name": "tb_order_staff_compensation_record", "sql": "select * from tb_order_staff_compensation_record where order_id = '" + orderId + "'"},

                {"name": "tb_order_wait", "sql": "select * from tb_order_wait where c_sale_order_id = '" + orderId + "'"},
                {"name": "tb_order_wait_detailwhere", "sql": "select * from tb_order_wait_detail where c_wait_order_id = '" + orderId + "'"},
                {"name": "tb_sale_order_activity_group", "sql": "select * from tb_sale_order_activity_group where c_sale_order_id = '" + orderId + "'"},
                {"name": "tb_sale_order_buyer_msg_v2", "sql": "select * from tb_sale_order_buyer_msg_v2 where c_sale_order_id = '" + orderId + "'"},
                {"name": "tb_sale_order_detail_distribution_agent_info", "sql": "select * from tb_sale_order_detail_distribution_agent_info where c_sale_order_id = '" + orderId + "'"},

                {"name": "tb_sale_order_detail_extend", "sql": "select * from tb_sale_order_detail_extend where c_sale_order_id = '" + orderId + "'"},
                {"name": "tb_sale_order_distribution_agent_info", "sql": "select * from tb_sale_order_distribution_agent_info where c_sale_order_id = '" + orderId + "'"},
                {"name": "tb_sale_order_extend", "sql": "select * from tb_sale_order_extend where c_sale_order_id = '" + orderId + "'"},
                {"name": "tb_sale_order_remarks_v2", "sql": "select * from tb_sale_order_remarks_v2 where c_sale_order_id = '" + orderId + "'"},
                {"name": "tb_sale_order_reverse_record", "sql": "select * from tb_sale_order_reverse_record where c_sale_order_id = '" + orderId + "'"},

                {"name": "tb_order_complaint_record", "sql": "select * from tb_order_complaint_record where c_sale_order_id = '" + orderId + "'"},
                {"name": "tb_order_complaint_operate_record", "sql": "select * from tb_order_complaint_operate_record where c_complaint_no in (select c_complaint_no from tb_order_complaint_record where c_sale_order_id = '" + orderId + "')"},
                {"name": "tb_order_complaint_wechat_callback", "sql": "select * from tb_order_complaint_wechat_callback where c_complaint_no in (select c_complaint_no from tb_order_complaint_record where c_sale_order_id = '" + orderId + "')"},
                {"name": "tb_order_complaint_wechat_pay_callback", "sql": "select * from tb_order_complaint_wechat_pay_callback where c_complaint_no in (select c_complaint_no from tb_order_complaint_record where c_sale_order_id = '" + orderId + "')"},

                {"name": "tb_order_refund", "sql": "select * from tb_order_refund where c_sale_order_id = '" + orderId + "'"},
                {"name": "tb_order_refund_detail", "sql": "select * from tb_order_refund_detail where c_refund_id in (select c_refund_order_id from tb_order_refund where c_sale_order_id = '" + orderId + "')"},
                {"name": "tb_order_refund_detail_distribution_agent_info", "sql": "select * from tb_order_refund_detail_distribution_agent_info where c_refund_order_id in (select c_refund_order_id from tb_order_refund where c_sale_order_id = '" + orderId + "')"},
                {"name": "tb_order_bill", "sql": "select * from tb_order_bill where c_album_id = '" + albumId + "' and c_transaction_id in (select c_refund_order_id from tb_order_refund where c_sale_order_id = '" + orderId + "')"},

                {"name": "tb_receive_order", "sql": "select * from tb_receive_order where receive_order_id = '" + orderId + "'"},
                {"name": "tb_receive_order_extend", "sql": "select * from tb_receive_order_extend where receive_order_id = '" + orderId + "'"},
            ]

            insertSql = []
            for item in tabs:
                # print("----查询sql："+item.get("sql"))
                cursor.execute(item.get("sql"), ())
                dataList = cursor.fetchall()
                if dataList is None:
                    continue
                sqls = self.buildInsertSql(item.get("name"), dataList)
                if sqls is not None:
                    insertSql.extend(sqls)

            for sql in insertSql:
                print(sql)
        except Exception as e:
            print("组装数据异常异常" + e)
            return "组装数据异常异常"
        finally:
            mysql.mysqlClose()

        # 恢复到现网数据库
        orderMySqlPro: OrderMySqlPro = OrderMySqlPro()
        try:
            orderMyProCursor = orderMySqlPro.get_cursor()

            for sql in insertSql:
                try:
                    orderMyProCursor.execute(sql, ())
                except Exception as e:
                    continue
        except Exception as e:
            print("恢复数据异常异常: {}".format(e))
            return "恢复数据异常异常"
        finally:
            orderMySqlPro.mysqlClose()

        return "处理成功"

    def processOldCollectCodeOrder(self, orderId):
        logHeader = "【""orderId:" + orderId + "】"
        if self.getOldCollectOrderFromBank(orderId) == False:
            return "不需要处理"

        mysql: PayMySqlALi = PayMySqlALi()
        cursor = mysql.get_cursor()
        insertSql = []
        try:
            tabs: [dict] = [
                {"name": "tb_pay_collection_record", "sql": "select * from tb_pay_collection_record where c_collection_record_id = '" + orderId + "'"}
            ]

            for item in tabs:
                # print("----查询sql："+item.get("sql"))
                cursor.execute(item.get("sql"), ())
                dataList = cursor.fetchall()
                if dataList is None:
                    continue
                sqls = self.buildInsertSql(item.get("name"), dataList)
                if sqls is not None:
                    insertSql.extend(sqls)
        except Exception as e:
            print("恢复数据异常异常" + e)
            return "恢复数据异常异常"
        finally:
            mysql.mysqlClose()

        if len(insertSql) == 0:
            return "不是老的收款码订单"

        # 恢复到现网数据库
        payMySqlPro: PayMySqlPro = PayMySqlPro()
        try:
            payMyProCursor = payMySqlPro.get_cursor()

            for sql in insertSql:
                payMyProCursor.execute(sql, ())
        except Exception as e:
            print(logHeader + "恢复数据出现异常：" + e)
            return "恢复数据出现异常"
        finally:
            payMySqlPro.mysqlClose()

        return "处理成功"
