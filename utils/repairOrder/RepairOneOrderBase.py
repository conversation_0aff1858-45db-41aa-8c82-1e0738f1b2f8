from flask import make_response

from .OrderMySqlPro import OrderMySqlPro
from .OrderMysqlALi import OrderMySqlALi
from .PayMySqlPro import PayMySqlPro
from .PayMysqlALi import PayMySqlALi

"""
处理支付业务
"""


class RepairOneOrderBase:

    # 【备份库】获取支付订单ID
    def getPayOrderId(self, orderId):
        mysql: PayMySqlALi = PayMySqlALi()
        cursor = mysql.get_cursor()
        try:
            sql = "select * from tb_pay_order where c_business_order_id = '" + orderId + "'"
            cursor.execute(sql)
            payOrder: dict = cursor.fetchone()
            if payOrder is None:
                return None
            return payOrder.get("c_pay_order_id")
        except Exception as e:
            print("获取支付订单ID出现异常：" + e)
            return ""
        finally:
            mysql.mysqlClose()

    # 查询老的收款码订单
    def getOldCollectOrderFromBank(self, orderId):
        mysql: PayMySqlALi = PayMySqlALi()
        cursor = mysql.get_cursor()
        try:
            sql = "select * from tb_pay_collection_record where c_collection_record_id = '" + orderId + "'"
            cursor.execute(sql)
            order: dict = cursor.fetchone()
            return order is not None
        except Exception as e:
            print("老的收款码订单异常：" + e)
            raise e
        finally:
            mysql.mysqlClose()

    # 【备份库】获取支付账户ID
    def getAccountId(self, albumId):
        # mysql: PayMySqlPro = PayMySqlPro()
        mysql: PayMySqlALi = PayMySqlALi()
        cursor = mysql.get_cursor()
        try:
            sql = "select * from tb_account where c_business_id = '" + albumId + "'"
            cursor.execute(sql)
            account: dict = cursor.fetchone()
            if account is None:
                return None
            accountId = account.get("c_account_id")
            return accountId
        except Exception as e:
            print("获取支付账户ID出现异常：" + e)
            return ""
        finally:
            mysql.mysqlClose()

    # 【现网库】获取支付订单
    def checkPayProExist(self, payOrderId):
        mysql: PayMySqlPro = PayMySqlPro()
        cursor = mysql.get_cursor()
        try:
            sql = "select * from tb_pay_order where c_pay_order_id = '" + payOrderId + "'"
            cursor.execute(sql)
            payOrder: dict = cursor.fetchone()
            return payOrder is not None
        except Exception as e:
            print("获取支付订单ID出现异常：" + e)
            raise e
        finally:
            mysql.mysqlClose()

    def buildInsertSql(self, tabName, dataList: [dict]):
        insertSql: [] = []
        for data in dataList:
            sqlKey = "insert into " + tabName + " ("
            sqlValue = " values("
            index = 0
            for key, value in data.items():
                index += 1
                if index > 1:
                    sqlKey = sqlKey + ","
                    sqlValue = sqlValue + ","
                sqlKey = sqlKey + key
                if value is None:
                    sqlValue = sqlValue + "null"
                else:
                    sqlValue = sqlValue + "'" + str(value).replace('\n', " ").replace('\'', " ") + "'"

            sqlValue = sqlValue + ")"
            sqlKey = sqlKey + ")"
            # print(sqlKey + sqlValue + ";")
            insertSql.append(sqlKey + sqlValue + ";")
        return insertSql

    """
    查询备份库的订单
    """

    def getAlbumIdByOrderIdFromBank(self, orderId):
        mysql: OrderMySqlALi = OrderMySqlALi()

        try:
            cursor = mysql.get_cursor()

            # 先从销售单中查询
            sql = "select * from tb_sale_order where c_sale_order_id = '" + orderId + "'"
            cursor.execute(sql, ())
            data: dict = cursor.fetchone()
            if data is not None:
                return data.get("c_album_id")

            # 再从收款码订单中查询
            sql = "select * from tb_receive_order where receive_order_id = '" + orderId + "'"
            cursor.execute(sql, ())
            data: dict = cursor.fetchone()
            if data is not None:
                return data.get("album_id")

            return None
        except Exception as e:
            print(e)
        finally:
            mysql.mysqlClose()

    """
    查询现网的订单
    """

    def checkOrderInPro(self, orderId):
        mysql: OrderMySqlPro = OrderMySqlPro()

        try:
            cursor = mysql.get_cursor()

            sql = "select * from tb_sale_order where c_sale_order_id = '" + orderId + "'"
            cursor.execute(sql, ())
            data = cursor.fetchone()
            if data is not None:
                return True

            # 再从收款码订单中查询
            sql = "select * from tb_receive_order where receive_order_id = '" + orderId + "'"
            cursor.execute(sql, ())
            data: dict = cursor.fetchone()
            if data is not None:
                return True
            return False
        except Exception as e:
            print(e)
        finally:
            mysql.mysqlClose()

    def makeResponse(self, message):
        response = make_response(message)
        response.headers['Content-Type'] = 'text/html; charset=utf-8'
        return response
