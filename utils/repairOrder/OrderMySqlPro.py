import pymysql

from utils.repairOrder import config_read, env


class OrderMySqlPro:
    def __init__(self):
        self.host = config_read.get(f"OrderMysql-{env}", "host")
        self.port = int(config_read.get(f"OrderMysql-{env}", "port"))
        self.user = config_read.get(f"OrderMysql-{env}", "user")
        self.password = config_read.get(f"OrderMysql-{env}", "password")
        self.database = config_read.get(f"OrderMysql-{env}", "database")

    def get_cursor(self):
        self.mysqlConn = pymysql.connect(host=self.host, port=self.port, user=self.user, passwd=self.password, db=self.database, charset='utf8mb4')
        self.cursor = self.mysqlConn.cursor(pymysql.cursors.DictCursor)
        return self.cursor

    def mysqlClose(self):
        self.mysqlConn.commit()
        if self.cursor:
            self.cursor.close()
        self.mysqlConn.close()
