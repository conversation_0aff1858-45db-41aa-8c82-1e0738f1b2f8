import pymysql
from sshtunnel import SSHTunnelForwarder

from utils.repairOrder import config_read, env


class OrderMySqlALi:
    # 初始化函数，初始化连接列表
    def __init__(self):
        self.host = '127.0.0.1'
        self.port = 3306
        self.user = 'root'
        self.pwd = ''
        self.dbname = 'wg_tongs'

    # 获取数据库游标对象cursor
    # 游标对象：用于执行查询和获取结果
    def get_cursor(self):
        # ssh 登录跳板机
        self.server = SSHTunnelForwarder(
            ssh_address_or_host=(OrderCfg.SSH.HOST, OrderCfg.SSH.PORT),  # 指定ssh登录的跳转机的address
            ssh_username=OrderCfg.SSH.USER,  # 跳转机的用户
            # ssh_password='*****',  # 跳转机的密码
            local_bind_address=(OrderCfg.LOCAL_MYSQL.HOST, OrderCfg.LOCAL_MYSQL.PORT),  # 映射到本机的地址和端口
            remote_bind_address=(self.host, self.port),  # 数据库的地址和端口
            ssh_pkey=OrderCfg.SSH.PRIVATE_KEY)  # rsa 私钥路径
        self.server.start()

        # 建立数据库连接
        self.db = pymysql.connect(
            user=self.user,
            passwd=self.pwd,
            # host=host,
            host=OrderCfg.LOCAL_MYSQL.HOST,  # 映射地址local_bind_address ip
            port=OrderCfg.LOCAL_MYSQL.PORT,  # 映射地址local_bind_address port
            db=self.dbname,  # 需要连接的实例名
            cursorclass=pymysql.cursors.DictCursor)

        # 创建游标对象
        cursor = self.db.cursor(pymysql.cursors.DictCursor)

        # 返回
        return cursor

    def close(self):
        self.db.cursor().close()
        self.db.close()
        self.server.close()

    def mysqlClose(self):
        self.db.commit()
        self.db.cursor().close()
        self.db.close()
        self.server.close()


class OrderCfg:
    class MYSQL_TEST:
        HOST = "************"
        PORT = 3306
        USER = "root"
        PWD = ""
        DB = "wg_tongs"

    class SSH:
        HOST = "************"
        PORT = 22
        USER = "root"
        PRIVATE_KEY = config_read.get(f"PRIVATE-KEY-{env}", "PRIVATE_KEY")

    # 映射到本地mysql 地址，端口
    class LOCAL_MYSQL:
        HOST = "127.0.0.1"
        PORT = 1268

#
# if __name__ == '__main__':
#     mysql = OrderMySqlALi()
#     cursor = mysql.get_cursor()
#     sql: str = "SHOW TABLES;"
#     cursor.execute(sql, ())
#     data = cursor.fetchone()
#     mysql.close()
#     print(data)
