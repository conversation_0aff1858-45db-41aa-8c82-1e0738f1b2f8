#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
@Title:     ssobackend.py
@Date:      2022/7/26 16:30
@Author:    wanglh
@Desc：     sso权限认证后端实现函数
"""

import json

import requests
from flask import current_app
from sqlalchemy import and_, func, or_

from model import *
from .bluekingapi import BlueKings


def ssoRsync():
    bk = BlueKings()
    bk.get_job_list()
    data = {
        "bk_app_code": 'wegoops',
        "bk_app_secret": '1186e36a-b156-42f0-8724-e6afd64fdc59',
        "bk_username": 'admin',
        "bk_biz_id": 3,
        "bk_job_id": 1000131,
        "global_vars": [
            {  # 全局变量信息
                "id": 1000096,
                "target_server": {  # 目标服务器
                    "ip_list": [{  # 主机IP列表
                        "bk_cloud_id": 0,  # 云区域ID
                        "ip": "**********"  # 主机IP
                    }]
                }}
        ]
    }
    print("data:", data)
    url = "https://paas.in.szwego.com/api/c/compapi/v2/job/execute_job/"
    rs = requests.post(url, data=json.dumps(data))
    return (rs.text)


def sso_getlimit(pageSize, pageNo, userName=''):
    task_filter = {
        and_(
            or_(
                SsousersModel.username.like('%{}%'.format(userName)),
                SsousersModel.displayname.like('%{}%'.format(userName)),
                SsousersModel.email.like('%{}%'.format(userName)),
            ),
            # SsousersModel.status == 1
        )
    }
    totalCount = db.session.query(func.count(SsousersModel.id)).filter(*task_filter).scalar()
    select_sql = """
        SELECT
                ANY_VALUE ( u.id ) AS id,
                ANY_VALUE ( u.status ) AS status,
                ANY_VALUE ( u.update_time ) AS update_time,
                u.username,
                u.displayname,
                u.email,
                IFNULL(GROUP_CONCAT(IF(r.role_desc IS NULL, NULL, r.role_desc )),"") role,
                IFNULL(GROUP_CONCAT(IF(a.access IS NULL, NULL, a.access )),"") access 
        FROM
                sso_users u
                LEFT JOIN sso_user_role_access ua ON ua.user_id = u.id
                LEFT JOIN sso_role r ON ua.roleacc_id = r.id         AND ua.type = 0
                LEFT JOIN sso_access a ON ua.roleacc_id = a.id         AND ua.type = 1 
        WHERE
                (username like '%{userName}%' OR displayname like '%{userName}%' OR email like '%{userName}%') and (status = 1 or status = 2)
        GROUP BY
                u.username,
                u.displayname,
                u.email 
        ORDER BY
                ANY_VALUE ( u.status ) ASC, 
                ANY_VALUE ( u.update_time ) DESC 
                LIMIT {pageIndex},
                {pageOffset}
    """.format(userName=str(userName), pageIndex=int(pageSize) * (int(pageNo) - 1), pageOffset=int(pageSize))
    sql_data = db.session.execute(select_sql).fetchall()
    db.session.close()
    column = ('id', 'status', 'update_time', 'username', 'displayname', 'email', 'role', 'access')
    sql_data = [dict(zip(column, i)) for i in sql_data]
    for data in sql_data:
        data["status"] = True if data["status"] == 1 else False
        data["update_time"] = data["update_time"].strftime("%Y-%m-%d %H:%M:%S")
    for data in sql_data:
        if data["role"] is not None:
            data["role"] = data["role"].split(',')
        if data["access"] is not None:
            data["access"] = data["access"].split(',')
    return totalCount, sql_data


def sso_getroles():
    select_role = """SELECT id, IFNULL(NULL, 'role') as type, rolename, role_desc FROM sso_role;"""
    select_access = """SELECT id+10000 AS id, IFNULL(NULL, 'access') as type, access as rolename, description as role_desc FROM sso_access;"""
    column = ('id', 'type', 'rolename', 'role_desc')
    role_list = db.session.execute(select_role).fetchall()
    access_list = db.session.execute(select_access).fetchall()
    role_list = [dict(zip(column, i)) for i in role_list]
    access_list = [dict(zip(column, i)) for i in access_list]
    roles = role_list + access_list
    return len(roles), roles


def _now(format="%Y-%m-%d %H:%M:%S"):
    return datetime.now().strftime(format)


class SsoUserroles:
    # 获取用户相应规则组装插入语句
    def userroleaccess(self):
        roles_sql = """SELECT id as roleacc_id, 0 as type, role_desc as name FROM sso_role;"""  # type：0 表示的所属部门端
        access_sql = """SELECT id as roleacc_id, 1 as type, access as name FROM sso_access;"""  # type；1 表示选择的应用

        column = ('roleacc_id', 'type', 'name')
        roles_data = db.session.execute(roles_sql).fetchall()
        roles_data = [dict(zip(column, i)) for i in roles_data]
        access_data = db.session.execute(access_sql).fetchall()
        access_data = [dict(zip(column, i)) for i in access_data]
        roles_access = roles_data + access_data
        roleaccess = {i["name"]: {"roleacc_id": i["roleacc_id"], "type": i["type"]} for i in roles_access}
        return roleaccess

    def main(self, **kwargs):

        username = kwargs.get("username", None)
        email = kwargs.get("email", None)
        displayname = kwargs.get("displayname", username)
        roles = kwargs.get("role", None)
        access = kwargs.get("access", None)
        local_host = current_app.config.get('LOCAL_HOST')
        local_port = current_app.config.get('LOCAL_PORT')
        if 'status' in list(kwargs.keys()):
            status = 1 if kwargs['status'] == True else 2
        else:
            status = 2

        role_data = roles + access
        role_data = [i for i in role_data if i != '']

        print(role_data)

        def select(name):
            select_query = SsousersModel.query.filter_by(username=name).all()
            sql_data = SsousersModel.to_all_json(select_query)
            return sql_data

        sql_data = select(username)
        values = (username, "*", displayname, email, _now(), _now(), status)
        insert_user_sql = """INSERT INTO sso_users (username,password,displayname,email,create_time,update_time,status) values {}""".format(
            values)
        roleaccess = self.userroleaccess()

        try:
            if sql_data:
                id = int(sql_data[0]['id'])
                # 更新用户信息表
                update_user_sql = """UPDATE sso_users SET displayname='{}',email='{}',update_time='{}',status={} WHERE id={}""".format(
                    displayname, email, _now(), status, id)
                # 删除用户权限信息表
                delete_userid = """DELETE FROM sso_user_role_access WHERE user_id={}""".format(id)
                db.session.execute(update_user_sql)
                db.session.execute(delete_userid)
                current_app.logger.info(f'{username}用户信息更新成功')
                if 'sql' in role_data:
                    payload = {
                        "username": username,
                        "displayname": displayname,
                        "email": email
                    }
                    print(f"更新yearning信息参数：{payload}")
                    res = requests.post(f"http://{local_host}:{local_port}/user/api/v1/yearning/update", data=payload)
                    current_app.logger.info(f'{res.json()["msg"]}')
            else:
                # 插入user表信息
                current_app.logger.info(f'{username}用户信息不存在，进行写入...')
                db.session.execute(insert_user_sql)

            # 重新查询，username的id并插入权限数据
            sql_data = select(username)
            user_id = sql_data[0]["id"]
            data = ""
            if role_data:
                for role in role_data:
                    type = roleaccess[role]["type"]
                    roleacc_id = roleaccess[role]["roleacc_id"]
                    data += str((user_id, roleacc_id, type)) + ','
                data = data[:-1]
                insert_uar_sql = """INSERT INTO sso_user_role_access (`user_id`, `roleacc_id`, `type`) VALUES {}""".format(
                    data)
                current_app.logger.info("数据写入sso_user_role_access....")
                db.session.execute(insert_uar_sql)

            # 提交事务
            db.session.commit()
            current_app.logger.info(f'{username}用户权限信息写入成功')
            return "{}用户信息写入成功".format(username)

        except Exception as e:
            db.session.rollback()
            current_app.logger.error(f"{username}用户权限信息写入失败，具体报错信息：\n{e}")
            return str(e)
        finally:
            db.session.close()
