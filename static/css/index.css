.question-ul li {
    padding: 10px;
    overflow: hidden;
    display: flex;
    background-color: #fff;
    border-bottom: 1px solid #eee;
}

.side-question {
    flex-basis: 38px;
    height: 100%;
}

.side-question-avatar {
    width: 38px;
    height: 38px;
    border-radius: 3px;
}

.question-main {
    flex: 1;
    width: 660px;
    margin-left: 10px;
    overflow: hidden;
}

.question-title a {
    color: #259;
    font-size: 14px;
    font-weight: 900;
}

.question-author {
    font-size: 12px;
    margin-top: 5px;
}

.question-content {
    margin-top: 5px;
    font-size: 12px;
}

.question-detail {
    text-align: right;
    margin-top: 10px;
}

.question-detail .question-author {
    margin-right: 10px;
}