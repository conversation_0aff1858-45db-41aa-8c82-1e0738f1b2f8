#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
@Title:   restart_docker.py
@Date:    2022/5/25 14:52
@Author:  wanglh
@Desc:    重启docker
"""

import json
import sys

import requests


def executejob(service, imageaddress):
    kwargs = {
        "bk_app_code": 'wegoops',
        "bk_app_secret": '1186e36a-b156-42f0-8724-e6afd64fdc59',
        "bk_username": 'admin',
        "bk_biz_id": 3,
        "bk_job_id": 1000165,
        "global_vars": [
            {  # 全局变量信息
                "id": 1000096,
                "target_server": {  # 目标服务器
                    "ip_list": [{  # 主机IP列表
                        "bk_cloud_id": 0,  # 云区域ID
                        "ip": "'***********"  # 主机IP
                    }]
                }},
            {
                "id": 1000173,
                "value": service
            },
            {
                "id": 1000174,
                "value": imageaddress
            }
        ]
    }

    url = "https://paas.in.szwego.com/api/c/compapi/v2/job/execute_job/"
    rs = requests.post(url, data=json.dumps(kwargs))
    print(rs.json())


if __name__ == "__main__":
    service = sys.argv[1]
    imageaddress = sys.argv[2]
    executejob(service, imageaddress)
