# get_alert_panel 接口测试文档

## 概述

本目录包含了 `get_alert_panel` 接口的全面测试用例，确保接口的正确性和健壮性。

## 测试覆盖范围

### 1. 正常功能测试
- ✅ 有效时间参数和正常告警数据处理
- ✅ 返回数据结构符合前端 HealthPanel 组件要求
- ✅ 不同段数的 policy_name 解析（3段、4段、5段式）
- ✅ 产品分类映射逻辑验证

### 2. 参数验证测试
- ✅ 缺少必填参数（start_time、end_time）的错误处理
- ✅ 无效时间格式处理
- ✅ 空参数值处理

### 3. 数据解析测试
- ✅ policy_name 格式错误处理（段数不足、缺少分隔符、空值等）
- ✅ labels 字段为空或格式错误处理
- ✅ 产品分类映射失败的降级处理

### 4. 边界条件测试
- ✅ 空告警数据集处理
- ✅ 数据库查询异常处理
- ✅ 大量数据性能测试

### 5. 状态映射测试
- ✅ 数据库状态（ALARM/OK/NO_DATA/NO_CONF）到前端健康状态映射
- ✅ 不同 severity 级别正确处理

### 6. 集成测试
- ✅ 模拟真实数据库环境的端到端测试
- ✅ 与 TencentCateProduct 表的正确关联

## 文件结构

```
tests/
├── __init__.py              # 测试包初始化
├── conftest.py              # pytest配置和共享fixtures
├── test_alerts_center.py    # 主要测试用例
├── test_runner.py           # 测试运行器
└── README.md               # 本文档
```

## 运行测试

### 安装依赖

```bash
pip install pytest pytest-mock pytest-cov
```

### 运行所有测试

```bash
# 方法1：使用pytest直接运行
pytest tests/ -v

# 方法2：使用测试运行器
python tests/test_runner.py
```

### 运行特定测试

```bash
# 运行特定测试类
pytest tests/test_alerts_center.py::TestGetAlertPanel -v

# 运行特定测试方法
pytest tests/test_alerts_center.py::TestGetAlertPanel::test_missing_required_parameters -v

# 使用测试运行器运行特定测试
python tests/test_runner.py --test TestGetAlertPanel::test_missing_required_parameters
```

### 运行带覆盖率的测试

```bash
# 使用测试运行器
python tests/test_runner.py --coverage

# 或直接使用pytest
pytest tests/ --cov=blueprint.moniterCenter.alerts_center --cov-report=html --cov-report=term-missing
```

## 测试用例说明

### TestGetAlertPanel 类

| 测试方法 | 描述 | 验证点 |
|---------|------|--------|
| `test_missing_required_parameters` | 测试缺少必填参数 | 错误响应、状态码400 |
| `test_successful_request_with_valid_data` | 测试正常请求 | 响应结构、数据正确性 |
| `test_policy_name_parsing_3_segments` | 测试policy_name解析 | 3-5段式解析正确性 |
| `test_policy_name_parsing_invalid_formats` | 测试无效格式处理 | 错误处理、日志记录 |
| `test_status_mapping` | 测试状态映射 | 状态转换正确性 |
| `test_empty_alerts_data` | 测试空数据处理 | 空结果处理 |
| `test_invalid_labels_handling` | 测试无效labels | 异常数据处理 |
| `test_product_category_mapping_fallback` | 测试分类映射降级 | 降级逻辑正确性 |
| `test_database_query_exception` | 测试数据库异常 | 异常处理、错误响应 |
| `test_large_dataset_performance` | 测试大数据集性能 | 性能要求、响应时间 |
| `test_date_range_generation` | 测试日期范围生成 | 日期逻辑正确性 |
| `test_mixed_valid_invalid_alerts` | 测试混合数据 | 数据过滤正确性 |
| `test_response_data_structure_compliance` | 测试响应结构 | 前端兼容性 |
| `test_logging_behavior` | 测试日志行为 | 日志记录正确性 |
| `test_edge_case_datetime_handling` | 测试边界日期处理 | 边界条件处理 |

## Mock 数据说明

### sample_alerts fixture
提供各种类型的告警数据：
- 正常的5段式policy_name告警
- 4段式policy_name告警  
- 3段式policy_name告警
- 格式错误的policy_name告警
- 空labels告警
- 缺少policy_name的告警

### sample_category_products fixture
提供产品分类映射数据：
- cvm -> 计算
- rds -> 数据库
- cdn -> 网络

## 验证要点

1. **接口稳定性**: 确保异常数据不会导致接口崩溃
2. **日志记录**: 验证各种情况下的日志记录正确性
3. **数据格式**: 确保返回的JSON格式完全符合前端要求
4. **统计准确性**: 验证统计数据的计算正确性
5. **性能要求**: 确保大数据集下的响应时间合理
6. **错误处理**: 验证各种错误情况的优雅处理

## 持续集成

建议在CI/CD流程中集成这些测试：

```yaml
# .github/workflows/test.yml 示例
name: Test get_alert_panel API
on: [push, pull_request]
jobs:
  test:
    runs-on: ubuntu-latest
    steps:
    - uses: actions/checkout@v2
    - name: Set up Python
      uses: actions/setup-python@v2
      with:
        python-version: 3.9
    - name: Install dependencies
      run: |
        pip install -r requirements.txt
        pip install pytest pytest-mock pytest-cov
    - name: Run tests
      run: python tests/test_runner.py --coverage
```
