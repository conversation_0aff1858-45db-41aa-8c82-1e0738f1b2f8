#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
@Title:     conftest.py
@Date:      2025/7/30
@Author:    AI Assistant
@Desc:      pytest配置文件和共享fixtures
"""

import pytest
from unittest.mock import Mock, patch
from datetime import datetime, timedelta
import json
import sys
import os

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from flask import Flask

# 尝试导入模型，如果失败则创建Mock
try:
    from model import MonitoringCenterAlert, TencentCateProduct
except ImportError:
    # 如果导入失败，创建Mock类
    class MonitoringCenterAlert:
        def __init__(self):
            self.policy_name = None
            self.status = None
            self.severity = None
            self.labels = None
            self.created_time = None

    class TencentCateProduct:
        def __init__(self):
            self.product_name = None
            self.category_name = None


@pytest.fixture
def app():
    """创建测试用的Flask应用"""
    app = Flask(__name__)
    app.config['TESTING'] = True
    app.config['WTF_CSRF_ENABLED'] = False
    # 添加SQLAlchemy必需的配置
    HOSTNAME = "***********"
    PORT = "3306"
    DATABASE = "wego_ops_bak"
    USERNAME = "loonflow"
    PASSWORD = "Ccwo17k92Zw5"
    DB_URI = "mysql+pymysql://{}:{}@{}:{}/{}?charset=utf8".format(
        USERNAME, PASSWORD, HOSTNAME, PORT, DATABASE
    )
    app.config['SQLALCHEMY_DATABASE_URI'] = DB_URI
    app.config['SQLALCHEMY_TRACK_MODIFICATIONS'] = False
    app.config['SQLALCHEMY_ECHO'] = False
    
    # 初始化数据库扩展
    from extension import db
    db.init_app(app)
    
    # 注册蓝图 - 添加错误处理
    try:
        from blueprint.moniterCenter.alerts_center import alerts_center_bp
        app.register_blueprint(alerts_center_bp)
    except ImportError as e:
        # 如果导入失败，创建一个简单的蓝图用于测试
        from flask import Blueprint
        alerts_center_bp = Blueprint('alerts_center', __name__)
        app.register_blueprint(alerts_center_bp)
    
    return app


@pytest.fixture
def client(app):
    """创建测试客户端"""
    return app.test_client()


@pytest.fixture
def mock_db_session():
    """模拟数据库会话"""
    with patch('extension.db.session') as mock_session:
        yield mock_session


@pytest.fixture
def sample_alerts(app):
    """创建示例告警数据 - 在app上下文中运行"""
    with app.app_context():
        alerts = []
        
        # 创建完整的示例数据
        alert_data = [
            {
                'policy_name': 'cvm-compute-core-critical-monitoring',
                'status': 'ALARM',
                'severity': 'critical',
                'labels': '{"instance": "i-123", "region": "ap-beijing"}',
                'created_time': datetime(2024, 1, 15, 10, 0, 0)
            },
            {
                'policy_name': 'rds-database-core-warning',
                'status': 'ALARM', 
                'severity': 'warning',
                'labels': '{"instance": "rds-456", "region": "ap-shanghai"}',
                'created_time': datetime(2024, 1, 15, 11, 0, 0)
            },
            {
                'policy_name': 'cdn-network-noncore',
                'status': 'OK',
                'severity': 'info',
                'labels': '{"domain": "example.com"}',
                'created_time': datetime(2024, 1, 15, 12, 0, 0)
            }
        ]
        
        for data in alert_data:
            alert = Mock()
            for key, value in data.items():
                setattr(alert, key, value)
            alerts.append(alert)
        
        return alerts


@pytest.fixture
def sample_category_products(app):
    """创建示例产品分类数据"""
    with app.app_context():
        products = []
        
        product1 = Mock()
        product1.product_name = "cvm"
        product1.category_name = "计算"
        products.append(product1)
        
        product2 = Mock()
        product2.product_name = "rds" 
        product2.category_name = "数据库"
        products.append(product2)
        
        product3 = Mock()
        product3.product_name = "cdn"
        product3.category_name = "网络"
        products.append(product3)
        
        return products


@pytest.fixture
def mock_current_app(app):
    """模拟current_app - 需要在app上下文中运行"""
    with app.app_context():
        with patch('blueprint.moniterCenter.alerts_center.current_app') as mock_app:
            mock_app.logger = Mock()
            mock_app.logger.debug = Mock()
            mock_app.logger.warning = Mock()
            mock_app.logger.error = Mock()
            yield mock_app


@pytest.fixture
def mock_format_datetime():
    """模拟format_datetime函数"""
    with patch('blueprint.moniterCenter.alerts_center.format_datetime') as mock_func:
        mock_func.side_effect = lambda dt: dt.strftime("%Y-%m-%d %H:%M:%S") if dt else None
        yield mock_func
