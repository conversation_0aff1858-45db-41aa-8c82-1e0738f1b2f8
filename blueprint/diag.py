#!/usr/bin/env python3
# -*- coding: utf-8 -*-

from flask import Blueprint, abort, request, jsonify, current_app
from flask.views import MethodView
from sqlalchemy import func, case, desc, not_, and_, or_, distinct
from datetime import datetime, timedelta, timezone
from utils import *
import traceback
import base64
import json


# 定义模型
class KafkaEventNotify(db.Model):
    """Kafka事件通知表模型"""
    __tablename__ = 'kafka_event_notify'

    id = db.Column(db.BigInteger, primary_key=True, autoincrement=True)
    event = db.Column(db.String(255), nullable=False)
    time = db.Column(db.BigInteger, default=0)
    albumId = db.Column(db.String(200), default='', index=True)
    reqid = db.Column(db.String(200), default='', index=True)
    source_ip = db.Column(db.String(255), default='')
    details = db.Column(db.Text)
    create_time = db.Column(db.DateTime, default=datetime.now, index=True)

    def to_dict(self):
        """将模型转换为字典"""
        return {
            'id': self.id,
            'event': self.event,
            'time': self.time,
            'albumId': self.albumId,
            'reqid': self.reqid,
            'source_ip': self.source_ip,
            'details': self.details,
            'create_time': self.create_time.strftime('%Y-%m-%d %H:%M:%S') if self.create_time else None
        }

# 创建蓝图
diag_bp = Blueprint('diag_api', __name__, url_prefix='/toolbox/api/v1/diag')

HTTP_NOT_FOUND = 404
HTTP_SERVER_ERROR = 500
HTTP_SUCCESS = 200
HTTP_BAD_REQUEST = 400

def get_response(req):
    """从请求中获取参数，支持GET和POST请求"""
    if req.method == 'GET':
        return req.args
    else:
        if req.is_json:
            return req.get_json()
        else:
            return req.form

class BaseApi(MethodView):
    """API基类"""
    def __init__(self):
        super().__init__()
        self.applicant = request.headers.get('X-User', 'unknown')

class DiagnosisResultAPI(BaseApi):
    """诊断结果API，处理诊断数据的查询"""

    def get(self, diagnosis_id=None):
        """获取诊断结果，支持按ID查询或列表查询"""
        if diagnosis_id:
            return self.get_diagnosis_by_id(diagnosis_id)
        else:
            return self.get_diagnosis_list()

    def get_diagnosis_by_id(self, diagnosis_id):
        """根据ID查询诊断结果"""
        try:
            # 使用SQLAlchemy查询
            result = KafkaEventNotify.query.filter_by(
                event='user_diagnose',
                reqid=diagnosis_id
            ).order_by(KafkaEventNotify.create_time.desc()).first()

            if not result:
                return jsonify({"error": "未找到诊断记录"}), HTTP_NOT_FOUND

            # 解码Base64编码的details字段
            details_data = []
            if result.details:
                try:
                    # Base64解码
                    #decoded_data = base64.b64decode(result.details).decode('gbk')
                    details_data = json.loads(result.details)
                except Exception as e:
                    current_app.logger.error(f"解析诊断数据失败: {str(e)}")
                    return jsonify({"error": f"解析诊断数据失败: {str(e)}"}), HTTP_SERVER_ERROR

            # 构建响应
            response = {
                "data": {
                    "id": result.id,
                    "reqid": result.reqid,
                    "time": result.time,
                    "source_ip": result.source_ip,
                    "create_time": result.create_time.strftime('%Y-%m-%d %H:%M:%S') if result.create_time else None,
                    "details": details_data
                }
            }
            return jsonify(response), HTTP_SUCCESS

        except Exception as e:
            current_app.logger.error(f"获取诊断数据异常: {str(e)}")
            current_app.logger.error(traceback.format_exc())
            return jsonify({"error": f"获取诊断数据失败: {str(e)}"}), HTTP_SERVER_ERROR

    def get_diagnosis_list(self):
        """获取诊断列表"""
        data = get_response(request)
        page_no = int(data.get("pageNo", 1))
        page_size = int(data.get("pageSize", 10))
        keyword = data.get("keyword", "")
        start_date = data.get("startDate")
        end_date = data.get("endDate")

        try:
            # 构建查询
            query = KafkaEventNotify.query.filter_by(event='user_diagnose')

            if keyword:
                query = query.filter(
                    or_(
                        KafkaEventNotify.reqid.like(f"%{keyword}%"),
                        KafkaEventNotify.source_ip.like(f"%{keyword}%")
                    )
                )

            if start_date and end_date:
                query = query.filter(
                    and_(
                        KafkaEventNotify.create_time >= f"{start_date} 00:00:00",
                        KafkaEventNotify.create_time <= f"{end_date} 23:59:59"
                    )
                )

            # 获取总记录数
            total = query.count()

            # 分页查询
            results = query.order_by(KafkaEventNotify.create_time.desc()) \
                .limit(page_size).offset((page_no - 1) * page_size).all()

            # 转换为字典列表
            results_dict = [item.to_dict() for item in results]

            return jsonify({
                "data": results_dict,
                "total": total,
                "pageNo": page_no,
                "pageSize": page_size
            }), HTTP_SUCCESS

        except Exception as e:
            current_app.logger.error(f"获取诊断列表异常: {str(e)}")
            current_app.logger.error(traceback.format_exc())
            return jsonify({"error": f"获取诊断列表失败: {str(e)}"}), HTTP_SERVER_ERROR

# 注册路由
diagnosis_api = DiagnosisResultAPI.as_view('diagnosis_api')
diag_bp.add_url_rule('/<diagnosis_id>', view_func=diagnosis_api, methods=['GET'])
diag_bp.add_url_rule('', view_func=diagnosis_api, methods=['GET'])