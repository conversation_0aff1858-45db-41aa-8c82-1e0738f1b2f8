#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
@Title:   blueking.py
@Date:    2022/4/12 17:15
@Author:  wanglh
@Desc:    蓝鲸作业作业平台
"""

from flask import Blueprint, render_template, request

from utils import *

blueking_bp = Blueprint('blueking', __name__, url_prefix='/blueking/api/v1')
application_bp = Blueprint('application', __name__, url_prefix='/application/api/v1')


@blueking_bp.route('/index')
def bp_index():
    """
    蓝鲸作业平台执行任务列表
    :return: html
    """
    bk = BlueKings()
    result = bk.get_job_list()
    result = [{"bk_biz_id": i["bk_biz_id"], "id": i["id"], "name": i["name"]} for i in result]
    for i in result:
        if i["bk_biz_id"] == 3:
            i["bk_biz_id"] = "微购科技-生产"
    current_app.logger.info("蓝鲸平台列表查询完成")
    result = sorted(result, key=lambda result: result["id"], reverse=True)  # reverse 逆序  lamdba 匿名函数
    return render_template("bkjoblist.html", result=result)


@application_bp.route('/index')
def application_index():
    """
    权限信息首页
    :return: html

    """
    try:
        select_index_sql = """SELECT * FROM sso_application"""
        query = db.session.execute(select_index_sql)
        data = query.fetchall()
        column = ("id", "title", "link", "desc", "img", "groups", "type")
        data = [dict(zip(column, i)) for i in data]
        for data_dict in data:
            data_dict["id"] = str(data_dict["id"])
        return jsonify({"code": 200, "data": data})
    except Exception as e:
        return jsonify({"code": 500, "msg": str(e)})


@application_bp.route('/index/user')
def user_application_index():
    user = request.headers.get('X-User')
    try:
        select_sql = """
            SELECT
                    IFNULL(GROUP_CONCAT(IF(r.role_desc IS NULL, NULL, r.rolename )),'') rolename,
                    IFNULL(GROUP_CONCAT(IF(a.access IS NULL, NULL, a.access )),'') access
            FROM
                    sso_users u
                    LEFT JOIN sso_user_role_access ua ON ua.user_id = u.id
                    LEFT JOIN sso_role r ON ua.roleacc_id = r.id         AND ua.type = 0
                    LEFT JOIN sso_access a ON ua.roleacc_id = a.id         AND ua.type = 1
            WHERE
                    username = '{}'
            GROUP BY
                    u.username,
                    u.displayname,
                    u.email,
                    u.update_time
            ORDER BY
                    ANY_VALUE ( u.update_time ) DESC
                    LIMIT 0,100""".format(str(user))
        query = db.session.execute(select_sql)
        groups = ""
        for i in query.fetchall():
            groups = groups + ",".join(i)
        groups = groups.split(',')
        result = SsoApplication.query.filter_by().all()
        data = SsoApplication.to_all_json(result)
        result = [item for item in data if set(item["groups"].split(',')) & set(groups)]
        current_app.logger.info("主页信息获取成功")
        return jsonify({"code": 200, "data": result})
    except Exception as e:
        current_app.logger.error("主页信息获取失败，可能是MySQL执行失败，或者MySQL数据库结构发生改变")
        return jsonify({"code": 500, "msg": str(e)})


@application_bp.route('/asyncpre', methods=['POST'])
def async_pre_cnf():
    """
    同步 预发布 1 配置到预发布 2
    :return: html
    """
    data_raw = get_response(request)
    # 重新组装 request
    creator_value = "jiyelin" if data_raw.get("creator") == "" else data_raw.get("creator")
    new_headers = {
        'X-User': creator_value
    }
    new_data = json.dumps(data_raw, ensure_ascii=False)  # Convert data_raw to JSON
    new_request = request.environ['werkzeug.request'].from_values(
        '/asyncpre', method='POST', data=new_data, headers=new_headers
    )

    try:
        msg = execute_job_1000210()
        save_to_operation_log(new_request, action="预发布配置同步", result=200)
        return jsonify({"msg": msg}), 200
    except Exception as e:
        save_to_operation_log(new_request, action="预发布配置同步", result=500)
        return jsonify({"msg": str(e)}), 500
