#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
@Title:     xlogcrypt.py
@Date:      2024/10/16 14:41
@Author:    wanglh
@Desc:      xlog 日志解密
"""

import os
import re
import subprocess
import sys
from configparser import ConfigParser
from pathlib import Path
from urllib.parse import urlencode

import paramiko
import pymysql
from flask import Blueprint, abort, current_app, jsonify, request, send_file  # 引入 jsonify 用于返回 JSON 响应
from scp import SCPClient
from sqlalchemy import desc

from extension import db
from model import AppLogDetail, AppLogList, SsousersModel
from utils import get_time_stamp
from utils.repairOrder import env

REMOTE_SERVER = '***********'
REMOTE_USERNAME = 'root'
REMOTE_PATH = '/data/wanglh/decode_crypt'
SCRIPT_PATH = '/data/wanglh/decode_crypt/decode_mars_crypt_log_file.py'

current_path = os.path.abspath(__file__)
current_dir = os.path.dirname(current_path)
config_path = os.path.join(os.path.dirname(current_dir), 'config.ini')
config_read = ConfigParser()
config_read.read(config_path)

SSH_KEY_PATH = config_read.get(f"PRIVATE-KEY-{env}", "PRIVATE_KEY")

UPLOADS_PATH = os.path.join(os.path.dirname(current_dir), 'static', 'uploads')
CRYPT_LOGS_PATH = os.path.join(os.path.dirname(current_dir), 'static', 'crypt')

# 确保上传文件目录存在
os.makedirs(UPLOADS_PATH, exist_ok=True)
os.makedirs(CRYPT_LOGS_PATH, exist_ok=True)

xlog_crypt_bp = Blueprint('xlog_crypt', __name__, url_prefix='/xlog/api/v1')


def create_cnx():
    db_config = {
        'host': "***********",
        'user': "loonflow",
        'password': "Ccwo17k92Zw5",
        'database': "wego_ops"
    }
    cnx = pymysql.connect(**db_config)
    return cnx


def insert_file_into_db(filename, owner, status=0):
    try:
        with create_cnx() as cnx:
            cursor = cnx.cursor()
            add_file = "INSERT INTO applog_list (filename, owner, status) VALUES (%s, %s, %s)"
            data_file = (filename, owner, status)
            cursor.execute(add_file, data_file)
            file_id = cursor.lastrowid  # 获取自增 ID
            cnx.commit()
            cursor.close()
        return file_id

    except pymysql.Error as err:
        print(f"数据库错误: {err}")
        return None


def delete_file_into_db(file_id):
    try:
        with create_cnx() as cnx:
            cursor = cnx.cursor()
            delete_sql = f"DELETE FROM applog_list WHERE id={int(file_id)};"
            try:
                cursor.execute(delete_sql)
                cnx.commit()  # Commit if no errors
            except pymysql.Error as err:
                print(f"数据删除出错: {err}")
                cnx.rollback()  # Rollback in case of error
                return False
            finally:
                cursor.close()
        return True

    except Exception as e:
        print(f"Unexpected error: {e}")
        return False


def create_ssh_client():
    """创建并返回 SSH 客户端"""
    ssh = paramiko.SSHClient()
    ssh.set_missing_host_key_policy(paramiko.AutoAddPolicy())
    ssh.connect(REMOTE_SERVER, username=REMOTE_USERNAME, key_filename=SSH_KEY_PATH)
    return ssh


def upload_to_remote(local_file):
    """将文件上传到远程服务器"""
    ssh = create_ssh_client()
    with SCPClient(ssh.get_transport()) as scp:
        scp.put(local_file, os.path.join(REMOTE_PATH, os.path.basename(local_file)))

    ssh.close()


def run_remote_script(filename):
    """在远程服务器上运行解密脚本"""
    ssh = create_ssh_client()

    command = f'python {SCRIPT_PATH} {os.path.join(REMOTE_PATH, filename)}'
    stdin, stdout, stderr = ssh.exec_command(command)
    stdout.channel.recv_exit_status()

    log_file_name = f"{filename}.log"
    log_file_path = os.path.join(REMOTE_PATH, log_file_name)

    ssh.close()
    return log_file_path


def download_log_file(remote_log_path, file_id):
    """从远程服务器下载日志文件"""
    ssh = create_ssh_client()

    file_dir = os.path.join(CRYPT_LOGS_PATH, str(file_id))
    if not os.path.exists(file_dir):
        os.makedirs(file_dir, exist_ok=True)

    with SCPClient(ssh.get_transport()) as scp:
        scp.get(remote_log_path, os.path.join(file_dir, os.path.basename(remote_log_path)))

    ssh.close()


def clear_uploads_directory():
    """清空上传文件目录"""
    for filename in os.listdir(UPLOADS_PATH):
        file_path = os.path.join(UPLOADS_PATH, filename)
        try:
            if os.path.isfile(file_path) or os.path.islink(file_path):
                os.unlink(file_path)
        except Exception as e:
            print(f'Failed to delete {file_path}. Reason: {e}')


def process_log_async(filename, file_id):
    """
    :param file_id: 文件 ID
    :param filename: 日志文件名
    """
    # 获取项目根目录和脚本路径
    import datetime
    base_dir = Path(__file__).resolve().parent.parent
    script_path = base_dir / "utils/crypt/cryptSaveMysql.py"
    timestamp = datetime.datetime.now().strftime('%Y%m%d_%H%M%S')

    log_path = base_dir / f"utils/crypt/cryptSaveMysql_{file_id}_{timestamp}.log"
    current_app.logger.info(f"执行本地脚本：{script_path}")
    current_app.logger.info(f"日志文件：{log_path}")

    # 检查脚本路径是否存在
    if not script_path.exists():
        error_message = f"脚本路径不存在: {script_path}"
        current_app.logger.error(error_message)
        raise FileNotFoundError(error_message)

    # 构造命令
    python_executable = sys.executable  # 获取当前 Python 解释器路径
    cmd = [python_executable, str(script_path), filename, str(file_id)]

    try:
        # 以非阻塞方式运行脚本
        with open(log_path, "a") as log_file:
            current_app.logger.info(f"异步执行脚本: {script_path} 参数: file_name={filename}, file_id={file_id}")
            subprocess.Popen(cmd, stdout=log_file, stderr=log_file)
        return True
    except Exception as e:
        current_app.logger.error(f"Failed to execute script {script_path}: {str(e)}")
        delete_file_into_db(file_id)
        return False


@xlog_crypt_bp.route('/upload', methods=['POST'])
def upload_file():
    owner = request.headers.get('X-User', "admin")
    if 'file' not in request.files:
        return jsonify(error='No file part'), 400

    file = request.files['file']
    if file.filename == '':
        return jsonify(error='No selected file'), 400

    # 保存上传的文件
    local_file_path = os.path.join(UPLOADS_PATH, file.filename)
    try:
        current_app.logger.info(f'保存上传的文件到: {local_file_path}')
        file.save(local_file_path)
    except Exception as e:
        return jsonify(error=f'Failed to save file: {str(e)}'), 500

    # 将文件传输到远程服务器
    try:
        current_app.logger.info(f'将文件传输到远程服务器: {local_file_path}')
        upload_to_remote(local_file_path)
    except Exception as e:
        return jsonify(error=f'Failed to upload file: {str(e)}'), 500

    # 运行远程脚本
    try:
        current_app.logger.info(f'执行远程脚本，参数名：{file.filename}')
        log_file_path = run_remote_script(file.filename)
    except Exception as e:
        return jsonify(error=f'Failed to run remote script: {str(e)}'), 500

    file_id = insert_file_into_db(f"{file.filename}.log", owner=owner)

    # 下载远程生成的日志文件
    try:
        download_log_file(log_file_path, file_id)
    except Exception as e:
        delete_file_into_db(file_id)
        return jsonify(error=f'Failed to download log file: {str(e)}'), 500

    # 写入数据库
    current_app.logger.info(f'执行日志文件写入数据库：{file.filename}')
    proces_res = process_log_async(file.filename, file_id)
    current_app.logger.info(f'任务：日志文件写入数据库执行状态：{proces_res}')

    # 清空上传文件目录
    clear_uploads_directory()

    file_dir = os.path.join(CRYPT_LOGS_PATH, str(file_id))
    file_path = os.path.join(file_dir, os.path.basename(log_file_path))
    current_app.logger.info(f"返回日志文件路径: {file_path}, {os.path.basename(log_file_path)}")
    # 检查文件是否存在
    if not os.path.exists(file_path):
        return abort(404)
    return jsonify({"msg": "文件上传成功"}), 200


@xlog_crypt_bp.route('/download/<file_id>', methods=['POST'])
def download_file(file_id):
    file_name = request.args.get('filename', type=str)
    file_dir = os.path.join(CRYPT_LOGS_PATH, file_id)
    file_path = os.path.join(file_dir, file_name)
    if not file_name or not file_id or not file_path or not os.path.exists(file_path):
        return abort(404)
    return send_file(file_path, as_attachment=True, mimetype='text/plain')


@xlog_crypt_bp.route('/query/list')
def query_list():
    """
    查询日志列表
    """
    UserQuery = SsousersModel.query.all()
    userDict = {user.username: user.displayname for user in UserQuery}
    page_no = request.args.get('pageNo', default=1, type=int)
    page_size = request.args.get('pageSize', default=10, type=int)
    keyword = request.args.get('keyword', default='', type=str)

    try:
        query = AppLogList.query

        if keyword:
            query = query.filter(AppLogList.filename.like(f'%{keyword}%'))

        query = query.order_by(desc(AppLogList.create_time))

        pagination = query.paginate(page=page_no, per_page=page_size, error_out=False)

        data = [log.to_dict_is_time() for log in pagination.items]
        for item in data:
            item['owner'] = userDict.get(item['owner'])
        response = {
            'data': data,
            'totalCount': pagination.total,
        }
        return jsonify(response), 200
    except Exception as e:
        current_app.logger.error(f"查询日志列表失败: {e}")
        return jsonify(error=str(e)), 500


@xlog_crypt_bp.route('/details/<file_id>/group/by', methods=['POST'])
def query_details_list(file_id):
    """
    日志详情
    :param file_id:
    :return:
    """
    page_no = request.args.get('pageNo', default=1, type=int)
    page_size = request.args.get('pageSize', default=10, type=int)
    keywords = request.args.get("keywords", '')
    filters = request.get_json(force=True)

    try:
        query = AppLogDetail.query.filter_by(file_id=file_id)
        if keywords and keywords != '':
            query = query.filter(AppLogDetail.log_details.like(f'%{keywords}%'))

        first_record = query.order_by(AppLogDetail.log_time).first()
        last_record = query.order_by(desc(AppLogDetail.log_time)).first()

        filter_map = {
            'log_level': AppLogDetail.log_level,
            'log_type': AppLogDetail.log_type,
            'method_name': AppLogDetail.method_name,
            'module': AppLogDetail.module,
            'process': AppLogDetail.process,
            'thread': AppLogDetail.thread,
        }

        for filter_key, filter_values in filters.items():
            if filter_values:
                if filter_key in filter_map:
                    query = query.filter(filter_map[filter_key].in_(filter_values))

        query = query.order_by(desc(AppLogDetail.log_time))
        pagination = query.paginate(page=page_no, per_page=page_size, error_out=False)

        if first_record and last_record:
            time_from = first_record.log_time.isoformat() + 'Z'
            time_to = last_record.log_time.isoformat() + 'Z'
        else:
            time_from = time_to = None

        data = []
        for log in pagination.items:
            log_dict = log.to_dict_is_time()
            log_details = log_dict.get('log_details', '')

            match = re.search(r'X-Request-ID:\s*([\w-]+)\r?\n', log_details)
            if match:
                x_request_id = match.group(1)

                kibana_base_url = "https://kib.in.szwego.com/app/kibana#/discover/376b0950-c8cb-11eb-beaf-012b7c96e129"
                params = {
                    "_g": f"(filters:!(),refreshInterval:(pause:!t,value:0),time:(from:'{time_from}',to:'{time_to}'))",
                    "_a": f"(columns:!(status,npath,x_errcode,request_body),filters:!(),index:'7aa73370-4800-11eb-8024-6335eded8129',interval:auto,query:(language:kuery,query:'x_req_id:{x_request_id}'),sort:!(!('@timestamp',desc)))"
                }
                kibana_url = f"{kibana_base_url}?{urlencode(params)}"
                log_dict['kibanaUrl'] = kibana_url
            else:
                log_dict['kibanaUrl'] = ''

            data.append(log_dict)
        response = {
            'data': data,
            'totalCount': pagination.total,
            'time_from': time_from,
            'time_to': time_to
        }
        return jsonify(response), 200
    except Exception as e:
        print(e)

        return jsonify(error=str(e)), 500


@xlog_crypt_bp.route('/details/<file_id>/filters')
def query_details_filters(file_id):
    """
    筛选项
    :param file_id:
    :return:
    """
    try:
        def get_unique_values(column):
            unique_values = (
                db.session.query(getattr(AppLogDetail, column))
                .filter_by(file_id=file_id)
                .distinct()
                .all()
            )
            if column == "log_time":
                return [{"text": get_time_stamp(value[0]), "value": get_time_stamp(value[0])} for value in unique_values]
            return [{"text": value[0], "value": value[0]} for value in unique_values if value[0] is not None]

        filters = {
            "line_number": get_unique_values("line_number"),
            "log_level": get_unique_values("log_level"),
            "log_type": get_unique_values("log_type"),
            "method_name": get_unique_values("method_name"),
            "module": get_unique_values("module"),
            "part_id": get_unique_values("part_id"),
            "process": get_unique_values("process"),
            "thread": get_unique_values("thread")
        }
        return jsonify({"data": filters}), 200
    except Exception as e:
        return jsonify(error=str(e)), 500
