#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
@Title:   __init__.py
@Date:    2022/4/9 9:59
@Author:  WangLH
@Desc:    蓝图__init__文件
"""

import pkgutil
import sys

from flask import Blueprint

from .cicd import ViewMethodBase


def search_blueprint(app):
    """
    搜素当前项目下的所有蓝图
    :param app:
    :return:
    """
    app_dict = {}
    pkg_list = pkgutil.walk_packages(__path__, __name__ + '.')
    for _, modname, ispkg in pkg_list:
        __import__(modname)  # 导入注册模块
        module = sys.modules[modname]
        module_attr = dir(module)  # 查看包（模块的）所有属性
        for name in module_attr:
            var_obj = getattr(module, name)  # 获取包下函数的属性
            if isinstance(var_obj, Blueprint) and app_dict.get(name) is None:  # 判断是否为Blueprint蓝图属性
                app_dict[name] = var_obj
                app.register_blueprint(var_obj)  # 注册蓝图
                # with app.test_request_context():
                # print("* 注入 %s 模块 %s success" % (Blueprint.__name__, var_obj.__str__()))
    # print(app.url_map) # 输出所有蓝图
