#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
@Title:   cicd.py
@Date:    2022/04/09 09:46
@Update:  2023/05/05 10:27
@Author:  Wanglh
@Desc:    ci相关接口
"""

from flask import Blueprint, request, views
from sqlalchemy import func
from sqlalchemy.exc import IntegrityError, SQLAlchemyError

from blueprint.useritsm import get_name_dictionary
from utils import *

ci_sandbox_bp = Blueprint("ci_sandbox", __name__, url_prefix="/ci/api/v1")
devops_users = ['wanglinhao', 'dongxin', 'lijiana', 'wuxuan']


def sql_engine_create():
    DB_URL = current_app.config.get('DB_URI')
    engine = create_engine(DB_URL)
    return engine


@ci_sandbox_bp.route('/sandbox/query', methods=['POST'])
def sandbox_index():
    """查询当前沙盒环境的各种信息列表"""
    dataraw = get_response(request)
    keyword = dataraw.setdefault('keyword', "")
    database = current_app.config.get('DATABASE')
    try:
        # 执行原生的sql
        query = db.session.execute(
            "SELECT a.sbx_uuid, a.sbx_alias, b.version_code, a.type, DATEDIFF(a.valid_time,NOW()) AS day_left, a.status, "
            "a.flow_uuid, b.iteration_id, a.applicant "
            "FROM {}.ci_sandbox a JOIN {}.ci_workflow b ON a.flow_uuid = b.flow_uuid "
            "WHERE a.sbx_uuid LIKE '%{}%' OR b.version_code LIKE '%{}%' OR a.sbx_alias LIKE '%{}%'"
            "ORDER BY a.status, a.valid_time".format(database, database, keyword, keyword, keyword))
        query_total = db.session.execute(
            "SELECT count(a.flow_uuid) AS total "
            "FROM {}.ci_sandbox a JOIN {}.ci_workflow b ON  a.flow_uuid = b.flow_uuid "
            "WHERE a.sbx_uuid LIKE '%{}%' OR b.version_code LIKE '%{}%' OR a.sbx_alias LIKE '%{}%'".format(database, database, keyword, keyword, keyword))
        data = [dict(zip(result.keys(), result)) for result in query.fetchall()]
        data = sorted(data, key=lambda x: x.get("type"), reverse=True)
        # data = [item for item in data if item.get("sbx_uuid") == "baseline"] + [item for item in data if item.get("sbx_uuid") != "baseline"]
        total = [dict(zip(result.keys(), result)) for result in query_total.fetchall()]
        current_app.logger.info("沙盒环境数据获取")
        return jsonify({"code": 200, "msg": "获取数据成功", 'data': data, "total": total})
    except SQLAlchemyError as e:
        return jsonify({"code": 500, "msg": str(e)})


@ci_sandbox_bp.route("/sandbox/create", methods=['POST'])
def sandbox_create():
    """创建沙盒环境"""
    action = '沙盒环境信息'
    dataraw = get_response(request)
    valid_time = dataraw.get('valid_time')  # 格式问题
    valid_time = datetime.strptime(valid_time, '%Y-%m-%dT%H:%M:%S.%fZ')
    valid_time = str(valid_time)
    applicant = request.headers.get('X-User')
    flow_uuid = get_short_id()
    sbx_uuid = get_short_id()
    # 写入ci_workflow
    workflow_data = {"owner": "jiyelin", "operation": "jiyelin", "tester": "jiyelin", "product": "jiyelin",
                     "flow_uuid": flow_uuid, "applicant": applicant, "line_name": dataraw["bizline"],
                     "version_code": dataraw["version_code"], "status": 5}

    # 写入 ci_sandbox
    sandbox_data = {'flow_uuid': flow_uuid, 'sbx_uuid': sbx_uuid, 'domain': dataraw.get('domain'),
                    'applicant': applicant, 'valid_time': str(valid_time), 'type': dataraw.get('value', 1),
                    'memo': dataraw.get('memo', '')}
    try:
        db.session.execute(CiWorkflow.__table__.insert(), [workflow_data])  # 插入数据
        db.session.execute(CiSandbox.__table__.insert(), [sandbox_data])  # 插入数据
        db.session.commit()
        current_app.logger.info("ci_sanbox,ci_workflow 创建写入成功")
        save_to_operation_log(request, action=action, result=200)
        return jsonify({"code": 200, "msg": "数据插入成功"})
    except SQLAlchemyError as e:
        db.session.rollback()
        current_app.logger.error(f"{e}")
        save_to_operation_log(request, action=action, result=500)
        return jsonify({"code": 500, "msg": str(e)})
    finally:
        db.session.close()


@ci_sandbox_bp.route('/sandbox/update', methods=['POST'])
def sandbox_update():
    """
    更新-沙盒环境
    :return:
    """
    action = "沙盒环境信息"
    dataraw = get_response(request)
    status = dataraw.get("status")
    sbx_uuid = dataraw.get('sbx_uuid')
    flow_uuid = dataraw.get('flow_uuid')
    update_dict = {k: v for k, v in dataraw.items() if k not in ['sbx_uuid', 'flow_uuid']}
    print(update_dict)
    if sbx_uuid:
        try:
            # CiSandbox.query.filter(flow_uuid=flow_uuid, sbx_uuid=sbx_uuid).update({"status": status})
            CiSandbox.query.filter_by(sbx_uuid=sbx_uuid).update(update_dict)
            db.session.commit()
            db.session.close()
            current_app.logger.info("ci_sandbox 数据更新成功")
            save_to_operation_log(request, action=action, result=200)
            return jsonify({"code": 200, "msg": '数据更新成功'})
        except SQLAlchemyError as e:
            db.session.rollback()
            save_to_operation_log(request, action=action, result=500)
            return jsonify({"code": 500, "msg": str(e)})
    save_to_operation_log(request, action=action, result=400)
    return jsonify({"code": 400, "msg": "flow_uuid/status/sbx_uuid 不存在"})


# 统计数据信息
@ci_sandbox_bp.route('/sandbox/statistics')
def sandbox_statistics():
    """
    查询统计数据信息
    :return:
    """
    sandbox_query = db.session.execute("""SELECT count(id) AS num,status FROM ci_sandbox GROUP BY status""")
    name_dict = {0: "申请中", 1: "创建中", 2: "使用中", 3: "已封存", 4: "过期释放"}
    column = ("value", "name")
    sandbox_data = [(i[0], name_dict[i[-1]]) for i in sandbox_query.fetchall()]
    status_data = [dict(zip(column, i)) for i in sandbox_data]

    workflow_query = db.session.execute("""SELECT count(id) AS num,line_name FROM ci_workflow GROUP BY line_name""")
    workflow_data = workflow_query.fetchall()
    result = sorted(workflow_data, key=lambda workflow_data: workflow_data[-1], reverse=False)
    line_name = [i[-1] for i in result]
    line_value = [i[0] for i in result]
    data = {"status_data": status_data, "line_name": line_name, "line_value": line_value}
    return jsonify({"code": 200, "data": data})


@ci_sandbox_bp.route('/sandbox/projectdetail', methods=['POST'])
def sandbox_projectdetail():
    """
    沙盒环境下的项目信息更新
    :return:
    """
    action = "沙盒环境信息"
    dataraw = get_response(request)
    # 根据UUID进行数据的插入更新
    flow_uuid = dataraw['flow_uuid']
    query = CiSandboxProjectiles.query.all()  # 读取数据
    flowuid_data = [i.flow_uuid for i in query]
    if flow_uuid not in flowuid_data:
        current_app.logger.warning("flow_uuid不存在")
        save_to_operation_log(request, action=action, result=400)
        return jsonify({"code": 400, "msg": "flow_uuid不存在"})
    try:
        db.session.execute(CiSandboxProjectiles.__table__.insert(), [dataraw])  # 插入数据
        db.session.commit()
        current_app.logger.info("200")
        save_to_operation_log(request, action=action, result=200)
        return jsonify({"code": 200, "msg": "数据写入成功"})
    except SQLAlchemyError as e:
        db.session.rollback()
        current_app.logger.error(f"{e}")
        save_to_operation_log(request, action=action, result=500)
        return jsonify({"code": 500, "msg": str(e)})
    finally:
        db.session.close()


# 环境基础信息
@ci_sandbox_bp.route('/sandbox/detail', methods=['POST'])
def sandbox_detail():
    sbx_uuid = get_response(request)['sbx_uuid']
    try:
        select_sql = f"""
            SELECT
                a.flow_uuid, b.line_name,
                DATE_FORMAT( b.create_time, '%Y-%m-%d %T' ) AS create_time,
                b.`owner`, b.STATUS, a.domain, b.tester,
                b.product, b.operation, b.version_code 
            FROM
                ci_sandbox a
            JOIN ci_workflow b ON a.flow_uuid = b.flow_uuid 
            WHERE
                a.sbx_uuid = '{sbx_uuid}';
        """
        sandbox_query = db.session.execute(select_sql)

        column = (
            "flow_uuid", "line_name", "create_time", "owner", "status", "domain", "tester", "product", "operation",
            "version_code")
        detail_data = [dict(zip(column, i)) for i in sandbox_query.fetchall()]
        return jsonify({"code": 200, "data": detail_data})
    except Exception as e:
        return jsonify({"code": 500, "msg": e})


# 工作流接口  主要涉及：ci_workflow
@ci_sandbox_bp.route('/workflow/create', methods=['POST'])
def workflow_create():
    """
    工作流创建
    :return:
    """
    # 等待确定是否使用字母加数字的工单ID
    action = "工作流"
    dataraw = request.json
    flow_uuid = get_short_id()
    dataraw['flow_uuid'] = flow_uuid
    dataraw['applicant'] = request.headers.get('X-User')

    try:
        db.session.execute(CiWorkflow.__table__.insert(), [dataraw])  # 插入数据
        db.session.commit()
        current_app.logger.info("ci_workfow 数据写入成功")
        save_to_operation_log(request, action=action, result=200)
        return jsonify({"code": 200, "msg": "数据插入成功"})
    except SQLAlchemyError as e:
        db.session.rollback()
        current_app.logger.error(f"{e}")
        save_to_operation_log(request, action=action, result=500)
        return jsonify({"code": 500, "msg": str(e)})
    finally:
        db.session.close()


@ci_sandbox_bp.route('/workflow/update', methods=['POST'])
def workflow_update():
    """
    工作流更新
    :return:
    """
    action = "工作流"
    dataraw = request.json
    status = dataraw.get('status')
    flow_uuid = dataraw.get('flow_uuid')
    if status == 0:
        current_app.logger.warning("status为0不进行更新")
        save_to_operation_log(request, action=action, result=400)
        return jsonify({"code": 400, "msg": "status为0不进行更新"})
    if status and flow_uuid:
        try:
            CiWorkflow.query.filter_by(flow_uuid=flow_uuid).update({"status": status})
            db.session.commit()
            db.session.close()
            current_app.logger.info("ci_workflow 数据更新成功")
            save_to_operation_log(request, action=action, result=200)
            return jsonify({"code": 200, "msg": "数据写入成功"})
        except Exception as e:
            current_app.logger.error(f"{e}")
            save_to_operation_log(request, action=action, result=500)
            return jsonify({"code": 500, "msg": str(e)})
    save_to_operation_log(request, action=action, result=404)
    return jsonify({"code": 404, "msg": "flow_uuid/status 不存在"})


# @ci_sandbox_bp.route('/workflow/query')
# def workflow_query():
#     pass


@ci_sandbox_bp.route('/index')
def bp_index():
    return "This is a index"


# TODO 上线需要将jobid设置为固定值
@ci_sandbox_bp.route('/sbx/deploystatus')
def sbx_deploystatus():
    """获取pod相关信息"""
    pass


# 主要涉及：ci_workflow  ci_business_line_members
# 创建pod，namespace，create
@ci_sandbox_bp.route('/sbx/createns', methods=['POST'])
def sbx_createns():
    """
    创建pod,namespace：create
    :return:
    """
    action = '沙盒环境-环境管理'
    dataraw = get_response(request)
    print(dataraw)
    try:
        result = execute_job_1000152(namespace=dataraw['sbx_uuid'], ops='create')
        current_app.logger.info(f"调用[蓝鲸-集成测试环境管理]作业，结果：{result}")
        if str(result["result"]) == "True":
            CiSandbox.query.filter_by(flow_uuid=dataraw["flow_uuid"], sbx_uuid=dataraw['sbx_uuid']).update(
                {"status": 1})
            db.session.commit()
            db.session.close()
        current_app.logger.info("ci_sandbox 数据更新成功")
        save_to_operation_log(request, action=action, result=200)
        return jsonify({"code": 200, "msg": result["result"]})
    except Exception as e:
        current_app.logger.error(f"{e}")
        save_to_operation_log(request, action=action, result=500)
        return jsonify({"code": 500, "msg": str(e)})


@ci_sandbox_bp.route('/sbx/resume', methods=['POST'])
def sbx_resumepod():
    """恢复环境：resume
    status：resume
    :return:
    """
    action = '沙盒环境-环境管理'
    dataraw = get_response(request)
    try:
        result = execute_job_1000152(namespace=dataraw['sbx_uuid'], ops='resume')
        current_app.logger.info("环境恢复成功")
        save_to_operation_log(request, action=action, result=200)
        return jsonify({"code": 200, "msg": result["result"]})
    except Exception as e:
        save_to_operation_log(request, action=action, result=500)
        return jsonify({"code": 500, "msg": str(e)})


# 下面的内容全部传递给蓝鲸
@ci_sandbox_bp.route('/sbx/pause', methods=['POST'])
def sbx_pause():
    """停止所有pod为0
    status：pause
    :return:
    """
    action = "沙盒环境-环境管理"
    dataraw = get_response(request)
    try:
        result = execute_job_1000152(namespace=dataraw['sbx_uuid'], ops='pause')
        current_app.logger.info("当前环境所有pod已为0")
        save_to_operation_log(request, action=action, result=200)
        return jsonify({"code": 200, "msg": result["result"]})
    except Exception as e:
        current_app.logger.error(f"{e}")
        save_to_operation_log(request, action=action, result=500)
        return jsonify({"code": 500, "msg": str(e)})


@ci_sandbox_bp.route('/sbx/deletens', methods=['POST'])
def sbx_delete():
    """删除沙盒环境
    获取flow_uuid，然后删除沙盒环境
    status: delete
    """
    action = "沙盒环境-环境管理"
    dataraw = get_response(request)
    try:
        result = execute_job_1000152(namespace=dataraw['sbx_uuid'], ops='delete')
        current_app.logger.info(f"当前沙盒环境：{dataraw['sbx_uuid']}删除成功")
        save_to_operation_log(request, action=action, result=200)
        return jsonify({"code": 200, "msg": result["result"]})
    except Exception as e:
        current_app.logger.error(f"{e}")
        save_to_operation_log(request, action=action, result=500)
        return jsonify({"code": 500, "msg": str(e)})


# jenkins服务维护接口 /sbx/project/
# 主要涉及：ci_sandbox_project_details
@ci_sandbox_bp.route('/sbx/project/query', methods=['POST'])
def project_query():
    """
    查询：ci_sandbox_project_details
    :return:
    """
    # action = "沙盒环境-服务管理"
    dataraw = get_response(request)
    keyword = dataraw.get("keyword", "")
    sbx_uuid = dataraw["sbx_uuid"]
    database = current_app.config.get("DATABASE")
    try:
        query = db.session.execute(
            """SELECT
                    b.NAME,
                    b.project,
                    a.publish_count,
                    a.project_branch,
                    a.commit_id,
                    DATE_FORMAT( a.latest_build_time, '%Y-%m-%d %T' ) AS latest_build_time,
                    a.priority,
                    a.STATUS,
                    a.latest_sequence_id,
                    c.jenkins_project,
                    max(c.jenkins_buildno) AS jenkins_buildno 
                FROM
                    {database}.ci_sandbox_project_details AS a
                    JOIN {database}.ci_projects b ON a.project_id = b.id
                    LEFT JOIN {database}.ci_sandbox_build_info c ON a.latest_sequence_id = c.sequence_id
                    LEFT JOIN {database}.ci_sandbox d ON a.sbx_uuid = d.sbx_uuid
                WHERE
                    a.sbx_uuid = '{sbx_uuid}' 
                    AND ( b.NAME LIKE '%{keyword}%' OR b.project LIKE '%{keyword}%' OR a.commit_id LIKE '%{keyword}%' ) 
                GROUP BY
                    b.project, a.latest_sequence_id, a.priority, a.latest_build_time
                ORDER BY
                    a.priority DESC,
                    a.latest_build_time DESC""".format(keyword=keyword, sbx_uuid=sbx_uuid, database=database))

        column = (
            "name", "project", "publish_count", "project_branch", "commit_id", "latest_build_time", "priority",
            "status", "latest_sequence_id", "jenkins_project", "jenkins_buildno")
        data = [dict(zip(column, i[:11])) for i in query.fetchall()]
        total = len(data)
        # current_app.logger.info('数据获取成功')
        db.session.close()
        return jsonify({"code": 200, "msg": "获取数据成功", 'data': data, "total": total})
    except SQLAlchemyError as e:
        current_app.logger.error(f"{e}")
        db.session.close()
        return jsonify({"code": 500, "msg": str(e)})


@ci_sandbox_bp.route('/sbx/project/create', methods=['POST'])
def sbx_project_create():
    """
    沙盒环境中的服务（项目）创建
    :return:
    """

    action = "沙盒环境-服务管理"

    dataraw = get_response(request)
    repository = "https://git.code.tencent.com" + dataraw["git_project"]
    create_time = datetime.now().strftime("%Y-%m-%d %H:%M:%S")

    try:
        task_filter = {
            and_(
                CiProjects.name == dataraw.get('name'),
                CiProjects.appname == dataraw.get('appname'),
            )
        }
        # 写入cisandboxprojectdetail存在异常，等待解决。通过appname进行判断查询project_id
        # ci_query = CiProjects.query.filter_by(git_project=dataraw["git_project"]).first()  # 传入jenkins的服务名
        ci_query = CiProjects.query.filter(*task_filter).first()  # 传入jenkins的服务名
        if ci_query.id and dataraw.get('sbx_uuid'):
            create_query = CiSandboxProjectiles(sbx_uuid=dataraw['sbx_uuid'], project_id=ci_query.id,
                                                project_branch=dataraw["branch"], create_time=create_time, publish_count=1,
                                                repository=repository)
            db.session.add(create_query)
            db.session.commit()
            query = CiSandboxProjectiles.query.filter_by(sbx_uuid=dataraw["sbx_uuid"], project_id=ci_query.id).first()
            if query:
                current_app.logger.info(f"{dataraw}创建服务信息写入数据库成功")
                db.session.close()
                save_to_operation_log(request, action=action, result=200)
                return jsonify({"code": 200, "msg": f"{dataraw}创建服务信息写入数据库成功"})
    except IntegrityError as e:
        current_app.logger.error(f"{e}")
        db.session.rollback()
        if "1062" in str(e):
            save_to_operation_log(request, action=action, result=400)
            return jsonify({"code": 400, "msg": f"{dataraw['appname']}-{dataraw['name']}: 服务已存在！"})
        save_to_operation_log(request, action=action, result=500)
        return jsonify({"code": 500, "msg": str(e)})
    current_app.logger.warning("WARNING: 数据写入失败")
    save_to_operation_log(request, action=action, result=404)
    return jsonify({"code": 404, "msg": "未查询到服务id（ci_projects表）"})


@ci_sandbox_bp.route('/sbx/project/build', methods=['POST'])
def sbx_project_update():
    """
    沙盒环境服务构建
    :return:
    """

    action = "沙盒环境-服务管理"

    dataraw = get_response(request)
    latest_sequence_id = get_stamp_id()

    projects = dataraw["project"].split(',')
    services = []
    for project in projects:
        project_query = CiProjects.query.filter_by(project=project).first()
        services.append(project_query.project)
        pro_det_query = CiSandboxProjectiles.query.filter_by(project_id=project_query.id, sbx_uuid=dataraw[
            "sbx_uuid"]).first()

        if pro_det_query:
            # 发起构建任务的时候，额外添加一个时间戳ID到数据表ci_sandbox_project_details的latest_sequence_id列中
            CiSandboxProjectiles.query.filter_by(sbx_uuid=dataraw["sbx_uuid"],
                                                 project_id=pro_det_query.project_id).update(
                {"publish_count": int(pro_det_query.publish_count) + 1,
                 "latest_build_time": datetime.now(), "status": 2, "latest_sequence_id": latest_sequence_id})
            db.session.commit()
            db.session.close()
            current_app.logger.info("更新ci_sandbox_project_detail,ci_workflow数据成功")
        else:
            current_app.logger.warning(f"WARNING：未发现项目{project_query.project}历史记录")
            # return jsonify({"code": 400, "msg": f"未发现项目{project_query.project}历史记录"})
    services = ",".join(services)
    username = request.headers.get('X-User') if request.headers.get('X-User') else "unknown"
    bk_result = execute_job_1000154(services=services, branch=dataraw["project_branch"],
                                    deploy_env=dataraw["sbx_uuid"], sequence=str(latest_sequence_id),
                                    username=username)
    save_to_operation_log(request, action=action, result=200)
    return jsonify({"code": 200,
                    "msg": "{}构建成功,蓝鲸调用结果{}".format(dataraw['sbx_uuid'], str(bk_result["result"]))})


@ci_sandbox_bp.route('/sbx/project/delete', methods=['POST'])
def sbx_project_delete():
    """
    沙盒环境服务删除
    :return:
    """
    action = "沙盒环境-服务管理"
    dataraw = get_response(request)
    ci_query = CiProjects.query.filter_by(project=dataraw['project']).first()
    project_id = ci_query.id
    delete_sql = f"""DELETE FROM `ci_sandbox_project_details` WHERE `project_id`={project_id} and `sbx_uuid`=\
    '{dataraw['sbx_uuid']}\'"""
    db.session.execute(delete_sql)
    db.session.commit()
    select_sql = f"""SELECT * FROM `ci_sandbox_project_details` WHERE `sbx_uuid`=\'{dataraw['sbx_uuid']}\' AND 
    `project_id`={project_id}"""
    query = db.session.execute(select_sql)
    data = query.fetchall()
    db.session.close()
    if len(data) != 0:
        current_app.logger.warning("WARNING：数据删除失败")
        save_to_operation_log(request, action=action, result=400)
        return jsonify({"code": 400, "msg": "数据删除失败"})
    current_app.logger.info("200")
    save_to_operation_log(request, action=action, result=200)
    return jsonify({"code": 200, "msg": "数据删除成功"})


@ci_sandbox_bp.route('/sbx/operation/logs')
def sbx_operation_logs():
    """
    沙盒环境日志查询
    :return:
    """
    dataraw = get_response(request)
    sbx_uuid = dataraw['sbx_uuid']
    task_type = dataraw['task_type']
    query = CiSandboxOperationlogs.query.filter_by(sbx_uuid=sbx_uuid, task_type=task_type).all()
    query_items = CiSandboxOperationlogs.to_all_json(query)
    for data in query_items:
        data['create_time'] = str(data['create_time'])
    return jsonify({"code": 200, "data": query_items})


@ci_sandbox_bp.route("/base/allproject")
def mysql_project():
    """
    所有基础服务查询
    :return:
    """
    try:
        # type_dict = {0: "后端", 1: "前端", 2: "其他", 3: "pass"}
        # data_raw = get_response(request)
        query = CiProjects.query.filter_by(online=1).all()
        data = [{"project_id": i.id, "appname": i.appname, "git_project": i.git_project, "name": i.name, "project": i.project,
                 "type": CiProjects.CHOICE_TYPE_ITEMS.get(i.type, 2)} for i in query]
        current_app.logger.info("200")
        return jsonify({"code": 200, "data": data, "total": {"total": len(query)}, "msg": '数据获取成功'})
    except Exception as e:
        current_app.logger.error(f"{e}")
        return jsonify({"code": 500, "msg": str(e)})


@ci_sandbox_bp.route('/project/branches', methods=['POST'])
def tencent_code():
    """
    获取服务分支
    :return:
    """
    dataraw = get_response(request)
    git_repo = dataraw['git_project']
    branchs = CodeTencent(git_repo=git_repo).get_project_branches()
    if "Error" in branchs:
        current_app.logger.info(f"{git_repo}分支列表获取失败")
        return jsonify({"code": 200, "msg": "{}分支列表获取失败".format(git_repo)})
    current_app.logger.info(f"{git_repo}分支列表获取成功")
    return jsonify({"code": 200, "msg": "{}分支列表获取成功".format(git_repo), "data": branchs, "total": len(branchs)})


@ci_sandbox_bp.route('/project/query', methods=['POST'])
def ci_project_query():
    """
    查询：ci_projects
    :return:
    """
    dataraw = get_response(request)
    keyword = dataraw.get('keyword', "")
    pageIndex = dataraw.get("pageNo", 1)
    pageSize = dataraw.get("pageSize", 10)

    try:
        task_filter = {
            or_(
                CiProjects.name.like("%{}%".format(keyword)),
                CiProjects.appname.like("%{}%".format(keyword)),
                CiProjects.project.like("%{}%".format(keyword))
            )
        }

        query = CiProjects.query.filter(*task_filter).order_by(CiProjects.online.desc(), CiProjects.replicas.desc()).limit(pageSize).offset(
            (pageIndex - 1) * pageSize).all()
        data = CiProjects.to_all_json(query)
        count = db.session.query(func.count(CiProjects.id)).filter(*task_filter).scalar()  # 根据filter计算当前数据表的总条数
        current_app.logger.info('数据获取成功')
        return jsonify({"code": 200, "data": data, "totalCount": count, "msg": '数据获取成功'})
    except SQLAlchemyError as e:
        current_app.logger.error(f"{e}")
        return jsonify({"code": 500, "msg": str(e)})


@ci_sandbox_bp.route('/project/create', methods=['POST'])
def project_create():
    """
    添加：ci_projects
    :return:
    """
    action = "服务管理"
    dataraw = get_response(request)
    try:
        db.session.execute(CiProjects.__table__.insert(), [dataraw])  # 插入数据
        db.session.commit()
        current_app.logger.info("数据更新成功")
        save_to_operation_log(request, action=action, result=200)
        return jsonify({"code": 200, "msg": "数据更新成功"})
    except Exception as e:
        db.session.rollback()
        current_app.logger.error(f"{e}")
        save_to_operation_log(request, action=action, result=500)
        return jsonify({"code": 500, "msg": str(e)})
    finally:
        db.session.close()


@ci_sandbox_bp.route('/project/update', methods=['POST'])
def project_update():
    """
    更新：ci_projects
    :return:
    """
    action = "服务管理"
    dataraw = get_response(request)
    try:
        CiProjects.query.filter(CiProjects.project == dataraw['project'], CiProjects.appname == dataraw['appname']).update(
            {"replicas": dataraw["replicas"], "online": dataraw["online"]})
        db.session.commit()
        current_app.logger.info(f"{dataraw['appname']}数据更新成功")
        save_to_operation_log(request, action=action, result=200)
        return jsonify({'code': 200, 'msg': f"{dataraw['appname']}数据更新成功"})
    except Exception as e:
        current_app.logger.error(f"{e}")
        db.session.rollback()
        save_to_operation_log(request, action=action, result=500)
        return jsonify({"code": 500, "msg": str(e)})
    finally:
        db.session.close()


@ci_sandbox_bp.route('/compare/file', methods=['POST'])
def compare_file():
    """
    文件对比
    :param
        :type: 配置类型
        :sbx_uuid: 命名空间
        :filename: 文件名
        :service: 服务名
    :return:
    """
    dataraw = get_response(request)
    config_type = dataraw.get('type', 'nacos')
    result = compare_nacos(request) if config_type != 'apollo' else compare_apollo(request)
    current_app.logger.info("compare file 完成")
    return result


@ci_sandbox_bp.route('/compare/difference', methods=['POST'])
def compare_difference():
    """
    差异文件列表
    :param
        :type: 配置类型
        :sbx_uuid: 命名空间
    :return:
    """
    dataraw = get_response(request)
    print(dataraw)
    config_type = dataraw.get('type', 'nacos')
    result = compare_nacos_servicename(request) if config_type != 'apollo' else compare_apollo_servicename(request)
    current_app.logger.info("compare difference 获取差异文件完成")
    return result


@ci_sandbox_bp.route('/compare/writeback', methods=['POST'])
def compare_writeback():
    """
    文件对比，然后回写
    :param
        :type: 配置类型
        :sbx_uuid: 命名空间
        :filename: 文件名
        :service: 服务名
    :return:
    """
    action = "文件对比"
    dataraw = get_response(request)
    config_type = dataraw.get('type', 'nacos')
    result = compare_nacos_writeback(request) if config_type != 'apollo' else compare_apollo_writeback(request)
    save_to_operation_log(request, action=action, result=200)
    return result


@ci_sandbox_bp.route('/addgituser', methods=['POST'])
def add_gituser():
    """
    批量添加用户到指定组
    :param
        :groupname: 组名
    :return:
    """
    action = "用户管理-Git"
    dataraw = get_response(request)
    if "运维" in dataraw.get("groupname") and request.headers.get('X-User') != "dongxin":
        return jsonify({"code": 200, 'data': "运维组需运维负责人添加"})
    data = exectue_job_1000142(dataraw.get("names"), dataraw.get("groupname"))
    current_app.logger.info("蓝鲸作业ID：1000142，调用结束")
    save_to_operation_log(request, action=action, result=200)
    return jsonify({'code': 200, 'data': data})


@ci_sandbox_bp.route('/get_skywalking_sample')
def get_skywalking_sample():
    """
    获取skywalking信息
    :return:
    """
    try:
        data = skywalking_redis_mget()
        current_app.logger.info("200")
        return jsonify({"code": 200, "data": data, "msg": "获取成功"})
    except Exception as e:
        current_app.logger.error(f"{e}")
        return jsonify({"code": 200, "msg": str(e)})


@ci_sandbox_bp.route('/set_skywalking_sample', methods=['POST'])
def set_skywalking_sample():
    """
    设置skywalking的采集率
    :param:
        X-User: 用户
        keys_dict: json
    :return:
        {"code": 200, "msg": "采样率设置完成"} or {"code": 500, "msg": "采样率设置失败"}
    """
    action = "skywalking-采集率"
    keys_dict = get_response(request)
    user = request.headers.get("X-User")
    query = SsousersModel.query.filter_by(username=user).first()
    try:
        skywalking_redis_mset(keys_dict, query.displayname)
        current_app.logger.info("200")
        save_to_operation_log(request, action=action, result=200)
        return jsonify({"code": 200, "msg": "采样率设置完成"})
    except Exception as e:
        current_app.logger.error(f"{e}")
        save_to_operation_log(request, action=action, result=500)
        return jsonify({"code": 500, "msg": "采样率设置失败"})


@ci_sandbox_bp.route('/upgradePlan/update', methods=['POST'])
def upgradeplan_update():
    """
    更新：ci_upgrade_schedule
    :return:
    """
    action = "ci_upgrade_schedule"
    data_raw = get_response(request)
    data_raw['upgrade_id'] = data_raw['upgrade_id'] if data_raw.get("event_id", "") else "U" + datetime.now().strftime(
        "%Y%m%d%H%M%S")
    user_query = SsousersModel.query.filter().all()
    user_data = SsousersModel.to_all_json(user_query)
    user_data = {data['username']: data['displayname'] for data in user_data}  # userid和displayname
    displayname = user_data[data_raw.get('owner')] if data_raw.get('owner') in user_data else 'none'
    # 获取表的所有字段
    columns = [column.name for column in CiUpgradeSchedule.__table__.columns]
    for k in list(data_raw.keys()):
        if data_raw[k] == '' or k not in columns:
            del (data_raw[k])
    query = CiUpgradeSchedule.query.filter_by(version_name=data_raw['version_name']).first()
    try:
        if query:
            CiUpgradeSchedule.query.filter_by(version_name=data_raw['version_name']).update(data_raw)
            if data_raw.get('status') == 2:
                db.session.execute(CiUpgradeLog.__table__.insert(),
                                   [dict(owner=data_raw['owner'], c_time=datetime.now(),
                                         message=f'{displayname} {data_raw["version_name"]} 版本发布完成 {datetime.now().strftime("%Y-%m-%d %H:%M:%S")}')])
            elif data_raw.get('status') == 3:
                db.session.execute(CiUpgradeLog.__table__.insert(),
                                   [dict(owner=data_raw['owner'], c_time=datetime.now(),
                                         message=f'{displayname} 取消了{data_raw["version_name"]} 版本发布')])
            elif data_raw.get('status') == 1:
                db.session.execute(CiUpgradeLog.__table__.insert(),
                                   [dict(owner=data_raw['owner'], c_time=datetime.now(),
                                         message=f'{displayname} 延期了 {data_raw["version_name"]} 版本发布')])
            else:
                pass
            db.session.commit()

            return jsonify({'code': 200, 'msg': '已存在，更新完成'}), 200
        else:
            db.session.execute(CiUpgradeSchedule.__table__.insert(), [data_raw])
            db.session.execute(CiUpgradeLog.__table__.insert(), [dict(owner=data_raw['owner'], c_time=datetime.now(),
                                                                      message=f'{displayname} 预定了 {data_raw["plan_date"]} {data_raw["plan_time"]} 发布 {data_raw["version_name"]}')])
            db.session.commit()
            save_to_operation_log(request, action=action, result=200)
            return jsonify({'code': 200, 'msg': '数据写入成功'}), 200
    except Exception as e:
        db.session.rollback()
        save_to_operation_log(request, action=action, result=500)
        return jsonify({'code': 500, 'msg': e}), 500
    finally:
        db.session.close()


@ci_sandbox_bp.route('/upgradePlan/query', methods=['POST'])
def upgradeplan_select():
    """
    :param:
        keyword: str
        str_time: str
        end_time: str
        pageNo: int
        pageSize: int
    :return:
        {"code": 200, "data": datas, "msg": "数据获取成功", "totalCount": count}
    """
    data_raw = get_response(request)
    keyword = data_raw.get('keyword', '')
    str_time = get_time_stamp(data_raw.get('str_time', ''))
    end_time = get_time_stamp(data_raw.get('end_time', ''))
    pageIndex = data_raw.get("pageNo", 1)
    pageSize = data_raw.get("pageSize", 10)
    if str_time and end_time:
        task_filters = {
            and_(
                db.cast(CiUpgradeSchedule.plan_date, db.DATE) >= db.cast(str_time, db.DATE),
                db.cast(CiUpgradeSchedule.plan_date, db.DATE) < db.cast(end_time, db.DATE),
                or_(
                    CiUpgradeSchedule.owner.like("%{}%".format(keyword)),
                    CiUpgradeSchedule.version_name.like("%{}%".format(keyword)),
                    CiUpgradeSchedule.line_name.like("%{}%".format(keyword)),
                    CiUpgradeSchedule.flow_uid.like("%{}%".format(keyword))
                )
            )
        }
    else:
        task_filters = {
            or_(
                CiUpgradeSchedule.owner.like("%{}%".format(keyword)),
                CiUpgradeSchedule.version_name.like("%{}%".format(keyword)),
                CiUpgradeSchedule.line_name.like("%{}%".format(keyword)),
                CiUpgradeSchedule.flow_uid.like("%{}%".format(keyword))
            )
        }

    # 分页与排序
    query = db.session.query(CiUpgradeSchedule).filter(*task_filters).order_by(
        CiUpgradeSchedule.plan_date.asc()).limit(pageSize).offset((pageIndex - 1) * pageSize).all()
    data = CiUpgradeSchedule.to_all_json(query)
    count = db.session.query(func.count(CiUpgradeSchedule.id)).filter(*task_filters).scalar()
    datas = {}
    user_query = SsousersModel.query.filter().all()
    user_data = SsousersModel.to_all_json(user_query)
    user_data = {data['username']: data['displayname'] for data in user_data}
    # 加一个字段，displayname 显示在前端的中文名称
    for lines in data:
        date = str(lines['plan_date'])
        displayname = lines['owner'] if user_data.get(lines['owner'], '') == '' else user_data[lines['owner']]
        line = [{'time': str(lines['plan_time']), 'version_name': lines['version_name'], 'displayname': displayname,
                 'owner': lines['owner'], 'status': lines['status'], 'upgrade_id': lines['upgrade_id'], 'line_name': lines['line_name']}]
        try:
            datas.setdefault(date, []).extend(line)
        except TypeError:
            datas[date].append(line)

    return jsonify({"code": 200, "data": datas, "msg": "数据获取成功", "totalCount": count})


@ci_sandbox_bp.route('/upgradePlan/query/details')
def upgrade_details():
    """
    Get all information related to a given upgrade ID.

    ---
    tags:
      - upgrades
    parameters:
      - in: query
        name: upgrade_id
        type: integer
        required: true
        description: The ID of the upgrade to retrieve.
    responses:
      200:
        description: A JSON object with the upgrade schedule and project details.
        schema:
          type: object
          properties:
            ci_upgrade_schedule:
              $ref: '#/definitions/CiUpgradeSchedule'
            ci_upgrade_prj_details:
              $ref: '#/definitions/CiUpgradeprjDetails'
          required:
            - ci_upgrade_schedule
            - ci_upgrade_prj_details
    """
    upgrade_id = request.args.get('upgrade_id')
    schedule = CiUpgradeSchedule.query.filter_by(upgrade_id=upgrade_id).first()
    if not schedule:
        return jsonify({'message': f'Upgrade ID {upgrade_id} not found'}), 404
    # Fetch related project details using a join query
    details = db.session.query(CiUpgradeprjDetails). \
        join(CiUpgradeSchedule, CiUpgradeprjDetails.upgrade_id == CiUpgradeSchedule.upgrade_id). \
        filter(CiUpgradeSchedule.upgrade_id == upgrade_id).all()
    schedule_dict = schedule.to_dict()
    details_dict = [d.to_dict() for d in details]
    response = {
        'ci_upgrade_schedule': schedule_dict,
        'ci_upgrade_prj_details': details_dict
    }
    return json.dumps(response, cls=CustomJSONEncoder, ensure_ascii=False), 200


@ci_sandbox_bp.route('/upgradePlan/monthcount')
def upgradeplan_count():
    """
    计划变更统计
    # TODO: 没有上线，功能验证完毕
    Args:
        str_time:  (str) 开始时间
    Return:
        code: (int) 返回状态码
        totalCount: (query)  数据对象集合
    """
    data_raw = get_response(request)
    date_strtime = datetime.strptime(data_raw['str_time'], '%Y-%m-%d')
    month_start, month_end, month_len = get_month_start_end(date_strtime)
    print(month_start, month_end, month_len)
    task_filters = {
        and_(
            db.cast(CiUpgradeSchedule.plan_date, db.DATE) >= db.cast(month_start, db.DATE),
            db.cast(CiUpgradeSchedule.plan_date, db.DATE) < db.cast(month_end, db.DATE),
        )
    }
    # 当月数据汇总
    # 结束变更数
    end_count = db.session.query(func.count(CiUpgradeSchedule.id)).filter(
        CiUpgradeSchedule.status == 2).filter(*task_filters).scalar()
    # 计划变更数
    plan_count = db.session.query(func.count(CiUpgradeSchedule.id)).filter(
        CiUpgradeSchedule.status == 0).filter(*task_filters).scalar()
    # 发布变更数
    running_count = db.session.query(func.count(CiUpgradeSchedule.id)).filter(
        CiUpgradeSchedule.status == 1).filter(*task_filters).scalar()
    # 发布时长
    sum_time = db.session.query(func.sum(CiUpgradeSchedule.verify_time)).filter(CiUpgradeSchedule.status == 2).filter(
        *task_filters).scalar()
    # 验证时长
    b_verify_count = db.session.query(func.sum(CiUpgradeSchedule.verify_fails)).filter(
        CiUpgradeSchedule.status == 2).filter(*task_filters).scalar()
    # 业务线分类变更数
    line_name_count = db.session.query(CiUpgradeSchedule.line_name, func.count(CiUpgradeSchedule.id)).filter(CiUpgradeSchedule.status == 2).filter(*task_filters).group_by(CiUpgradeSchedule.line_name).all()
    line_name_count = [{line[0]: line[1]} for line in line_name_count]
    total = dict(end_total=end_count, plan_total=plan_count, running_total=running_count, verify_time_sum=float(str(sum_time)),
                 verify_count=float(str(b_verify_count)), line_name_count=line_name_count)
    print(total)
    return jsonify({"code": 200, 'totalCount': json.dumps(total)})


@ci_sandbox_bp.route('/upgradePlan/log')
def upgradePlan_log():
    """
    查询：ci_upgrade_log
    :return:
    """
    import timeago
    query = CiUpgradeLog.query.filter().order_by(CiUpgradeLog.c_time.desc()).limit(10).all()
    data = CiUpgradeLog.to_all_json(query)
    now = datetime.now()
    data = [{'message': i['message'], 'c_time': timeago.format(i['c_time'], now, 'zh_CN')} for i in data]
    return jsonify({'code': 200, 'data': data})


class DbtableOperation:
    """
    bind表基类
    """

    def __init__(self, Object: object, request_object: object, is_page=True):

        self.params_ages = get_response(request_object)
        self.object = Object
        if hasattr(self.object, 'update_by'):
            self.params_ages["update_by"] = request.headers.get('X-User')
        self.is_page = is_page
        self.request_object = request_object

    def operation_update(self):
        insert_stmt = insert(self.object).values(**self.params_ages)
        on_duplicate_key_stmt = insert_stmt.on_duplicate_key_update(**self.params_ages)
        try:
            db.engine.execute(on_duplicate_key_stmt)
            save_to_operation_log(self.request_object, action=self.object.__tablename__, result=200)
            return jsonify({'code': 200, 'msg': f'{self.object.__tablename__}表更新写入完成'})
        except Exception as e:
            db.session.rollback()
            save_to_operation_log(self.request_object, action=self.object.__tablename__, result=500)
            return jsonify({"code": 500, 'msg': str(e)})
        finally:
            db.session.close()

    def operation_query(self, task_filter=None) -> jsonify:
        """
        由于对象不同，task_filter 过滤条件不同
        :param task_filter:
        :return:
        """
        keyword = self.params_ages.get("keyword", 1)
        pageIndex = self.params_ages.get("pageNo", 1)
        pageSize = self.params_ages.get("pageSize", 10)
        task_filter = task_filter if task_filter else {
            or_(
                self.object.AppID.like('%{}%'.format(keyword)),
                self.object.name.like('%{}%'.format(keyword))
            )
        }
        user_query = db.session.query(SsousersModel.username, SsousersModel.displayname).filter().all()
        user_dict = {user[0]: user[1] for user in user_query}
        try:
            if self.is_page:
                query = self.object.query.filter(*task_filter).order_by(self.object.update_time.desc()).limit(pageSize).offset((pageIndex - 1) * pageSize).all() if hasattr(self.object, 'update_time') \
                    else self.object.query.filter(*task_filter).order_by(self.object.id.desc()).limit(pageSize).offset((pageIndex - 1) * pageSize).all()
            else:
                query = self.object.query.filter(*task_filter).order_by(self.object.id.desc()).all()
            totalCount = db.session.query(func.count(self.object.id)).filter(*task_filter).scalar()
            data = self.object.to_all_json(query)  # 结果已出

            if hasattr(self.object, 'update_by'):
                for line in data:
                    line["update_by"] = user_dict[line["update_by"]] if line.get("update_by") else line.get("update_by")
                    line['update_time'] = get_time_stamp(line['update_time'])
            if hasattr(self.object, 'owner'):
                for line in data:
                    line['owner'] = user_dict[line['owner']] if line.get('owner') else line.get('owner')
            if hasattr(self.object, 'create_time'):
                for line in data:
                    line['create_time'] = get_time_stamp(line['create_time'])
            if self.object.__tablename__ == "ci_operation_fileupload":
                for line in data:
                    line['expire_time'] = line.get('expire_time').strftime('%Y-%m-%d') if line.get('expire_time') else line.get('expire_time')
            current_app.logger.info("数据获取成功")
            return jsonify({'code': 200, 'data': data, "totalCount": totalCount})
        except Exception as e:
            current_app.logger.error(str(e))
            return jsonify({'code': 500, 'msg': str(e)})


@ci_sandbox_bp.route('/domainbind/query', methods=['POST'])
def domainbind_query():
    """
    基于表：ci_sandbox_domain_bind表的查询功能
    :return:
    """
    params_ages = get_response(request)
    current_app.logger.info(f"参数:{params_ages}")
    keyword = params_ages.get('keyword', '')
    task_filter = {
        or_(
            CiSandboxDomainBind.domain.like('%{}%'.format(keyword)),
            CiSandboxDomainBind.occupy_sbxid.like('%{}%'.format(keyword))
        )
    }
    DomainBind = DbtableOperation(CiSandboxDomainBind, request)
    result = DomainBind.operation_query(task_filter)
    return result


@ci_sandbox_bp.route('/domainbind/update', methods=['POST'])
def domainbind_update():
    """
    基于表：ci_sanndbox_domain_bind 表的更新写入操作
    :return:
    """
    DomainBind = DbtableOperation(CiSandboxDomainBind, request)
    result = DomainBind.operation_update()
    return result


@ci_sandbox_bp.route('/update/miniappbind', methods=['POST'])
def mini_app_bind_update():
    """
    更新表：ci_sandbox_miniapp_bind
    :return:
    """
    MiniAppBind = DbtableOperation(CiSandboxMiniAppBind, request)
    result = MiniAppBind.operation_update()
    return result


@ci_sandbox_bp.route('/update/officeaccount', methods=['POST'])
def office_account_update():
    """
    更新表：ci_sandbox_officeaccount_bind
    :return:
    """
    OfficeAccount = DbtableOperation(CiSandboxOfficeAccountBind, request)
    result = OfficeAccount.operation_update()
    return result


@ci_sandbox_bp.route('/update/wechatbind', methods=['POST'])
def wechat_bind_update():
    """
    更新表：ci_sandbox_wechat_bind
    :return:
    """
    WechatBind = DbtableOperation(CiSandboxWechatBind, request)
    result = WechatBind.operation_update()
    return result


@ci_sandbox_bp.route('/query/miniappbind', methods=['POST'])
def mini_app_bind_query():
    """
    查询表：ci_sandbox_miniapp_bind
    :return:
    """
    MiniAppBind = DbtableOperation(CiSandboxMiniAppBind, request)
    result = MiniAppBind.operation_query()
    return result


@ci_sandbox_bp.route('/query/officeaccount', methods=['POST'])
def office_account_query():
    """
    查询：ci_sandbox_officeaccount_bind
    :return:
    """
    OfficeAccount = DbtableOperation(CiSandboxOfficeAccountBind, request)
    result = OfficeAccount.operation_query()
    return result


@ci_sandbox_bp.route('/query/wechatbind', methods=['POST'])
def wechat_bind_query():
    """
    查询：ci_sandbox_wechat_bind
    :return:
    """
    WechatBind = DbtableOperation(CiSandboxWechatBind, request)
    result = WechatBind.operation_query()
    return result


@ci_sandbox_bp.route('/sandbox/portbind/export', methods=['POST'])
def sandbox_portbind_export():
    """
    导出工作
    :return:
    """
    params = get_response(request)
    text = "{port}={service}.{sbxid}:{service_port}".format(**params)
    return jsonify({'code': 200, 'data': text})


@ci_sandbox_bp.route('/sandbox/portbind/reload', methods=['POST'])
def sandbox_portbind_reload():
    """
    重启opengw-pod服务
    :return:
    """
    action = "opengw-pod服务"
    username = request.headers.get('X-User')
    local_host = current_app.config.get('LOCAL_HOST')
    local_port = current_app.config.get('LOCAL_PORT')
    try:
        url = f"http://{local_host}:{local_port}/kubectl/api/v1/deployment/restart"
        payload = json.dumps({
            "cluster_name": "test",
            "deployment": "opengw",
            "namespace": "default"
        })
        headers = {
            'Content-Type': 'application/json'
        }
        response = requests.request("POST", url, headers=headers, data=payload)
        current_app.logger.info(f'用户：{username}操作，{response.json()["msg"]}')
        save_to_operation_log(request, action=action, result=200)
        return response.json()
    except Exception as e:
        current_app.logger.error(e)
        save_to_operation_log(request, action=action, result=500)
        return e


class SandBoxPortBindAPI(views.MethodView):
    """
    类视图：基于表ci_sandbox_portbind
    根据请求的方式不同，进行不同的访问
    GET请求：查询操作
    POST请求：添加插入操作
    DELETE请求：删除操作
    """

    def get(self):
        params = get_response(request)
        keyword = params.get('keyword', '')
        pageIndex = int(params.get("pageNo", 1))
        pageSize = int(params.get("pageSize", 10))
        task_filter = {
            or_(
                CiSandboxPortBind.port.like('%{}%'.format(keyword)),
                CiSandboxPortBind.service.like('%{}%'.format(keyword)),
                CiSandboxPortBind.sbxid.like('%{}%'.format(keyword)),
                CiSandboxPortBind.gateway_ip.like('%{}%'.format(keyword))
            )
        }
        query = CiSandboxPortBind.query.filter(*task_filter).order_by(CiSandboxPortBind.create_time.desc()).limit(pageSize).offset((pageIndex - 1) * pageSize).all()
        result = CiSandboxPortBind.to_all_json(query)
        for line in result:
            line['create_time'] = get_time_stamp(line['create_time'])
        return jsonify({"code": 200, "data": result})

    def post(self):
        params_ages = get_response(request)
        params_ages['owner'] = request.headers.get('X-User')
        try:
            if params_ages.get('id'):  # 有ID 进行更新
                id_num = params_ages.get('id')
                res = CiSandboxPortBind.query.filter_by(id=id_num).update(params_ages)
                if res == 1:
                    db.session.commit()
                    current_app.logger.info(f'{CiSandboxPortBind.__tablename__}表，ID={id_num}更新完成')
                    save_to_operation_log(request, action=CiSandboxPortBind.__tablename__, result=200)
                    return jsonify({'code': 200, 'msg': f'{CiSandboxPortBind.__tablename__}表，ID={id_num}更新完成'})
                current_app.logger.warning(f'{CiSandboxPortBind.__tablename__}表，ID={id_num}更新失败')
                save_to_operation_log(request, action=CiSandboxPortBind.__tablename__, result=400)
                return jsonify({'code': 400, 'msg': f'{CiSandboxPortBind.__tablename__}表，ID={id_num}更新失败'})
            # 没有ID进行写入
            insert_stmt = insert(CiSandboxPortBind).values(**params_ages)  # 插入数据
            db.session.execute(insert_stmt)
            db.session.commit()
            current_app.logger.info(f'{CiSandboxPortBind.__tablename__}表写入完成')
            save_to_operation_log(request, action=CiSandboxPortBind.__tablename__, result=200)
            return jsonify({'code': 200, 'msg': f'{CiSandboxPortBind.__tablename__}表写入完成'})
        except Exception as e:
            db.session.rollback()
            current_app.logger.error(f'{CiSandboxPortBind.__tablename__}表写入完成')
            save_to_operation_log(request, action=CiSandboxPortBind.__tablename__, result=500)
            return jsonify({"code": 500, 'msg': str(e)})
        finally:
            db.session.close()

    def delete(self):
        params = get_response(request)
        owner = request.headers.get('X-User')
        if params.get('port'):
            try:
                query = CiSandboxPortBind.query.filter_by(owner=owner, port=params.get('port')).delete()  # 没有删除成功 为0 删除成功为1
                if query == 0:
                    current_app.logger.warning(f'{CiSandboxPortBind.__tablename__} 操作失败，当前用户与创建者不匹配')
                    save_to_operation_log(request, action=CiSandboxPortBind.__tablename__, result=403)
                    return jsonify({'code': 403, 'msg': f'操作失败，当前用户与创建者不匹配'})
                db.session.commit()
                current_app.logger.info(f'{CiSandboxPortBind.__tablename__}表，port为：{params.get("port")}：删除完成')
                save_to_operation_log(request, action=CiSandboxPortBind.__tablename__, result=200)
                return jsonify({'code': 200, 'msg': f'{CiSandboxPortBind.__tablename__}表，port为：{params.get("port")}：删除完成'})
            except Exception as e:
                db.session.rollback()
                db.session.close()
                current_app.logger.error(f'{e}')
                save_to_operation_log(request, action=CiSandboxPortBind.__tablename__, result=500)
                return jsonify({'code': 500, 'msg': e})
        current_app.logger.warning(f'{CiSandboxPortBind.__tablename__}删除操作：传递的参数缺失')
        save_to_operation_log(request, action=CiSandboxPortBind.__tablename__, result=400)
        return jsonify({'code': 400, 'msg': f'{CiSandboxPortBind.__tablename__}删除操作：传递的参数缺失'})


class SandboxConfigTemplate(views.MethodView):
    """
    类试图，基于表ci_sandbox_config_template
    get请求：查询
    post请求：写入配置信息到apollo或者nacos中
    """
    NacosConnection_version_3 = pymysql.Connection(user='nacos', password='nacos@2nacos',
                                                   host='mysql.in.wegoab.com', database='nacos_config',
                                                   port=3306, cursorclass=pymysql.cursors.DictCursor)
    NacosConnection_version_2 = ""

    def get(self):
        """
        :param request:
        :return jsonify:
        """
        params = get_response(request)
        keyword = params.get('keyword', '')
        pageIndex = int(params.get("pageNo", 1))
        pageSize = int(params.get("pageSize", 10))

        task_filter = {
            or_(
                CiSandboxConfigTemplate.AppId.like('%{}%'.format(keyword)),
                CiSandboxConfigTemplate.namespace.like('%{}%'.format(keyword)),
                CiSandboxConfigTemplate.item.like('%{}%'.format(keyword)),
                CiSandboxConfigTemplate.value.like('%{}%'.format(keyword)),
                CiSandboxConfigTemplate.remark.like('%{}%'.format(keyword)),
            )
        }
        try:
            query = CiSandboxConfigTemplate.query.filter(*task_filter).limit(pageSize).offset((pageIndex - 1) * pageSize).all()
            result = CiSandboxConfigTemplate.to_all_json(query)
            for line in result:
                html_dict = {"service": line['AppId'], 'filename': line['namespace'], 'type': CiSandboxConfigTemplate.CHOICE_TYPE_ITEMS[line['type']]}
                line.update(html_dict)
            current_app.logger.info(f"{CiSandboxConfigTemplate.__tablename__}表数据获取成功")
            return jsonify({"code": 200, "data": result})
        except Exception as e:
            current_app.logger.error(e)
            return jsonify({"code": 500, "msg": e})

    def post(self):
        """
        :param request:
        :return jsonify:
        """
        params = get_response(request)
        # 判断是apollo还是nacos
        try:
            if params['type'] == "apollo":
                envid = params.get('envid', 'TEST')  # 默认就是TEST，沙盒测试环境，nacos不需要此参数
                clustername = params.get('sbx_uuid')
                params['appid'] = params['service']
                params['namespace'] = params['filename']
                apo = ApolloWego(envid=envid, clustername=clustername)
                response = apo.change_namespace_items(**params)  # key,value,appid,namespace等参数
                save_to_operation_log(request, action="ConfigTemplate-apollo", result=json.loads(response)['code'])
                return response
            elif CiSandboxConfigTemplate.CHOICE_TYPE_ITEMS.get(params['type']) == 'nacos2.0' or params['type'] == 'nacos2.0':
                return jsonify({'code': 200, 'msg': "nacos2.0版本暂未接入...."})
            elif CiSandboxConfigTemplate.CHOICE_TYPE_ITEMS.get(params['type']) == 'nacos3.0' or params['type'] == 'nacos3.0':
                src_ip = request.remote_addr
                content = code_to_str(params["value"])
                datas = {"data_id": params["filename"], "group_id": params["service"], "content": content, "src_ip": src_ip, "tenant_id": params["sbx_uuid"], "type": params["filename"].split('.')[-1]}
                current_app.logger.info(f"nacos3.0配置写入数据库信息：{datas}")
                update_insert_sql = """INSERT INTO config_info (data_id, group_id, content, src_ip, tenant_id, type) values ("{data_id}","{group_id}","{content}","{src_ip}","{tenant_id}","{type}") 
ON DUPLICATE KEY UPDATE content=VALUES(content)""".format(**datas)
                current_app.logger.info(f'添加nacos3.0语句：{update_insert_sql}')
                cursor = self.NacosConnection_version_3.cursor()
                try:
                    cursor.execute(update_insert_sql)
                    self.NacosConnection_version_3.commit()
                    current_app.logger.info(f'nacos3.0配置添加，修改完成')
                    save_to_operation_log(request, action="ConfigTemplate-nacos3.0", result=200)
                    return jsonify({'code': 200, 'msg': f'nacos3.0配置添加，修改完成'})
                except Exception as e:
                    self.NacosConnection_version_3.rollback()
                    current_app.logger.error(f'nacos3.0配置写入异常，{e}')
                    save_to_operation_log(request, action="ConfigTemplate-nacos3.0", result=500)
                    return jsonify({'code': 500, 'msg': f'nacos3.0配置写入异常，{e}'})
                finally:
                    cursor.close()
                    self.NacosConnection_version_3.close()
            else:
                current_app.logger.warning(f'没有找到apollo，nacos其他的配置源')
                save_to_operation_log(request, action="ConfigTemplate", result=404)
                return jsonify({"code": 404, "msg": "没有找到apollo，nacos其他的配置源"})
        except Exception as e:
            current_app.logger.error(f'{e}')
            save_to_operation_log(request, action="ConfigTemplate", result=500)
            return jsonify({'code': 500, 'msg': e})


@ci_sandbox_bp.route('/sandbox/config/query')
def sandbox_config_query():
    """
    沙盒配置信息获取
    :return:
    """
    params = get_response(request)
    keyword = params.get('keyword', '')
    pageIndex = int(params.get("pageNo", 1))
    pageSize = int(params.get("pageSize", 10))

    try:
        if params.get('type') == 'apollo':
            envid, sbx_uuid, appid, filename = params.get('envid', 'TEST'), params.get('sbx_uuid'), params.get("service"), params.get('filename')
            apo = ApolloWego(envid=envid, clustername=sbx_uuid)
            content = apo.get_cluster_namespace(appid=appid, filename=filename)
            if int(params.get('encryption')) == 1 or params.get('encryption') == "是":
                content = str_to_code(content)
            return jsonify({'code': 200, 'data': {"content": content, "filename": filename, "sbx_uuid": sbx_uuid, "service": appid}})
        elif CiSandboxConfigTemplate.CHOICE_TYPE_ITEMS.get(params['type']) == "nacos2.0" or params['type'] == "nacos2.0":
            return jsonify({"code": 200, "msg": "暂不正常nacos2.0查询"})
        elif CiSandboxConfigTemplate.CHOICE_TYPE_ITEMS.get(params['type']) == "nacos3.0" or params['type'] == "nacos3.0":
            conn = pymysql.Connection(user=current_app.config['NACOS_V3_USER'], password=current_app.config['NACOS_V3_PWD'],
                                      host=current_app.config['NACOS_V3_HOST'], database=current_app.config['NACOS_V3_DATABASE'],
                                      port=3306, cursorclass=pymysql.cursors.DictCursor)
            cursor = conn.cursor()
            select_sql = """select data_id filename, group_id service, tenant_id sbx_uuid,content from config_info 
            where data_id='{filename}' and group_id = '{service}' and tenant_id='{sbx_uuid}'""".format(**params)
            cursor.execute(select_sql)
            result = cursor.fetchone()
            conn.close()
            if int(params.get('encryption')) == 1 or params.get('encryption') == "是":
                result['content'] = str_to_code(result.get('content'))
            return jsonify({'code': 200, 'data': result})
        else:
            current_app.logger.warning(f'没有找到配置')
            return jsonify({"code": 404, "msg": "没有找到配置"})
    except Exception as e:
        current_app.logger.error(e)
        return jsonify({"code": 500, "msg": e})


def get_page_keyword(params):
    pageIndex = int(params.get('pageNo', params.get('pageIndex', 1)))
    pageSize = int(params.get('pageSize', 10))
    key = ""
    if "keyword" in params:
        key = params.get('keyword', '')
    if "inputKeyword" in params:
        key = params.get('inputKeyword', '')
    return key, pageIndex, pageSize


class ViewMethodBase:
    """
    COMMENT 通过传递Model类，请求对象，会自动对请求对象进行处理：主要有名字的处理，关键字的处理，放回页面和页面数量的梳理等
    """

    def __init__(self, ModelObject, request):
        """
        :param ModelObject: Model Object
        :param request: request object
        :param params: table data dict
        """
        self.model_object = ModelObject
        self.request = request
        self.owner = request.headers.get('X-User')
        self.params = get_response(self.request)
        kw, self.pageIndex, self.pageSize = get_page_keyword(self.params)
        ks = get_name_dictionary(kw)  # 姓名转义
        self.keyword = ks if ks else kw

    def view_methods_post_for_table(self):
        """
        类视图的post请求变更
        需要有两个参数：Model_object和params
        """

        try:
            if self.params.get('id'):  # 有ID 进行更新
                id = self.params.get('id')
                res = self.model_object.query.filter_by(id=id).update(self.params)
                print(f"表：{self.model_object.__tablename__}，操作更新id：{int(id)}")
                if res == 1:
                    db.session.commit()
                current_app.logger.info(f'{self.model_object.__tablename__}表，ID={id}更新完成')
                save_to_operation_log(self.request, action=self.model_object.__tablename__, result=200)
                return {'code': 200, 'msg': f'{self.model_object.__tablename__}表，ID={id}更新完成', "later_id": int(id)}
                current_app.logger.warning(f'{self.model_object.__tablename__}表，ID={id}更新失败')
                save_to_operation_log(self.request, action=self.model_object.__tablename__, result=400)
                return {'code': 400, 'msg': f'{self.model_object.__tablename__}表，ID={id}更新失败'}
            # 没有ID进行写入
            self.params.pop("id", "没有找到键：id")  # 当id为空或者不存在时，删除params中的id键
            insert_stmt = insert(self.model_object).values(**self.params)  # 插入数据
            res_add = db.session.execute(insert_stmt)
            print(f"写入表：{self.model_object.__tablename__,}，最后一个ID：{res_add.inserted_primary_key[0]}")
            db.session.commit()
            current_app.logger.info(f'{self.model_object.__tablename__}表 id={res_add.inserted_primary_key[0]}写入完成')
            save_to_operation_log(self.request, action=self.model_object.__tablename__, result=200)
            return {'code': 200, 'msg': f'{self.model_object.__tablename__}表 写入完成', 'later_id': int(res_add.inserted_primary_key[0])}
        except Exception as e:
            db.session.rollback()
            current_app.logger.error(f'{self.model_object.__tablename__}表，POST请求失败')
            save_to_operation_log(self.request, action=self.model_object.__tablename__, result=500)
            return {"code": 500, 'msg': str(e)}
        finally:
            db.session.close()

    def view_methods_delete_for_table(self, task_filter=""):
        """
        类视图：delete请求统一方法
        """
        print(self.params)
        task_filter = {"owner": self.params.get('owner'), "id": self.params.get('id')} if task_filter == "" else task_filter
        if self.params.get('id'):
            try:
                # query = self.model_object.query.filter_by(owner=self.params.get('owner'), id=self.params.get('id')).delete()
                query = self.model_object.query.filter_by(**task_filter).delete()  # 删除失败：0 删除成功：1
                if query == 0:
                    current_app.logger.warning(f'{self.model_object.__tablename__} 操作失败，当前用户与创建者不匹配')
                    save_to_operation_log(self.request, action=self.model_object.__tablename__, result=403)
                    return jsonify({'code': 403, 'msg': f'操作失败，当前用户与创建者不匹配'})
                db.session.commit()
                current_app.logger.info(f'{self.model_object.__tablename__}表，id={self.params.get("id")}：删除完成')
                save_to_operation_log(self.request, action=self.model_object.__tablename__, result=200)
                return jsonify({'code': 200, 'msg': f'{self.model_object.__tablename__}表，id={self.params.get("id")}：删除完成'})
            except Exception as e:
                db.session.rollback()
                db.session.close()
                current_app.logger.error(f'{e}')
                save_to_operation_log(self.request, action=self.model_object.__tablename__, result=500)
                return jsonify({'code': 500, 'msg': e})
        current_app.logger.warning(f'{self.model_object.__tablename__}删除操作：传递的参数缺失')
        save_to_operation_log(self.request, action=self.model_object.__tablename__, result=400)
        return jsonify({'code': 400, 'msg': f'{self.model_object.__tablename__}删除操作：传递的参数缺失'})

    def view_methods_get_for_table(self, task_filter):
        query = self.model_object.query.filter(*task_filter).limit(self.pageSize).offset((self.pageIndex - 1) * self.pageSize).all()
        result = self.model_object.to_all_json(query)
        return result


class CiGrayEventAPI(views.MethodView, ViewMethodBase):
    """
    类视图：基于表ci_grayevent
    根据请求的方式不同，进行不同的访问
    GET请求：查询操作
    POST请求：添加插入操作
    DELETE请求：删除操作
    """

    def __init__(self):
        super().__init__(CiGrayEvent, request)  # 调用父类复制方法，
        # 时间兼容处理
        self.params['expire_time'] = get_time_stamp(self.params['expire_time']) if self.params.get('expire_time') else None
        self.params['create_time'] = get_time_stamp(self.params['create_time']) if self.params.get('create_time') else None
        self.params['update_time'] = get_time_stamp(self.params['update_time']) if self.params.get('update_time') else None

    def get(self):
        global keyword, task_filter
        # 分组和关键字查询
        # 关键字处理
        current_app.logger.info("表：{}，查询关键字：{}".format(CiGrayEvent.__tablename__, self.keyword))
        task_filter = {
            and_(
                or_(
                    CiGrayEvent.eventname.like('%{}%'.format(self.keyword)),
                    CiGrayEvent.flow_uuid.like('%{}%'.format(self.keyword)),
                    CiGrayEvent.owner.like('%{}%'.format(self.keyword))
                ),
                or_(
                    CiGrayEvent.status == 0,
                    CiGrayEvent.status == 1
                ))
        }

        if self.params.get('status') == "history":
            task_filter = {
                and_(
                    or_(
                        CiGrayEvent.eventname.like('%{}%'.format(self.keyword)),
                        CiGrayEvent.flow_uuid.like('%{}%'.format(self.keyword)),
                        CiGrayEvent.owner.like('%{}%'.format(self.keyword))
                    ),
                    CiGrayEvent.status == 2
                )
            }

        # 获取关键字查询的所有结果
        res = self.view_methods_get_for_table(task_filter)

        # 关联ci_grayevent_rule 获取匹配的id的字段
        for line in res:
            line['create_time'] = get_time_stamp(line['create_time'])
            line['expire_time'] = get_time_stamp(line['expire_time'])
            line['update_time'] = get_time_stamp(line['update_time'])
            # line['tag_type'] = CiGrayEvent.CHOICE_TAG_TYPE_ITEMS.get(line['tag_type'])
            rule_query = CiGrayEventRule.query.filter_by(event_id=line['id']).all()
            rule_data = CiGrayEventRule.to_all_json(rule_query)
            line['ruleData'] = rule_data
        res = sorted(res, key=lambda x: x["priority"], reverse=False)
        return jsonify({"code": 200, "data": res, "total": len(res)})

    def post(self):
        return_message = []
        self.params['owner'] = self.owner  # 添加创建人
        ruleData = self.params.get('ruleData')  # 处理数据 ruleData
        self.params.pop("status", "没有找到键：status")  # 删除键:status(ci_grayevent) post请求不允许操作status
        self.params.pop("ruleData", "没有找到键：ruleData")  # 删除键：ruleData(ci_grayevent_rule)
        for key in list(self.params.keys()):
            if self.params.get(key) == 0:
                continue
            if not self.params.get(key):
                self.params.pop(key)
        gray_res = self.view_methods_post_for_table()  # ci_grayevent数据进行写入更新
        last_id = gray_res.get("later_id") if gray_res.get("later_id") else None  # 插入新数据获取last_id，用于rule的event_id
        return_message.append(gray_res)

        # cigrayevent修改时，删除ci_grayevent_rule表与之匹配的规则
        try:
            CiGrayEventRule.query.filter_by(event_id=last_id).delete()
            db.session.commit()
            return_message.append({"code": 200, "msg": f"{CiGrayEventRule.__tablename__},event_id={last_id} 删除成功"})

        except Exception as e:
            current_app.logger.error({"code": 500, "msg": f"删除ci_grayevent_rule表异常，具体错误：{e}"})
            save_to_operation_log(self.request, action=CiGrayEventRule.__tablename__, result=500)
            return_message.append({"code": 500, "msg": f"删除ci_grayevent_rule表异常，具体错误：{e}"})
            return return_message

        # 添加数据
        if ruleData:
            self.model_object = CiGrayEventRule
            for line in ruleData:
                if not line.get('event_id') and last_id:  # 不存在event_id,但数据是新添加的（last_id is true）,则添加event_id
                    line['event_id'] = last_id
                if line.get('id'):
                    line.pop('id', '没有找到键：id')
                self.params = line
                print(f"ruleData数据为{line}")
                return_message.append(self.view_methods_post_for_table())
        return jsonify({"msg": return_message})

    # def delete(self):
    #     res = self.view_methods_delete_for_table()
    #     return res


class CiGrayEventRuleAPI(views.MethodView, ViewMethodBase):
    """
    类视图：基于表ci_grayevent_rule
    GET请求：查询操作
    POST请求：添加插入操作
    DELETE请求：删除操作
    """

    def __init__(self):
        super().__init__(CiGrayEventRule, request)

    def get(self):
        current_app.logger.info("表：{}，查询关键字：{}".format(CiGrayEventRule.__tablename__, self.keyword))
        task_filter = {
            or_(
                CiGrayEventRule.rule_name.like('%{}%'.format(self.keyword)),
                CiGrayEventRule.rule_type.like('%{}%'.format(self.keyword)),
                CiGrayEventRule.rule_value.like('%{}%'.format(self.keyword)),
                CiGrayEventRule.event_id.like('%{}%'.format(self.keyword))
            )
        }
        res = self.view_methods_get_for_table(task_filter)
        return jsonify({"code": 200, "data": res, "total": len(res)})

    def post(self):
        res = self.view_methods_post_for_table()
        return jsonify(res)

    # def delete(self):
    #     res = self.view_methods_delete_for_table()
    #     return res


class NssVersionCfgAPI(views.MethodView, ViewMethodBase):
    """
    类视图：基于表nss_version_cfg
    GET请求：查询操作
    POST请求：添加插入操作
    DELETE请求：删除操作
    """

    def __init__(self):
        super().__init__(NssVersionCfg, request)

    def get(self):
        sql = """
        SELECT
            c.type,
            c.project_name,
            l.`version`,
            c.approve_time
        FROM
            nss_version_cfg c
            JOIN nss_versionlist l ON l.id = c.version_build_id
        WHERE
            status = 1
            AND c.env = 0
            AND l.env = 0;
        """
        print(sql)
        query = db.session.execute(sql)
        data = [dict(zip(result.keys(), result)) for result in query]
        for line in data:
            line['approve_time'] = get_time_stamp(line['approve_time'])
        return jsonify({"code": 200, "data": data, "total": len(data)})


class NssProjectCfgAPI(views.MethodView, ViewMethodBase):
    def __init__(self):
        super().__init__(NssProjectCfg, request)

    def get(self):
        task_filter = {
            or_(
                NssProjectCfg.location.like('%{}%'.format(self.keyword)),
                NssProjectCfg.type.like('%{}%'.format(self.keyword)),
            )
        }
        res = self.view_methods_get_for_table(task_filter)
        for line in res:
            line['update_time'] = get_time_stamp(line['update_time'])
        return jsonify({"code": 200, "data": res, "total": len(res)})

    def post(self):
        res = self.view_methods_post_for_table()
        return jsonify(res)

    # def delete(self):
    #     pass


class NssVersionListApi(views.MethodView, ViewMethodBase):
    """
    类视图：基于表nss_version_list
    GET请求：查询操作
    POST请求：添加插入操作
    DELETE请求：删除操作
    """

    def __init__(self):
        super().__init__(NssVersionList, request)

    def get(self):
        task_filter = {
            and_(
                or_(
                    NssVersionList.version.like('%{}%'.format(self.keyword)),
                ),
                NssVersionList.env == 0,
                NssVersionList.project_name == self.params.get('project_name'), )
        }

        query = NssVersionList.query.filter(*task_filter).order_by(NssVersionList.last_build_time.desc()).limit(self.pageSize).offset((self.pageIndex - 1) * self.pageSize).all()
        res = NssVersionList.to_all_json(query)
        for line in res:
            line['last_build_time'] = get_time_stamp(line['last_build_time'])
        return jsonify({"code": 200, "data": res, "total": len(res)})


update_nss_cfg_base = """UPDATE `{npconfig}` SET project_name='{project}' where type='{tag}'"""
update_nss_cfg_entry = """UPDATE `{nssversioncfg}` SET status=3, expire_time=now() WHERE project_name='{project_name}' AND status=1 AND env=0 AND type='{tag}';"""
update_nss_cfg_portal = """UPDATE `{nssversioncfg}` SET status=3, expire_time=now() WHERE project_name='{project_name}' AND status=1 AND env=0 AND type='{tag}';"""
update_nss_cfg_increase = """UPDATE `{nssversioncfg}` SET status=3, expire_time=now() WHERE project_name='{project_name}' AND status=1 AND env=0 AND type='{tag}';"""
insert_nss_cfg = """INSERT INTO `{nssversioncfg}`(env,project_name,version_build_id,`type`,apply_time,`status`,applicant,approve_time,approver) SELECT 0, '{project_name}', id, '{tag}', now(), 1, '{x_user}',now(), '{x_user}' FROM `{nvlist}` WHERE project_name='{project_name}' AND env=0 AND version='{version}';"""  # version的取值为：version_entry}/{version_portal}


## nss 配置表UPDATE事务操作接口
def nss_total_of_parameters(request, tag):
    """
    nss数据表事务语句拼装创建 实际业务操作在于sql语句。
    :param request:
    :param tag: 环境区分：prd，stg，canary
    :return: tuple:(sql_list, log_dict)
        sql_list: 执行写入与更新语句列表
        log_dict：日志信息字典
    """
    ### 指定数据表
    npconfig = "nss_project_cfg"
    nvlist = "nss_versionlist"
    nssversioncfg = "nss_version_cfg"

    ### 获取相关参数
    data_raw = get_response(request)
    project = data_raw.get('project')
    version_entry = data_raw.get('version_entry')
    version_portal = data_raw.get('version_portal')
    version_increase = data_raw.get('version_increase')
    version_login = data_raw.get('version_login')
    version_article = data_raw.get('version_article')
    version_weshop = data_raw.get('version_weshop')
    operator = request.headers.get('X-User')

    # 组合参数，替换sql语句
    params_dict = {"project": project, "npconfig": npconfig, "nvlist": nvlist, "nssversioncfg": nssversioncfg, "x_user": operator, "tag": tag}  # 语句拼装
    sql_result = []
    if project:
        update_sql_1 = update_nss_cfg_base.format(**params_dict)
        sql_result.append(update_sql_1)
        if version_entry:
            params_dict["version"] = version_entry
            params_dict['project_name'] = "wsxc_entry"
            update_sql_2 = update_nss_cfg_entry.format(**params_dict)
            sql_result.append(update_sql_2)
            insert_sql = insert_nss_cfg.format(**params_dict)
            sql_result.append(insert_sql)
        if version_portal:
            params_dict["version"] = version_portal
            params_dict['project_name'] = "wsxc_portal"
            update_sql_2 = update_nss_cfg_portal.format(**params_dict)
            sql_result.append(update_sql_2)
            insert_sql = insert_nss_cfg.format(**params_dict)
            sql_result.append(insert_sql)
        if version_increase:
            params_dict["version"] = version_increase
            params_dict['project_name'] = "increase_performance"
            update_sql_2 = update_nss_cfg_increase.format(**params_dict)
            sql_result.append(update_sql_2)
            insert_sql = insert_nss_cfg.format(**params_dict)
            sql_result.append(insert_sql)
        if version_login:
            params_dict["version"] = version_login
            params_dict['project_name'] = "wsxc_silentLogin"
            update_sql_2 = update_nss_cfg_increase.format(**params_dict)
            sql_result.append(update_sql_2)
            insert_sql = insert_nss_cfg.format(**params_dict)
            sql_result.append(insert_sql)
        if version_article:
            params_dict["version"] = version_article
            params_dict['project_name'] = "wsxc_article"
            update_sql_2 = update_nss_cfg_increase.format(**params_dict)
            sql_result.append(update_sql_2)
            insert_sql = insert_nss_cfg.format(**params_dict)
            sql_result.append(insert_sql)
        if version_weshop:
            params_dict["version"] = version_weshop
            params_dict['project_name'] = "wsxc_weshop"
            update_sql_2 = update_nss_cfg_increase.format(**params_dict)
            sql_result.append(update_sql_2)
            insert_sql = insert_nss_cfg.format(**params_dict)
            sql_result.append(insert_sql)
    log_dict = {"operator": operator, "operated_action": ",".join(sql_result),
                "operated_time": datetime.now(), "operated_url": request.base_url,
                "affected_table": "{},{},{}".format(npconfig, nvlist, nssversioncfg), "operated_tag": tag}  # 日志拼装
    return sql_result, log_dict


def sql_the_transaction(sql_list):
    """
    执行事务
    :param sql_list: 需要执行的sql语句
    :return:
    """
    HOSTNAME = current_app.config.get('HOSTNAME')
    PORT = current_app.config.get('PORT')
    DATABASE = current_app.config.get('DATABASE')
    USERNAME = current_app.config.get('USERNAME')
    PASSWORD = current_app.config.get('PASSWORD')
    conn = pymysql.Connection(user=USERNAME, password=PASSWORD, host=HOSTNAME, database=DATABASE, port=int(PORT), cursorclass=pymysql.cursors.DictCursor)
    cur = conn.cursor()
    try:
        for sql_line in sql_list:
            print(sql_line)
            cur.execute(sql_line)
            current_app.logger.info(f"nss静态资源配置，执行操作：{sql_line}")
        conn.commit()
        return True
    except Exception as e:
        current_app.logger.error(f'nss 静态资源配置，写入异常\n{e}')
        conn.rollback()
        return "操作失败"
    finally:
        cur.close()
        conn.close()


def nss_update(request, tag):
    sql_list, log_dict = nss_total_of_parameters(request, tag)
    action = 'nss静态资源配置'
    if len(sql_list) == 0:
        log_dict["operated_code"] = 400
        save_to_operation_log(request, action=action, result=400)
        save_nss_operation_log(log_dict)
        return jsonify({"code": 400, "msg": "参数缺失，不进行操作"})
    result = sql_the_transaction(sql_list)
    if result is True:
        log_dict["operated_code"] = 200
        save_to_operation_log(request, action=action, result=200)
        save_nss_operation_log(log_dict)
        return jsonify({"code": 200, "msg": "操作完成"})
    log_dict["operated_code"] = 500
    save_to_operation_log(request, action=action, result=500)
    save_nss_operation_log(log_dict)
    return jsonify({"code": 500, "msg": result})


# 申请人 审批人 现阶段都使用X-User
@ci_sandbox_bp.route('/nss/versioncfg/updatePrd', methods=['POST'])
def nss_update_prd():
    nss_tag = "prd"
    return nss_update(request, tag=nss_tag)


@ci_sandbox_bp.route('/nss/versioncfg/updateStg', methods=['POST'])
def nss_update_stg():
    nss_tag = "stg"
    return nss_update(request, tag=nss_tag)


@ci_sandbox_bp.route('/nss/versioncfg/updateCanary', methods=['POST'])
def nss_update_canary():
    nss_tag = "canary"
    return nss_update(request, tag=nss_tag)


@ci_sandbox_bp.route('/grayevent/updateStatus', methods=['POST'])
def ci_grayevent_status():
    """
    此接口只修改ci_grayevent的status状态
    :return:
    """
    params = get_response(request)
    cge_object = ViewMethodBase(CiGrayEvent, request)
    params.pop("ruleData", "没有找到键：ruleData")  # 删除键：ruleData(ci_grayevent_rule)
    res = cge_object.view_methods_post_for_table()
    return jsonify(res)


class NssBuildInfoView(views.MethodView):
    def get(self):
        env_name = request.args.get('env_name')
        data_raw = get_response(request)
        pageNo = int(data_raw.get("pageNo", 1))  # 页数
        pageSize = int(data_raw.get("pageSize", 10))  # 每页显示
        if env_name == 'prod':
            select_sql = '''
            SELECT
                n.project_name,n.env_name,n.`version`,n.build_size,n.last_build_time, if(t.id is null,'','1') as 'tested' , if(t.id is null,'', t.create_time) as 'test_time'
            FROM
                nss_build_info n
                INNER JOIN (
                        SELECT
                                project_name,version,
                                MAX(last_build_time) AS latest_build_time
                        FROM
                                nss_build_info
                        WHERE
                                env_name='prod'
                        GROUP BY
                                project_name, version) latest ON n.project_name = latest.project_name AND n.last_build_time = latest.latest_build_time
                LEFT JOIN nss_net_weak_test t on t.project_name = n.project_name and t.`version`=n.`version` and  t.status=1
            order by 6 desc ,last_build_time desc'''
            try:
                results = db.session.execute(select_sql).fetchall()
                total = len(results)
                result = [dict(row) for row in results]
                for line in result:
                    line['last_build_time'] = get_time_stamp(line['last_build_time'])
                result_data = result[((pageNo - 1) * pageSize):(pageNo * pageSize)]
                return jsonify({"data": result_data, "code": 200, "total": total})
            except Exception as e:
                return jsonify({'code': 500, 'msg': e})
        else:
            return query_build_info(env_name)


def query_build_info(env_name):
    sql = '''
    SELECT n.*, t.`size` as "tested_size", concat (ROUND((n.build_size - t.`size`)/ t.`size` * 100,2), '%') as "rate"
    FROM wego_ops.nss_build_info n
    INNER JOIN (
            SELECT project_name, MAX(last_build_time) AS latest_build_time
            FROM wego_ops.nss_build_info
            WHERE env_name=:env_name
            GROUP BY project_name) latest ON n.project_name = latest.project_name AND n.last_build_time = latest.latest_build_time
    LEFT JOIN wego_ops.nss_net_weak_test t on t.project_name = n.project_name 
    WHERE t.status = 1'''
    try:
        results = db.session.execute(sql, {'env_name': env_name}).fetchall()
        total = len(results)
        result = [dict(row) for row in results]
        for line in result:
            line['last_build_time'] = get_time_stamp(line['last_build_time'])
        return jsonify({"data": result, "code": 200, "total": total})
    except Exception as e:
        return jsonify({'code': 500, 'msg': e})


class CiCloudUsersAPI(views.MethodView):
    def get(self):
        data_raw = get_response(request)
        pageIndex = int(data_raw.get("pageNo", 1))
        pageSize = int(data_raw.get("pageSize", 10))
        keyword = data_raw.get("keyword", "")
        task_filter = and_(
            or_(
                CiCloudUsers.platform.like(f'%{keyword}%'),
                CiCloudUsers.account_name.like(f'%{keyword}%'),
                CiCloudUsers.apply_user.like(f'%{keyword}%'),
                CiCloudUsers.account_id.like(f'%{keyword}%'),
                CiCloudUsers.secretId.like(f'%{keyword}%'),
            ),
            CiCloudUsers.isApi == 1
        )

        users = CiCloudUsers.query.filter(task_filter).order_by(CiCloudUsers.update_time.desc()).limit(
            pageSize).offset((pageIndex - 1) * pageSize).all()
        total = CiCloudUsers.query.filter(task_filter).count()
        users = CiCloudUsers.to_all_json(users)
        for user in users:
            if 'secretKey' in user:
                user.pop('secretKey')
            if 'id' in user:
                user.pop('id')
            user['update_time'] = get_time_stamp(user['update_time'])
            user['create_time'] = get_time_stamp(user['create_time'])
        return jsonify({'data': users, 'total': total})

    def post(self):
        data = request.get_json()
        try:
            user = CiCloudUsers()
            for key, value in data.items():
                setattr(user, key, value)
            db.session.add(user)
            db.session.commit()
            return jsonify({'message': 'User created successfully'}), 200
        except Exception as e:
            return jsonify({'message': f'{e}'}), 500

    def put(self):
        data = request.get_json()
        account_id = data['account_id']
        user = CiCloudUsers.query.filter_by(account_id=account_id).first()
        if not user:
            return jsonify({'message': '用户不存在'}), 404
        current_user_id = request.headers.get('X-User')
        if current_user_id not in devops_users:
            return jsonify({'message': '权限不足'}), 403
        if 'secretKey' in data:
            data.pop('secretKey')
        for key, value in data.items():
            setattr(user, key, value)
        db.session.commit()
        return jsonify({'message': '用户信息已成功更新'}), 200


def cloud_user_change():
    data = get_response(request)
    account_id = data.get('account_id')
    if account_id:
        if request.method == 'POST':
            user = CiCloudUsers.query.filter_by(account_id=account_id).first()
            if not user:
                return jsonify({'message': '用户不存在'}), 404
            current_user_id = request.headers.get('X-User')
            if current_user_id not in devops_users:
                return jsonify({'message': '权限不足'}), 403
            for key, value in data.items():
                setattr(user, key, value)
            db.session.commit()
            return jsonify({'message': '用户信息已成功更新'}), 200
        if request.method == 'GET':
            user = CiCloudUsers.query.filter_by(account_id=account_id).first()
            total = CiCloudUsers.query.filter_by(account_id=account_id).count()
            data = CiCloudUsers.to_one_json(user)
            data['update_time'] = get_time_stamp(data['update_time'])
            data['create_time'] = get_time_stamp(data['create_time'])
            if data['secretKey']:
                data['ESK'] = AES_encrypt(org_str=data['secretKey'], key=current_app.config.get('AES_KEY'))
            return jsonify({'data': data, 'total': total}), 200
        return jsonify({'msg': '请求方式异常'}), 405
    return jsonify({'msg': '参数缺失'}), 400


@ci_sandbox_bp.route('/nss/netweaktest/update', methods=['POST'])
def update_nss_net_weak_test():
    """
    Add a new data to nss_net_weak_test table.

    This API only allows to add new data, not delete. Each time a new data is written,
    the status of the old data will be set to invalid.

    ---
    tags:
      - nss_net_weak_test
    parameters:
      - name: X-User
        in: header
        type: string
        required: true
        description: The owner of the data.
      - name: body
        in: body
        schema:
          type: object
          required:
            - project_name
            - version
            - size
          properties:
            project_name:
              type: string
              description: The name of the project.
            version:
              type: string
              description: The version of the project.
            size:
              type: integer
              description: The size of the project.
    responses:
      200:
        description: Success.
      400:
        description: Invalid input parameters.
      500:
        description: Internal server error.
    """
    data = get_response(request)
    project_name = data.get('project_name')
    version = data.get('version')
    size = data.get('size')
    status = data.get('status')
    owner = request.headers.get('X-User')

    # 设置旧数据的状态为 2
    old_data = NssNetWeakTest.query.filter_by(project_name=project_name, status=1).first()
    if old_data:
        old_data.status = 2
        db.session.add(old_data)
        db.session.commit()

    # 插入新数据
    new_data = NssNetWeakTest(
        project_name=project_name,
        version=version,
        size=size,
        status=status,
        owner=owner
    )
    db.session.add(new_data)
    db.session.commit()

    return jsonify({'code': 0, 'msg': 'Success'}), 200


ci_sandbox_bp.add_url_rule('/cloud/user', view_func=CiCloudUsersAPI.as_view('ci_cloud_users_api'))
ci_sandbox_bp.add_url_rule('/cloud/user/changesk', view_func=cloud_user_change, methods=['POST', 'GET'])
ci_sandbox_bp.add_url_rule(rule='/nss/buildinfo', view_func=NssBuildInfoView.as_view('build_info_api'))
ci_sandbox_bp.add_url_rule(rule='/nss/buildinfo', view_func=query_build_info, methods=['GET'])
ci_sandbox_bp.add_url_rule(rule='/sandbox/portbind', view_func=SandBoxPortBindAPI.as_view('sandbox_port_bind_api'))  # 添加类视图
ci_sandbox_bp.add_url_rule(rule='/sandbox/config/template', view_func=SandboxConfigTemplate.as_view('sandbox_config_template_api'))  # 添加类视图
ci_sandbox_bp.add_url_rule(rule='/grayevent', view_func=CiGrayEventAPI.as_view('ci_grayevent_api'))
ci_sandbox_bp.add_url_rule(rule='/grayevent/rule', view_func=CiGrayEventRuleAPI.as_view('ci_grayevent_rule_api'))
ci_sandbox_bp.add_url_rule(rule='/nss/versioncfg', view_func=NssVersionCfgAPI.as_view('nss_version_cfg_api'))
ci_sandbox_bp.add_url_rule(rule='/nss/projectcfg', view_func=NssProjectCfgAPI.as_view('nss_project_cfg_api'))
ci_sandbox_bp.add_url_rule(rule='/nss/versionlist', view_func=NssVersionListApi.as_view('nss_versionlist_api'))
