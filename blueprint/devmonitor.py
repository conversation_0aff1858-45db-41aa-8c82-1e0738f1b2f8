#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
@Title:     devmonitor.py
@Date:      2024/1/15 11:40
@Author:    WangLH
@Desc:      运维监控面板后端接口
"""
from datetime import timedelta
from typing import Tuple

from flask import Blueprint, make_response, request, views
from sqlalchemy import Integer, case, text
from sqlalchemy.exc import SQLAlchemyError

from blueprint.cicd import get_page_keyword
from blueprint.useritsm import get_name_dictionary
from utils import *

devmonitor_bp = Blueprint("devmonitor_blue", __name__, url_prefix="/devmonitor/api/v1")


@devmonitor_bp.route("/kafka/nginx/error")
def kafka_nginx_error():
    try:
        ModelObject = KafkaNginxError
        dataraw = get_response(request)
        today = datetime.now()
        start_of_week = today - timedelta(days=today.weekday())
        end_of_week = start_of_week + timedelta(days=6)
        start_of_week_str = start_of_week.strftime("%Y-%m-%d 00:00:00")
        end_of_week_str = end_of_week.strftime("%Y-%m-%d 23:59:59")
        # 没有时间默认获取一周的数据
        startTime = dataraw.get("BeginTime", start_of_week_str)
        endTime = dataraw.get("EndTime", end_of_week_str)

        granularity = dataraw.get("Granularity", "10m")
        time_format = get_time_format(granularity, ModelObject.ts)
        group_by_time = func.DATE_FORMAT(ModelObject.ts, time_format).label("time")
        # 根据传递的url修改查询条件
        url = dataraw.get("url", "")
        if url:
            task_filter = {
                and_(ModelObject.ts.between(startTime, endTime), ModelObject.url == url)
            }
            sum_query = (
                ModelObject.query.filter(*task_filter)
                .with_entities(
                    group_by_time,
                    ModelObject.url,
                    func.sum(ModelObject.count).label("sumValue"),
                )
                .group_by(group_by_time)
                .order_by(group_by_time.asc())
                .all()
            )
        else:
            sum_query = (
                ModelObject.query.filter(ModelObject.ts.between(startTime, endTime))
                .with_entities(
                    group_by_time,
                    ModelObject.url,
                    func.sum(ModelObject.count).label("sumValue"),
                )
                .group_by(group_by_time, ModelObject.url)
                .order_by(
                    group_by_time.desc(), text("sumValue desc")
                )  # 添加order_by子句
                .all()
            )

        urls_query = (
            ModelObject.query.filter(ModelObject.ts.between(startTime, endTime))
            .with_entities(ModelObject.url)
            .distinct()
            .all()
        )

        urls_items = [{"value": i.url, "label": i.url} for i in urls_query]

        sum_data = [
            {
                "time": row.time,
                "url": row.url,
                "value": int(row.sumValue) if row.sumValue is not None else 0,
            }
            for row in sum_query
        ]
        sum_termsCount = len(sum_query)
        save_to_operation_log(request, action=ModelObject.__table__, result=200)
        return (
            jsonify(
                {"data": sum_data, "termsCount": sum_termsCount, "urls": urls_items}
            ),
            200,
        )
        return jsonify({"urls": urls_items}), 200
    except Exception as e:
        return jsonify({"msg": str(e)}), 500


@devmonitor_bp.route("/kafka/code/error")
def kafka_code_error():
    try:
        dataraw = get_response(request)
        today = datetime.now()
        startTime = dataraw.get("BeginTime", today)

        if bool(startTime) is False:
            startTime = today
        gra_keys = ["granularity", "Granularity"]
        granularity = dataraw.get(
            next((k for k in gra_keys if k in dataraw), None), "10m"
        )
        if "m" in granularity:
            interval = int("".join(filter(str.isdigit, granularity)))
        elif "h" in granularity:
            interval = int("".join(filter(str.isdigit, granularity))) * 60
        else:
            interval = 1

        end_time = (
            datetime.strptime(startTime, "%Y-%m-%d %H:%M:%S")
            if isinstance(startTime, str)
            else startTime
        )
        start_time = end_time - timedelta(minutes=interval)

        ModelObject = KafkaErrorCode
        keyword = dataraw.get("inputKeyword", "")
        task_filter = []

        if keyword:
            keyword_condition = or_(
                ModelObject.err_code.like("%{}%".format(keyword)),
                ModelObject.url.like("%{}%".format(keyword)),
                ModelObject.err_msg.like("%{}%".format(urllib.parse.quote(keyword))),
            )
            task_filter.append(keyword_condition)

        time_condition = ModelObject.ts.between(start_time, end_time)
        task_filter.append(time_condition)

        sum_query = (
            ModelObject.query.filter(and_(*task_filter))
            .with_entities(
                ModelObject.err_code,
                ModelObject.err_msg,
                func.sum(ModelObject.count).label("sumCount"),
                func.group_concat(ModelObject.url, separator=",").label("urls"),
            )
            .group_by(ModelObject.err_code, ModelObject.err_msg)
            .all()
        )

        all_code_query = (
            ModelObject.query.filter(
                ModelObject.ts.between(end_time - timedelta(minutes=30), end_time)
            )
            .group_by(ModelObject.err_code)
            .all()
        )

        all_code_items = [i.err_code for i in all_code_query] if all_code_query else []

        kafka_base_domain = "https://kib.in.szwego.com/app/kibana#/discover/376b0950-c8cb-11eb-beaf-012b7c96e129?_g=(filters:!(),refreshInterval:(pause:!t,value:0),time:(from:now-{},to:now))&_a=(columns:!(status,npath,txid,x_errcode),filters:!(),index:'7aa73370-4800-11eb-8024-6335eded8129',interval:auto,query:(language:kuery,query:'{}'),sort:!(!('@timestamp',desc)))"
        sum_data = []
        for row in sum_query:
            row_dict = {
                "err_code": row.err_code,
                "err_msg": urllib.parse.unquote(row.err_msg),
                "sumCount": int(row.sumCount) if row.sumCount is not None else 0,
                "urls": [],
                "urls_count": 0,
            }

            urls = set(row.urls.split(","))
            for url in urls:
                query = f'npath:"{url}" and x_errcode:{row.err_code}'
                encoded_query = urllib.parse.quote(query, safe=":")
                row_dict["urls"].append(
                    {
                        "url": url,
                        "href": kafka_base_domain.format(granularity, encoded_query),
                    }
                )
            row_dict["urls_count"] = len(urls)
            sum_data.append(row_dict)

        sum_data = sorted(sum_data, key=lambda x: x["sumCount"], reverse=True)

        sum_termsCount = len(sum_data)
        countError = sum(item["sumCount"] for item in sum_data)
        save_to_operation_log(request, action=ModelObject.__table__, result=200)
        return (
            jsonify(
                {
                    "data": sum_data,
                    "termsCount": sum_termsCount,
                    "countError": countError,
                    "allCodeError": all_code_items,
                }
            ),
            200,
        )
    except Exception as e:
        return jsonify({"msg": str(e)}), 500


@devmonitor_bp.route("/kafka/code/list")
def kafka_code_list():
    try:
        startTime = datetime.now()
        end_time = (
            datetime.strptime(startTime, "%Y-%m-%d %H:%M:%S")
            if isinstance(startTime, str)
            else startTime
        )
        ModelObject = KafkaErrorCode
        all_code_query = (
            ModelObject.query.filter(
                ModelObject.ts.between(end_time - timedelta(minutes=30), end_time)
            )
            .group_by(ModelObject.err_code)
            .all()
        )

        all_code_items = [i.err_code for i in all_code_query] if all_code_query else []
        return jsonify({"data": all_code_items, "msg": "获取成功"}), 200
    except Exception as e:
        current_app.logger.error(f"{e}")
        return jsonify({"msg": str(e)}), 500


@devmonitor_bp.route("/kafka/code/query", methods=["POST"])
def kafka_code_query():
    """
    需要区分granularity为 5m 还是 10m
    根据所选的granularity与 10m 进行一个整除判断，为整数则查询表 kafka_error_code_10m，不为整数则查询 kafka_err_code 表
    :return:
    """
    try:
        dataraw = get_response(request)

        gra_keys = ["granularity", "Granularity"]
        granularity = dataraw.get(
            next((k for k in gra_keys if k in dataraw), None), "10m"
        )

        granularity_minutes = int("".join(filter(str.isdigit, granularity)))
        is_divisible_by_10 = granularity_minutes % 10 == 0
        interval = 1440 if is_divisible_by_10 else 360
        granularity_seconds = granularity_minutes * 60

        ModelObject = KafkaErrorCode10m if is_divisible_by_10 else KafkaErrorCode

        # 处理时间
        end_time = dataraw.get("EndTime")
        end_time = (
            datetime.strptime(end_time, "%Y-%m-%d %H:%M:%S")
            if end_time and isinstance(end_time, str)
            else datetime.now()
        )

        start_time = dataraw.get("BeginTime")
        start_time = (
            datetime.strptime(start_time, "%Y-%m-%d %H:%M:%S")
            if start_time and isinstance(start_time, str)
            else end_time - timedelta(minutes=interval)
        )

        keyword = dataraw.get("inputKeyword", "")
        if keyword == "":
            return jsonify({"msg": "err_code错误"}), 500

        # 处理 url 并构建查询
        urls = dataraw.get("urls", "").split(",")
        url_filter = ModelObject.url.in_(urls) if urls else None
        start_time = (
            start_time.strftime("%Y-%m-%d %H:%M:%S")
            if isinstance(start_time, str) is False
            else start_time
        )
        end_time = (
            end_time.strftime("%Y-%m-%d %H:%M:%S")
            if isinstance(end_time, str) is False
            else end_time
        )

        task_filter = {
            and_(
                ModelObject.ts.between(start_time, end_time),
                ModelObject.err_code == int(keyword),
                url_filter,
            )
        }

        sum_query = (
            ModelObject.query.filter(*task_filter)
            .with_entities(
                func.from_unixtime(
                    func.floor(
                        func.unix_timestamp(ModelObject.ts) / granularity_seconds
                    )
                    * granularity_seconds
                ).label("time_group"),
                ModelObject.url,
                ModelObject.err_code,
                func.sum(ModelObject.count).label("count"),
            )
            .group_by("time_group", ModelObject.err_code, ModelObject.url)
            .order_by("time_group")
            .all()
        )

        sum_data = []
        for row in sum_query:
            result_dict = {
                "time": get_time_stamp(row.time_group),
                "url": row.url,
                "err_code": row.err_code,
                "count": int(row.count),
            }
            sum_data.append(result_dict)
        return jsonify({"data": sum_data}), 200
    except Exception as e:
        return jsonify({"msg": str(e)}), 500


def get_display_name(data: list):
    user_query = (
        db.session.query(SsousersModel.username, SsousersModel.displayname)
        .filter()
        .all()
    )
    userdict = {user.username: user.displayname for user in user_query}
    for item in data:
        for k, v in item.items():
            if isinstance(v, datetime):
                item[k] = v.strftime("%Y-%m-%d %H:%M:%S")
        receivers = item.get("receiver", "").split(",")
        if "all" in receivers:
            item["displayname"] = "所有人"
        else:
            display_names = [userdict.get(name) for name in receivers if name != "all"]
            # 如果接收人列表为空，直接设置为 ""
            item["displayname"] = ",".join(display_names) if display_names else ""
    return data


class BaseConfigView(views.MethodView):
    model = None  # 子类应覆盖此属性以指定模型

    def get_query_filters(self, keyword):
        """生成查询过滤条件，子类实现具体逻辑"""
        raise NotImplementedError

    def get(self):
        """查询操作"""
        kw, pageIndex, pageSize = get_page_keyword(get_response(request))
        ks = get_name_dictionary(kw)  # 姓名转义
        keyword = ks if ks else kw
        filters = self.get_query_filters(keyword)
        current_app.logger.info(
            "表：{}，查询关键字：{}".format(self.model.__tablename__, keyword)
        )
        query = self.model.query.filter(*filters).all()
        totalCount = self.model.query.count()
        data = self.model.to_all_json(query)
        data = self.process_query_result(data)
        return jsonify({"code": 200, "data": data, "totalCount": totalCount}), 200

    def process_query_result(self, data: list):
        """钩子方法：允许子类自定义处理查询结果数据。默认实现直接返回数据。"""
        raise NotImplementedError

    def prepare_params(self, params):
        """清理参数，移除None值，保留0值"""
        return {k: v for k, v in params.items() if v is not None or v == 0}

    def update_record(self, update_conditions, update_values):
        """更新记录，子类可重写提供具体逻辑"""
        current_app.logger.info(
            f"Model: {self.model} update_conditions:{update_conditions} update_values:{update_values}"
        )
        try:
            if update_conditions != {}:
                record = self.model.query.filter_by(**update_conditions).first()
                if record:
                    for key, value in update_values.items():
                        setattr(record, key, value)
            else:
                # 如果未找到记录，则创建新记录
                new_record_data = {**update_conditions, **update_values}
                print(new_record_data)
                current_app.logger.info(
                    f"Model: {self.model} update_data:{new_record_data}"
                )
                record = self.model(**new_record_data)  # 创建新记录实例
                db.session.add(record)  # 添加新记录到会话
            db.session.commit()
            return jsonify({"code": 200, "msg": "更新成功"}), 200
        except Exception as e:
            db.session.rollback()
            return jsonify({"code": 500, "msg": str(e)}), 500

    def post(self):
        params = self.prepare_params(request.json)
        update_conditions = self.get_update_conditions(params)
        return self.update_record(update_conditions, params)

    def get_update_conditions(self, params):
        """生成更新条件，子类实现具体逻辑"""
        return {}
        # raise NotImplementedError


class KafkaAlertFire(BaseConfigView):
    model = ErrAlertFiringModel

    def get_query_filters(self, keyword):
        return [
            or_(
                self.model.level.like("%{}%".format(keyword)),
                self.model.title.like("%{}%".format(keyword)),
                self.model.receiver.like("%{}%".format(keyword)),
                self.model.content.like("%{}%".format(keyword)),
            ),
        ]

    def get_update_conditions(self, params):
        return {k: params[k] for k in ["id"] if k in params}

    def process_query_result(self, data: list):
        return get_display_name(data)

    def get(self):
        """重写的查询操作，包括排序和分页，以及历史告警总数和当前告警总数"""
        _, pageIndex, pageSize = get_page_keyword(get_response(request))
        current_app.logger.info("参数具体信息：{}".format(get_response(request)))
        kw = get_response(request).get("inputKeyword", "")
        ks = get_name_dictionary(kw)  # 姓名转义
        keyword = ks if ks else kw

        # 基础查询，统计总数
        base_query = self.model.query
        total_count = base_query.count()

        # 统计不同状态的告警数
        status_1_count = base_query.filter_by(status=1).count()
        not_status_1_count = total_count - status_1_count

        # 根据 keyword 进行过滤
        filters = self.get_query_filters(keyword)
        current_app.logger.info(
            "表：{}，查询关键字：{}".format(self.model.__tablename__, keyword)
        )

        # 根据 nowAlarm 参数决定获取什么数据
        nowAlarm = request.args.get("nowAlarm", "true")
        if nowAlarm == "true":
            query = base_query.filter(*filters).filter(self.model.status != 1)
        else:
            query = base_query.filter(*filters).filter(self.model.status == 1)

        # 排序
        query = query.order_by(self.model.level.asc(), self.model.last_update_at.desc())

        # 分页
        paginated_query = query.paginate(pageIndex, pageSize, False)

        # 处理结果
        data = [item.to_dict_is_time() for item in paginated_query.items]
        data = self.process_query_result(data)
        return (
            jsonify(
                {
                    "code": 200,
                    "data": data,
                    "totalCount": paginated_query.total,
                    "statusCounts": {
                        "hisAlarms": status_1_count,
                        "nowAlarms": not_status_1_count,
                    },
                }
            ),
            200,
        )


class ErrorIgnoreRules(BaseConfigView):
    model = ErrorIgnoreRulesModel

    def get_query_filters(self, keyword):
        return [
            or_(
                self.model.err_code.like("%{}%".format(keyword)),
                self.model.reason.like("%{}%".format(keyword)),
                self.model.url_pattern.like("%{}%".format(keyword)),
            ),
        ]

    def process_query_result(self, data: list):
        sorted_data = sorted(
            data,
            key=lambda x: (
                x["created_at"] if x["created_at"] is not None else datetime.min
            ),
            reverse=True,
        )
        for i in sorted_data:
            for k, v in i.items():
                if isinstance(v, datetime):
                    i[k] = v.strftime("%Y-%m-%d %H:%M:%S")
        return sorted_data

    def get_update_conditions(self, params):
        for k in ["id"]:
            if k in params:
                return {k: params[k]}
        return {}


class ErrorAlertRules(BaseConfigView):
    model = ErrorAlertRulesModel

    def get_query_filters(self, keyword):
        return [
            or_(
                self.model.rule_name.like("%{}%".format(keyword)),
                self.model.err_code.like("%{}%".format(keyword)),
                self.model.receiver.like("%{}%".format(keyword)),
            ),
        ]

    def process_query_result(self, data: list):
        data.sort(
            key=lambda x: (
                x["created_at"] if x["created_at"] is not None else datetime.min
            ),
            reverse=True,
        )
        sorted_data = sorted(data, key=lambda x: x["fire_level"], reverse=False)
        return get_display_name(sorted_data)

    def get_update_conditions(self, params):
        for key in params.keys():
            if isinstance(params[key], list):
                params[key] = ",".join(params[key])
            if params[key] == "":
                # 对于整数字段，你可能希望转换为空值或一个特定的默认值
                if key in [
                    "threshold",
                    "priority",
                    "fire_level",
                ]:  # 假设这些字段是整数类型
                    params[key] = None  # 或者其他适当的整数值，如0
                else:
                    # 对于非整数字段，根据需要处理
                    params[key] = None  # 或保留为空字符串，如果允许
        for k in ["id"]:
            if k in params:
                return {k: params[k]}
        return {}


@devmonitor_bp.route("/exclude/ignore/list", methods=["POST"])
def filter_data():
    dataraw = get_response(request)
    data = dataraw.get("data", [])
    rules = ErrorIgnoreRulesModel.query.all()
    try:
        filtered_data = []
        for item in data:
            if not matches_any_rule(item, rules):
                filtered_data.append(item)

        countError = sum([item.get("sumCount", 0) for item in filtered_data])
        allCodeError = [item.get("err_code", "") for item in filtered_data]
        return (
            jsonify(
                {
                    "data": filtered_data,
                    "termsCount": len(filtered_data),
                    "countError": countError,
                    "allCodeError": allCodeError,
                }
            ),
            200,
        )
    except Exception as e:
        return jsonify({"msg": str(e)}), 500


def matches_any_rule(data_item, rules):
    for rule in rules:
        if rule.err_code:
            if data_item["err_code"] == int(rule.err_code):
                if rule.url_pattern == "%":
                    return True
                for url_info in data_item["urls"]:
                    if match_pattern(url_info["url"], rule.url_pattern):
                        return True
    return False


def match_pattern(value, pattern):
    if pattern == "%":
        return True
    if "%" in pattern:
        pattern = pattern.replace("%", ".*")
        return re.match(pattern, value) is not None
    else:
        return value == pattern


def validate_and_set_time(data_raw, key, default):
    """
    前端时间字符串校验
    """
    time_value = data_raw.get(key)
    if time_value and isinstance(time_value, str):
        try:
            return datetime.strptime(time_value, "%Y-%m-%d %H:%M:%S")
        except ValueError:
            return default
    elif time_value and isinstance(time_value, datetime):
        return time_value
    else:
        return default


def process_time_params(data_raw, days=7) -> Tuple[datetime]:
    """
    抽象出处理 startTime 和 endTime 的逻辑
    return : -> Tuple[datetime.datetime]
    """
    start_time = validate_and_set_time(
        data_raw, "startTime", datetime.now() - timedelta(days=days)
    )
    end_time = validate_and_set_time(data_raw, "endTime", datetime.now())

    if start_time > end_time:
        start_time, end_time = end_time, start_time

    return start_time, end_time


def escape_sql(keyword):
    """
    转义 sql 关键字，防止 sql 注入
    :param keyword:
    :return:
    """
    return keyword.replace("%", "%%").replace("_", "%%_")


def build_query_filters(model, start_time, end_time, keyword=None, event_type=None):
    """
    抽象出【函数视图】构建查询条件的逻辑
    """
    time_fields = model.get_time_fields()
    base_filters = [getattr(model, field) >= start_time for field in time_fields]
    base_filters += [getattr(model, field) <= end_time for field in time_fields]
    if event_type:
        base_filters.append(model.event == event_type)

    if keyword:
        keyword_filters = or_(
            model.albumId.like(f"%{escape_sql(keyword)}%"),
            model.reqid.like(f"%{escape_sql(keyword)}%"),
            model.source_ip.like(f"%{escape_sql(keyword)}%"),
            model.details.like(f"%{escape_sql(keyword)}%"),
        )
        base_filters.append(keyword_filters)

    return base_filters


@devmonitor_bp.route("/kafka/event/notify")
def get_event_stats():
    try:
        data_raw = get_response(request)
        current_app.logger.info(f"query: {data_raw}")
        model = KafkaEventNotify
        start_time, end_time = process_time_params(data_raw)
        filters = build_query_filters(model, start_time, end_time)

        results = (
            db.session.query(model.event, func.count(model.id).label("count"))
            .filter(*filters)
            .group_by(model.event)
            .all()
        )
        # for line in results:
        #      current_app.logger.info(line)
        filterEvent = [
            {"text": result.event, "value": result.event} for result in results
        ]

        event_stats = [
            {"event": result.event, "count": result.count} for result in results
        ]
        return jsonify({"data": event_stats, "filterEvent": filterEvent}), 200
    except SQLAlchemyError as e:
        current_app.logger.error(str(e))
        return jsonify({"error": "Database query failed"}), 500
    except Exception as e:
        current_app.logger.error(str(e))
        return jsonify({"error": "An unexpected error occurred"}), 500


@devmonitor_bp.route("/kafka/event/notify/query")
def query_event_stats():
    try:
        data_raw = get_response(request)
        current_app.logger.info(f"query: {data_raw}")
        model = KafkaEventNotify
        start_time, end_time = process_time_params(data_raw)
        keyword = data_raw.get("keyword", None)
        event = data_raw.get("event_type", None)
        filters = build_query_filters(
            model, start_time, end_time, keyword=keyword, event_type=event
        )

        # 分页查询
        page = int(data_raw.get("pageIndex", 1))  # 页面
        per_page = int(data_raw.get("pageSize", 10))  # 每页的数量

        results = (
            model.query.filter(*filters)
            .order_by(model.create_time.desc())
            .limit(per_page)
            .offset((page - 1) * per_page)
            .all()
        )
        results_dict = [result.to_dict() for result in results]
        total_count = model.query.filter(*filters).count()

        # 查询优化，过滤 keyword
        # if keyword:
        #     import time
        #     current_app.logger.info("当前查询关键字: %s" % keyword)
        #     timea = time.time()
        #     res = model.query.filter(*filters).all()
        #     current_app.logger.info(f"查询耗时: {time.time()-timea}")
        #
        #     timea = time.time()
        #     all_dict = [result.to_dict() for result in res]
        #     timede = time.time()-timea
        #     current_app.logger.info(f"解密耗时: {timede}")
        #
        #     time2 = time.time()
        #     filterResult = [ line for line in all_dict
        #                      if (line.get("details") and keyword in line["details"])
        #                      or (line.get("albumId") and keyword in line["albumId"])
        #                      or (line.get("reqid") and keyword in line["reqid"])
        #                      or (line.get("source_ip") and keyword in line["source_ip"])]
        #     sorted_results = sorted(filterResult, key=lambda x: x["create_time"], reverse=True)
        #     timede = time.time()-time2
        #     current_app.logger.info(f"过滤耗时: {timede}")
        #     # 实现分页
        #     start_index = (page - 1) * per_page
        #     end_index = start_index + per_page
        #     results_dict = sorted_results[start_index:end_index]
        #     total_count = len(filterResult)
        # else:

        return make_response(
            jsonify({"data": results_dict, "totalCount": total_count}), 200
        )

    except SQLAlchemyError as e:
        current_app.logger.error(str(e))
        return make_response(
            jsonify({"error": "Database query failed", "details": str(e)}), 500
        )
    except Exception as e:
        current_app.logger.error(str(e))
        return make_response(
            jsonify({"error": "An unexpected error occurred", "details": str(e)}), 500
        )


def convert_to_unix_time(timestamp_str):
    dt = datetime.strptime(timestamp_str, "%Y-%m-%d %H:%M:%S")
    return int(dt.timestamp())


@devmonitor_bp.route("/mysql/slow/get_instance", methods=["GET"])
def slow_get_instance():
    try:
        query = AssetDatabase.query.all()
        result = [instance.to_dict() for instance in query]
        result = sorted(result, key=lambda x: x["InstanceName"], reverse=True)
        return jsonify({"data": result}), 200
    except Exception as e:
        return jsonify({"error": str(e)}), 500


@devmonitor_bp.route("/mysql/slow/grouped_logs", methods=["GET"])
def get_grouped_slow_logs():
    try:
        model = CiSqlSlowLogInfo
        data_raw = get_response(request)
        page_index = request.args.get("pageIndex", 1, type=int)
        page_size = request.args.get("pageSize", 10, type=int)

        instance_id = data_raw.get("instanceId")
        start_time, end_time = process_time_params(data_raw, days=1)

        if not all([instance_id, start_time, end_time]):
            return jsonify({"msg": "Missing required parameters"}), 404

        start_unix = int(start_time.timestamp())
        end_unix = int(end_time.timestamp())

        aggregation_functions = {
            "AvgQueryTime": func.avg(model.QueryTime).label("AvgQueryTime"),
            "MaxQueryTime": func.max(model.QueryTime).label("MaxQueryTime"),
            "SumQueryTime": func.sum(model.QueryTime).label("SumQueryTime"),
            "Count": func.count(model.id).label("Count"),
            "MaxLockTime": func.ifnull(func.max(model.LockTime), 0).label(
                "MaxLockTime"
            ),
            "AvgLockTime": func.ifnull(func.avg(model.LockTime), 0).label(
                "AvgLockTime"
            ),
            "MaxRowsExamined": func.max(model.RowsExamined)
            .cast(Integer)
            .label("MaxRowsExamined"),
            "AvgRowsExamined": func.avg(model.RowsExamined)
            .cast(Integer)
            .label("AvgRowsExamined"),
            "MaxRowsSent": func.max(model.RowsSent).cast(Integer).label("MaxRowsSent"),
            "AvgRowsSent": func.avg(model.RowsSent).cast(Integer).label("AvgRowsSent"),
        }

        query = (
            db.session.query(
                model.SqlTemplate, model.Database, *aggregation_functions.values()
            )
            .filter(
                model.InstanceId == instance_id,
                model.Timestamp.between(start_unix, end_unix),
            )
            .group_by(model.SqlTemplate, model.Database)
            .order_by(func.sum(model.QueryTime).desc())
        )

        total_records = query.count()
        results = query.limit(page_size).offset((page_index - 1) * page_size).all()

        data = [
            {
                **{
                    column.name: getattr(result, column.name)
                    for column in [
                        CiSqlSlowLogInfo.SqlTemplate,
                        CiSqlSlowLogInfo.Database,
                    ]
                },
                **{
                    label: (
                        (lambda x: 0 if round(x, 3) == 0 else round(x, 3))(
                            getattr(result, label)
                        )
                        if isinstance(getattr(result, label), float)
                        else getattr(result, label)
                    )
                    for label in aggregation_functions
                },
            }
            for result in results
        ]
        return jsonify({"data": data, "totalCount": total_records})

    except Exception as e:
        return jsonify({"msg": str(e)}), 500


@devmonitor_bp.route("/mysql/slow/advice", methods=["POST"])
def mysql_slow_advice():
    """
    获取 mysql 慢日志查询的 Advice
    :return:
    """
    try:
        model = CiSqlSlowLogInfo
        data_raw = get_response(request)

        start_time, end_time = process_time_params(data_raw, days=1)
        start_unix, end_unix = int(start_time.timestamp()), int(end_time.timestamp())

        keyword = data_raw.get("keyword")
        instance_id = data_raw.get("instanceId")
        page = int(data_raw.get("pageIndex", 1))
        per_page = int(data_raw.get("pageSize", 10))
        sql_template = data_raw.get("SqlTemplate")  # 新增参数，用于过滤 SQLTemplate

        query = model.query
        query = query.filter(
            model.Timestamp.between(start_unix, end_unix),
            model.InstanceId == instance_id,
        )

        if keyword:
            keyword_like = f"%{keyword}%"
            query = query.filter(
                db.or_(
                    model.UserHost.like(keyword_like), model.UserName.like(keyword_like)
                )
            )

        if sql_template:
            query = query.filter(model.SqlTemplate == sql_template)

        query = query.order_by(model.QueryTime.desc(), model.Timestamp.desc())
        pagination = query.paginate(page=page, per_page=per_page, error_out=False)
        logs = pagination.items
        return (
            jsonify(
                {
                    "totalCount": pagination.total,
                    "data": [log.to_dict() for log in logs],
                }
            ),
            200,
        )

    except Exception as e:
        return jsonify({"error": str(e)}), 500


@devmonitor_bp.route("/mysql/slow/statistics", methods=["POST"])
def slow_log_percentage():
    import pytz

    data_raw = get_response(request)

    # 将字符串时间转换为datetime对象
    start_time, end_time = process_time_params(data_raw, days=1)
    current_app.logger.info(
        f"mysql 慢日志分析 开始和结束时间 {start_time} -- {end_time}"
    )
    start_unix, end_unix = int(start_time.timestamp()), int(end_time.timestamp())

    instance_id = data_raw.get("instanceId")
    sql_template = data_raw.get("SqlTemplate")  # 新增参数，用于过滤 SQLTemplate
    if not all([instance_id]):
        return jsonify({"msg": "Missing required parameters"}), 404

    task_filter = [
        CiSqlSlowLogInfo.Timestamp >= start_unix,
        CiSqlSlowLogInfo.Timestamp < end_unix,
        CiSqlSlowLogInfo.InstanceId == instance_id,
    ]

    if sql_template:
        task_filter.append(CiSqlSlowLogInfo.SqlTemplate == sql_template)

    try:
        # 查询总数
        total_count_subquery = CiSqlSlowLogInfo.query.filter(*task_filter).count()

        def execute_query_time_query():
            return (
                db.session.query(
                    case(
                        [
                            (CiSqlSlowLogInfo.QueryTime > 5, ">5s"),
                            (CiSqlSlowLogInfo.QueryTime > 1, "1-5s"),
                            (True, "0-1s"),
                        ]
                    ).label("QueryTimeRange"),
                    func.count().label("QueryCount"),
                    func.concat(
                        func.round((func.count() / total_count_subquery) * 100, 2), "%"
                    ).label("PercentageOfTotal"),
                )
                .group_by("QueryTimeRange")
                .filter(*task_filter)
                .all()
            )

        def format_query_time_result(result):
            return [
                {
                    "Key": row.QueryTimeRange,
                    "Value": row.QueryCount,
                    "Percent": row.PercentageOfTotal,
                }
                for row in result
            ]

        # 调整时间函数，假设数据库时间是 UTC，需要调整到 UTC+8
        def adjust_timezone(dt, from_tz="UTC", to_tz="Asia/Shanghai"):
            from_zone = pytz.timezone(from_tz)
            to_zone = pytz.timezone(to_tz)
            utc_dt = from_zone.localize(dt)
            return utc_dt.astimezone(to_zone)

        if not sql_template:
            all_query = (
                db.session.query(
                    func.from_unixtime(
                        func.floor(CiSqlSlowLogInfo.Timestamp / 300) * 300
                    ).label("GroupedTimestamp"),
                    func.count().label("Count"),
                )
                .filter(*task_filter)
                .group_by("GroupedTimestamp")
                .all()
            )

            # all_data = [{'Timestamp': adjust_timezone(item.GroupedTimestamp).strftime("%Y-%m-%d %H:%M:%S"), 'Count': item.Count} for item in all_query]
            # 调整 all_data 的时间
            all_data = [
                {
                    "Timestamp": adjust_timezone(
                        item.GroupedTimestamp, "UTC", "Asia/Shanghai"
                    ).strftime("%Y-%m-%d %H:%M:%S"),
                    "Count": item.Count,
                }
                for item in all_query
            ]

            query_time_result = execute_query_time_query()
            analysis_QueryTime = format_query_time_result(query_time_result)
            resdata = {
                "analysis_QueryTime": analysis_QueryTime,
                "analysis_total": total_count_subquery,
                "all_data": all_data,
            }

            current_app.logger.info(f"mysql 慢日志分析 获取到的数据：{resdata}")

            return (
                jsonify(
                    {
                        "analysis_QueryTime": analysis_QueryTime,
                        "analysis_total": total_count_subquery,
                        "all_data": all_data,
                    }
                ),
                200,
            )

        # UserHost query
        user_host_query = (
            db.session.query(
                CiSqlSlowLogInfo.UserHost,
                db.func.count().label("TotalQueries"),
                db.func.concat(
                    db.func.round(
                        (db.func.count() * 100.0)
                        / (
                            db.session.query(db.func.count())
                            .select_from(CiSqlSlowLogInfo)
                            .where(*task_filter)
                        ),
                        2,
                    ),
                    "%",
                ).label("PercentageOfTotal"),
            )
            .select_from(CiSqlSlowLogInfo)
            .where(*task_filter)
            .group_by(CiSqlSlowLogInfo.UserHost)
            .order_by(db.desc("TotalQueries"))
        )
        user_host_result = user_host_query.all()
        analysis_UserHost = [
            {
                "Key": row.UserHost,
                "Value": row.TotalQueries,
                "Percent": row.PercentageOfTotal,
            }
            for row in user_host_result
        ]

        # UserName query
        user_name_query = (
            db.session.query(
                CiSqlSlowLogInfo.UserName,
                func.count().label("TotalUserName"),
                func.concat(
                    func.round((func.count() / total_count_subquery) * 100, 2), "%"
                ).label("PercentageOfTotal"),
            )
            .group_by(CiSqlSlowLogInfo.UserName)
            .filter(*task_filter)
        )
        user_name_result = user_name_query.all()
        analysis_UserName = [
            {
                "Key": row.UserName,
                "Value": row.TotalUserName,
                "Percent": row.PercentageOfTotal,
            }
            for row in user_name_result
        ]

        # QueryTime query
        query_time_result = execute_query_time_query()
        analysis_QueryTime = format_query_time_result(query_time_result)

        return (
            jsonify(
                {
                    "analysis_UserHost": analysis_UserHost,
                    "analysis_UserName": analysis_UserName,
                    "analysis_QueryTime": analysis_QueryTime,
                    "analysis_total": total_count_subquery,
                }
            ),
            200,
        )

    except Exception as e:
        return jsonify({"error": str(e)}), 500


def process_data(**kwargs):
    biz_id = kwargs.get("biz_id")
    current_start_date = kwargs.get("current_start_date")
    current_end_date = kwargs.get("current_end_date")
    days = kwargs.get("days", 3)
    window_size = kwargs.get("window_size", 3)
    threshold = kwargs.get("threshold", 0.3)

    data_coll_id = (
        BizMoniIdTitle.query.filter(BizMoniIdTitle.biz_id == biz_id).first().id
    )

    current_start_datetime = datetime.strptime(current_start_date, "%Y-%m-%d %H:%M:%S")
    current_end_datetime = datetime.strptime(current_end_date, "%Y-%m-%d %H:%M:%S")
    past_start_datetime = current_start_datetime - timedelta(days=days)
    past_end_datetime = current_start_datetime

    window_size = window_size if isinstance(window_size, int) else int(window_size)

    past_data = (
        db.session.query(BizMoniDataColl.data_time, BizMoniDataColl.value)
        .filter(
            and_(
                BizMoniDataColl.data_time >= past_start_datetime,
                BizMoniDataColl.data_time < past_end_datetime,
                BizMoniDataColl.biz_id == data_coll_id,
            )
        )
        .all()
    )

    df_past = pd.DataFrame(past_data, columns=["data_time", "value"])
    df_past["data_time"] = pd.to_datetime(df_past["data_time"])
    df_past["time"] = df_past["data_time"].dt.strftime("%H:%M")
    df_past["date"] = df_past["data_time"].dt.date
    past_days_avg = df_past.groupby("time")["value"].mean().reset_index()

    current_data = (
        db.session.query(BizMoniDataColl.data_time, BizMoniDataColl.value)
        .filter(
            and_(
                BizMoniDataColl.data_time >= current_start_datetime,
                BizMoniDataColl.data_time < current_end_datetime,
                BizMoniDataColl.biz_id == data_coll_id,
            )
        )
        .all()
    )

    df_current = pd.DataFrame(current_data, columns=["data_time", "value"])
    df_current["data_time"] = pd.to_datetime(df_current["data_time"])
    df_current["time"] = df_current["data_time"].dt.strftime("%H:%M")
    current_time = datetime.now().strftime("%H:%M")
    df_current = df_current[df_current["time"] <= current_time]
    df_current["smoothed_value"] = (
        df_current["value"].rolling(window=window_size, center=True).mean()
    )
    comparison_df = pd.merge(
        past_days_avg,
        df_current[["time", "value", "smoothed_value"]],
        on="time",
        suffixes=("_avg", "_current"),
        how="left",
    )

    # 计算偏差
    comparison_df["deviation"] = (
        abs(comparison_df["smoothed_value"] - comparison_df["value_avg"])
        / comparison_df["value_avg"]
    )

    # 标记连续 window_size 个偏差大于 threshold 的数据点
    comparison_df["high_deviation"] = comparison_df["deviation"] > threshold
    comparison_df["flag"] = (
        comparison_df["high_deviation"].rolling(window=window_size, center=True).sum()
        >= window_size
    )

    # 保留小数点后 3 位
    comparison_df["smoothed_value"] = comparison_df["smoothed_value"].apply(
        lambda x: f"{x:.3f}" if pd.notnull(x) else None
    )
    comparison_df["value_avg"] = comparison_df["value_avg"].apply(
        lambda x: f"{x:.3f}" if pd.notnull(x) else None
    )

    # 将 deviation 作为 thresholds 传递给前端
    comparison_df["deviation"] = comparison_df["deviation"].apply(
        lambda x: f"{x:.3f}" if pd.notnull(x) else None
    )

    result = {
        "time": json.loads(
            comparison_df["time"].to_json(orient="values", default_handler=str)
        ),
        "smoothed_value": json.loads(
            comparison_df["smoothed_value"].to_json(
                orient="values", default_handler=str
            )
        ),
        "value_avg": json.loads(
            comparison_df["value_avg"].to_json(orient="values", default_handler=str)
        ),
        "significant_deviation_time": json.loads(
            comparison_df[comparison_df["flag"]]["time"].to_json(
                orient="values", default_handler=str
            )
        ),
        "thresholds": json.loads(
            comparison_df[comparison_df["flag"]]["deviation"].to_json(
                orient="values", default_handler=str
            )
        ),
    }

    return {"data": result}


@devmonitor_bp.route("/biz/moni/data/query", methods=["POST"])
def biz_moni_data_query():
    data = get_response(request)
    kwargs = {
        "biz_id": data.get("biz_id"),
        "current_start_date": data.get("current_start_date"),
        "current_end_date": data.get("current_end_date"),
        "days": data.get("days", 3),  # 默认值为3天
        "window_size": data.get("window_size", 3),
        "threshold": data.get("threshold", 0.3),
    }
    result = process_data(**kwargs)
    return jsonify(result), 200


@devmonitor_bp.route("/biz/moni/data/compare", methods=["POST"])
def biz_moni_data_compare():
    data = get_response(request)
    compare = data.get("compare")
    biz_id = data.get("biz_id")

    current_start_date = datetime.strptime(
        data.get("current_start_date"), "%Y-%m-%d %H:%M:%S"
    )
    current_end_date = datetime.strptime(
        data.get("current_end_date"), "%Y-%m-%d %H:%M:%S"
    )

    # 查询 biz_id 对应的 id
    data_coll_id = (
        BizMoniIdTitle.query.filter(BizMoniIdTitle.biz_id == biz_id).first().id
    )

    current_data = BizMoniDataColl.query.filter(
        and_(
            BizMoniDataColl.biz_id == data_coll_id,
            BizMoniDataColl.data_time >= current_start_date,
            BizMoniDataColl.data_time <= current_end_date,
        )
    ).all()
    current_data = (
        [item.to_dict() for item in current_data] if len(current_data) > 0 else []
    )

    if compare in ["YoW", "MoD"]:
        if compare == "YoW":  # 同比
            compare_start_date = current_start_date - timedelta(weeks=1)
            compare_end_date = current_end_date - timedelta(weeks=1)
        elif compare == "MoD":  # 环比
            compare_start_date = current_start_date - timedelta(days=1)
            compare_end_date = current_end_date - timedelta(days=1)
        compare_data = BizMoniDataColl.query.filter(
            and_(
                BizMoniDataColl.biz_id == data_coll_id,
                BizMoniDataColl.data_time >= compare_start_date,
                BizMoniDataColl.data_time <= compare_end_date,
            )
        ).all()
        compare_data = (
            [item.to_dict() for item in compare_data] if len(compare_data) > 0 else []
        )
    else:
        compare_data = []

    compare_value = [item.get("value") for item in compare_data]
    current_value = [item.get("value") for item in current_data]
    current_time = [item.get("data_time") for item in current_data]
    compare_time = [item.get("data_time") for item in compare_data]

    return (
        jsonify(
            {
                # "current_data": current_data,
                # "compare_data": compare_data,
                "compare_value": compare_value,
                "current_value": current_value,
                "current_time": current_time,
                "compare_time": compare_time,
            }
        ),
        200,
    )


@devmonitor_bp.route("/biz/moni/data/bizids")
def get_biz_ids():
    model = BizMoniIdTitle
    query = db.session.query(model.biz_id, model.biz_title).distinct().all()
    return (
        jsonify(
            {
                "data": [
                    {"value": item.biz_id, "label": item.biz_title} for item in query
                ]
            }
        ),
        200,
    )


devmonitor_bp.add_url_rule(
    "/kafka/alert/fire", view_func=KafkaAlertFire.as_view("kafka_alert_fire")
)
devmonitor_bp.add_url_rule(
    "/kafka/ignore/rules", view_func=ErrorIgnoreRules.as_view("kafka_ignore_rules")
)
devmonitor_bp.add_url_rule(
    "/kafka/alert/rules", view_func=ErrorAlertRules.as_view("kafka_alert_rules")
)
