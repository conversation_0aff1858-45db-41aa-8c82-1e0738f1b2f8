#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
@Title:     presm.py
@Date:      2024/7/31 14:41
@Author:    wanglh
@Desc:      预发布环境管理
"""

from flask import Blueprint, abort, request, url_for
from flask.views import MethodView
from sqlalchemy import insert, text, update
from sqlalchemy.exc import SQLAlchemyError, TimeoutError
from tenacity import (retry, retry_if_exception_type,
                      stop_after_attempt, wait_exponential)

from utils import *

presm_bp = Blueprint("ci_presm", __name__, url_prefix="/ci/api/v1/presm")
devops_users = ["wanglinhao", "dongxin", "lijiana", "wuxuan"]

HTTP_NOT_FOUND = 404
HTTP_SERVER_ERROR = 500
HTTP_SUCCESS = 200


def parse_time(time_str):
    if not time_str:
        return datetime.min
    return datetime.strptime(time_str, "%Y-%m-%d %H:%M:%S")


class LogOperationError(Exception):
    pass


class PreBaseApi(MethodView):
    def __init__(self):
        super().__init__()
        self.applicant = request.headers.get("X-User", "unknown")


class DbOperationUtil:
    @staticmethod
    def save_to_db(model_instance):
        db.session.add(model_instance)

        # 打印具体的 SQL 语句
        # session = db.object_session(model_instance)
        # statement = orm.Query(model_instance.__class__).filter_by(id=model_instance.id).statement
        # current_app.logger.info(str(statement.compile(compile_kwargs={"literal_binds": True})))

        try:
            db.session.commit()
        except SQLAlchemyError as e:
            db.session.rollback()
            abort(HTTP_SERVER_ERROR, description=f"Database error: {e}")

    @staticmethod
    def get_or_abort(model, query_filter, status_code):
        """从数据库获取记录，或者使用给定的HTTP状态代码中止操作."""
        record = model.query.filter_by(**query_filter).first()
        if not record:
            abort(status_code)
        return record

    @staticmethod
    def update_to_sql(model, update_data, condition):
        """根据指定的条件更新数据库中的记录.

        :param model: 要更新的模型类
        :param update_data: 包含要更新的字段及其新值的字典
        :param condition: 用于过滤更新的记录的条件字典
        """
        stmt = (
            update(model)
            .where(*[getattr(model, key) == value for key, value in condition.items()])
            .values(update_data)
        )

        # current_app.logger.info(str(stmt.compile(compile_kwargs={"literal_binds": True})))

        try:
            db.session.execute(stmt)
            db.session.commit()
        except SQLAlchemyError as e:
            db.session.rollback()
            abort(HTTP_SERVER_ERROR, description=f"Database error: {e}")

    @staticmethod
    def delete_from_db(query):
        try:
            query.delete()
            db.session.commit()
        except SQLAlchemyError as e:
            db.session.rollback()
            abort(HTTP_SERVER_ERROR, description=f"Database error: {e}")
        finally:
            db.session.close()


class LogOperationUtil:
    @staticmethod
    def log_operation(
            task_type: int,
            pre_uuid: str,
            step_name: str,
            step_log: str,
            step_result: int,
            applicant: str,
            module=CiPreOpLogs,
    ):
        operation = module(
            task_type=task_type,
            pre_uuid=pre_uuid,
            step_name=step_name,
            step_log=step_log,
            step_result=step_result,
            applicant=applicant,
        )
        try:
            db.session.add(operation)
            db.session.commit()
        except SQLAlchemyError as e:
            db.session.rollback()
            current_app.logger.error(f"Failed to log operation: {e}")
            raise LogOperationError(e) from e
        finally:
            db.session.close()

    @staticmethod
    def response_log_error(response: dict, e: str):
        response.setdefault("log_status", "Failed")
        response.setdefault("log_error", e)
        return response


class EnvironmentAPI(PreBaseApi):
    def __init__(self):
        super().__init__()
        self.model = CiPreEnvInfo

    def get(self, pre_uuid=None):
        """获取环境信息"""
        data = get_response(request)
        try:
            page_no = int(data.get("pageNo", 1))
            page_size = int(data.get("pageSize", 10))
            status = int(data.get("status", 2))
        except ValueError:
            return jsonify({"msg": "Invalid pageNo, pageSize or status value"}), 400
        keyword = data.get("keyword", "")
        query = self.model.query
        try:
            if pre_uuid:
                environments = query.filter_by(pre_uuid=pre_uuid).all()
                totalCount = query.filter_by(pre_uuid=pre_uuid).count()
            else:
                task_filter = {
                    and_(
                        self.model.status == status,
                        or_(
                            self.model.pre_uuid.like(f"%{keyword}%"),
                            self.model.pre_alias.like(f"%{keyword}%"),
                            self.model.version_code.like(f"%{keyword}%"),
                            self.model.bizline.like(f"%{keyword}%"),
                        ),
                    ),
                }
                filtered_query = query.filter(*task_filter)
                totalCount = filtered_query.count()
                environments = (
                    filtered_query.order_by(self.model.create_time.desc())
                    .limit(page_size)
                    .offset((page_no - 1) * page_size)
                    .all()
                )

            if environments:
                return (
                    jsonify(
                        {
                            "data": [env.to_dict_is_time() for env in environments],
                            "totalCount": totalCount,
                        }
                    ),
                    HTTP_SUCCESS,
                )
            else:
                return jsonify({"msg": "No environmental information"}), HTTP_SUCCESS
        except LogOperationError as e:
            return LogOperationUtil.response_log_error(e)

    def post(self):
        """新增环境, 调用蓝鲸作业 1000240"""
        data = get_response(request)
        pre_uuid = get_short_id(beginStr="p")
        new_data = {"applicant": self.applicant, "pre_uuid": pre_uuid, **data}
        current_app.logger.info(f"create env: {new_data}")
        current_app.logger.info(f"前端post-data：{data}")

        try:
            new_env = self.model(**new_data)
            DbOperationUtil.save_to_db(new_env)
            params = {"namespace": pre_uuid, "ops": "create"}
            resDict = execute_job_1000240(**params)
            if resDict.get("result") is True:
                msg = "新增环境创建成功"
                stepResult = 0
            else:
                msg = "新增环境创建失败"
                stepResult = 1
            # 创建一个空规则
            addgray_url = url_for("ci_presm.grayrule_api", _external=True)
            current_app.logger.info(f"add gray rule: {addgray_url}")
            data = {"pre_uuid": pre_uuid}
            res = requests.post(url=addgray_url, data=data)
            response = {}
            if res.raise_for_status():
                current_app.logger.info(f"新增环境: {pre_uuid}，空规则创建成功")
            response = {"msg": msg, "bluemsg": resDict}
            LogOperationUtil.log_operation(
                0,
                pre_uuid,
                "新增环境",
                f"环境ID：{pre_uuid}，具体信息：{new_data}, 蓝鲸调用信息：{resDict}",
                stepResult,
                self.applicant,
            )
            response["log_status"] = "Success"
        except LogOperationError as e:
            LogOperationUtil.response_log_error(response, e)
        except Exception as e:
            current_app.logger.error(f"Error: {e}")
        return jsonify(response), HTTP_SUCCESS

    def delete(self, pre_uuid):
        """
        释放环境，将状态变更为 4
        """
        env = DbOperationUtil.get_or_abort(
            self.model, {"pre_uuid": pre_uuid}, HTTP_NOT_FOUND
        )
        original_data = env.to_dict_is_time()
        env.status = 4
        try:
            DbOperationUtil.save_to_db(env)
            params = {"namespace": pre_uuid, "ops": "delete"}
            resDict = execute_job_1000240(**params)
            if resDict.get("result") is True:
                msg = "环境释放成功"
                stepResult = 0
                # 新增逻辑：使用 DbOperationUtil.delete_from_db 删除此 pre_uuid 环境的 gray_rule 规则
                query = CiPreGrayRule.query.filter_by(pre_uuid=pre_uuid)
                DbOperationUtil.delete_from_db(query)
            else:
                msg = "环境释放失败"
                stepResult = 1
            response = {"msg": msg, "bluemsg": resDict}
            LogOperationUtil.log_operation(
                4,
                pre_uuid,
                "释放环境",
                f"环境ID：{pre_uuid}, 释放前：{original_data}, 蓝鲸调用信息: {resDict}",
                stepResult,
                self.applicant,
            )
            response["log_status"] = "Success"
            return jsonify(response), HTTP_SUCCESS

        except LogOperationError as e:
            LogOperationUtil.response_log_error(response, e)
            return jsonify(response), 502

    def put(self, pre_uuid):
        """
        更新和封存环境
        """
        env = DbOperationUtil.get_or_abort(
            self.model, {"pre_uuid": pre_uuid}, HTTP_NOT_FOUND
        )

        update_data = request.get_json()

        current_app.logger.info(f"update env: {update_data}")
        original_data = env.to_dict_is_time()

        operation = request.args.get("operation", "").lower()
        task_type = 0

        if operation == "update" or operation == "extension":
            current_app.logger.info(f"运行「{operation}」")
            current_app.logger.info(f"当前修改人：{update_data.get('applicant')}")
            current_app.logger.info(f"self.applicant：{self.applicant}")

            for key, value in update_data.items():
                if hasattr(env, key):
                    setattr(env, key, value)
            task_type = 5
            env.status = env.status if operation == "update" else 2
            str_name = "更新" if operation == "update" else "延期"
            step_name = f"{str_name}环境"
            step_log = f"{str_name}前：{original_data}，{str_name}后：{env.to_dict()}"
        elif operation == "archive":
            env.status = 3
            task_type = 3
            step_name = "封存环境"
            step_log = f"环境ID：{pre_uuid}，封存前信息：{original_data}"
        else:
            abort(400, description="Invalid operation parameter")

        try:
            DbOperationUtil.save_to_db(env)
            response = {"msg": f"{step_name} 成功"}
            LogOperationUtil.log_operation(
                task_type, pre_uuid, step_name, step_log, 0, self.applicant
            )
            response["log_status"] = "Success"
        except LogOperationError as e:
            LogOperationUtil.response_log_error(response, e)
        return jsonify(response), HTTP_SUCCESS


class ServiceAPI(PreBaseApi):

    def __init__(self):
        super().__init__()
        response = current_app.test_client().get(
            url_for("ci_sandbox.mysql_project", _external=True)
        )
        if response.status_code == HTTP_SUCCESS:
            all_project = response.get_json().get("data")
            self.project_id_name = {
                line["project_id"]: line["name"] for line in all_project
            }

    def get(self, pre_uuid):
        """
        获取服务列表
        :param pre_uuid: 环境 ID
        """

        services = CiPreServiceMap.query.filter_by(pre_uuid=pre_uuid).all()

        if not services:
            return (
                jsonify({"code": HTTP_NOT_FOUND, "msg": "此环境没有服务"}),
                HTTP_SUCCESS,
            )

        res_services = []
        service_dicts = [service.to_dict_is_time() for service in services]
        response = current_app.test_client().get(
            url_for("ci_sandbox.mysql_project", _external=True)
        )
        if response.status_code == HTTP_SUCCESS:
            all_project = response.get_json().get("data")
            project_dict = {project["project_id"]: project for project in all_project}
            for service in service_dicts:
                project_id = service.get("project_id")
                if project_id:
                    project_info = project_dict.get(project_id)
                    if project_info:
                        combined_service = {**service, **project_info}
                        res_services.append(combined_service)
        else:
            return (
                jsonify({"code": HTTP_NOT_FOUND, "msg": "没有找到服务信息"}),
                HTTP_SUCCESS,
            )
        res_services.sort(key=lambda x: x.get("publish_count"), reverse=True)
        return jsonify({"data": res_services}), HTTP_SUCCESS

    def post(self, pre_uuid):
        """
        添加服务
        :param pre_uuid: 环境 ID
        """
        data = request.get_json()
        current_app.logger.info(f"create service: {data}")
        service = CiPreServiceMap(**data)
        project_name = self.project_id_name.get(int(data.get("project_id")))
        service.pre_uuid = pre_uuid
        try:
            DbOperationUtil.save_to_db(service)
            response = {"msg": "服务添加成功"}
            current_app.logger.info(
                f"service.to_dict_is_time：{service.to_dict_is_time()}"
            )
            LogOperationUtil.log_operation(
                0,
                pre_uuid,
                f"新增服务: {project_name}",
                f"环境ID：{pre_uuid}, 具体信息：{service.to_dict_is_time()}",
                0,
                self.applicant,
                CiPreServiceOpLogs,
            )
            response["log_status"] = "Success"
        except LogOperationError as e:
            LogOperationUtil.response_log_error(response, e)

        return jsonify(response), HTTP_SUCCESS

    def delete(self, pre_uuid, project_id):
        """
        删除服务
        :param pre_uuid: 环境 ID
        :param project_id: 服务 ID
        """
        project_name = self.project_id_name.get(int(project_id))
        current_app.logger.info(
            f"删除服务：pre_uuid: {pre_uuid}, project_id: {project_id}, project_name: {project_name}"
        )
        service = CiPreServiceMap.query.filter_by(
            pre_uuid=pre_uuid, project_id=project_id
        ).first()
        response = {}

        if service:
            try:
                db.session.delete(service)
                db.session.commit()
                service_after_deletion = CiPreServiceMap.query.filter_by(
                    pre_uuid=pre_uuid, project_id=project_id
                ).first()
                if service_after_deletion:
                    response, step_result = {
                        "msg": f"服务: {project_name}, 删除失败"
                    }, 1
                else:
                    response, step_result = {
                        "msg": f"服务: {project_name}, 删除成功"
                    }, 0
                LogOperationUtil.log_operation(
                    3,
                    pre_uuid,
                    f"删除服务：{project_name}",
                    f"环境ID：{pre_uuid}, 删除服务ID：{project_id}",
                    step_result,
                    self.applicant,
                    CiPreServiceOpLogs,
                )
                response["log_status"] = "Success"
            except LogOperationError as e:
                LogOperationUtil.response_log_error(response, e)
            except SQLAlchemyError as e:
                db.session.rollback()
                current_app.logger.error(f"Database Error: {e}")
                abort(HTTP_SERVER_ERROR, description=f"Database Error: {e}")
            finally:
                db.session.close()
        else:
            return (
                jsonify({"code": HTTP_NOT_FOUND, "msg": "此环境不存在此服务"}),
                HTTP_NOT_FOUND,
            )
        return jsonify(response), HTTP_SUCCESS

    def put(self, pre_uuid):
        """
        服务发布
        :param project_id:
        :param name:
        :param pre_uuid:  环境 ID
        """
        passUser = ["unknown", "undefined", ""]
        if self.applicant in passUser:
            return jsonify({"code": 400, "msg": "请登录，再操作"}), 400

        data_raw = request.get_json()

        current_app.logger.info(f"data_raw: {data_raw}")

        project_id_raw = data_raw.get("project_id", "")

        try:
            project_ids = []
            for project_id in project_id_raw.split(","):
                try:
                    project_ids.append(int(project_id))
                except ValueError:
                    continue
        except ValueError:
            current_app.logger.error(
                "Error converting project IDs to integers.")
            return jsonify({"error": "Invalid project IDs."}), 400

        names = data_raw.get("name").split(",")
        appnames = data_raw.get("appname").split(",")

        project_names = dict(zip(project_ids, names))
        project_appnames = dict(zip(project_ids, appnames))

        latest_sequence_id = get_stamp_id()
        latest_build_time = datetime.now()

        services = (
            CiPreServiceMap.query.filter_by(pre_uuid=pre_uuid)
            .filter(CiPreServiceMap.project_id.in_(project_ids))
            .all()
        )
        if not services:
            return abort(HTTP_NOT_FOUND)

        branchSet = set([service.project_branch for service in services])
        if len(branchSet) > 1:
            return jsonify({"msg": "请选择同一分支的服务"}), 400

        branch = branchSet.pop()

        project_query = CiProjects.query.filter(
            CiProjects.id.in_(project_ids)).all()
        params_services = ",".join([p.project for p in project_query])

        bk_result = []
        build_info = [
            dict(service.to_dict_is_time(),
                 name=project_names.get(service.project_id))
            for service in services
        ]

        services_names = [
            project_appnames.get(service.project_id) for service in services
        ]
        try:
            with db.session.begin_nested():
                for service in services:
                    service.publish_count += 1
                    service.latest_sequence_id = latest_sequence_id
                    service.latest_build_time = latest_build_time
                    service.status = 2
                    current_app.logger.info(
                        f"service.project_id：{service.project_id}")
                    db.session.add(service)
            db.session.commit()
            params = {
                "services": params_services,
                "branch": branch,
                "deploy_env": pre_uuid,
                "sequence": latest_sequence_id,
                "username": self.applicant if self.applicant else "",
            }
            bk = execute_job_1000237(**params)
            bk_result.append({"bk_result": bk.get("result"), "name": names})

            LogOperationUtil.log_operation(
                1,
                pre_uuid,
                f"构建服务：{','.join(services_names)}",
                f"环境ID：{pre_uuid}, 具体信息：{build_info}",
                3,
                self.applicant,
                CiPreServiceOpLogs,
            )

            response = {
                "code": HTTP_SUCCESS,
                "bk_result": bk_result,
                "msg": f"{pre_uuid} 构建信息更新成功",
                "log_status": "Success",
            }
            current_app.logger.info(response)

            return jsonify(response), HTTP_SUCCESS

        except LogOperationError as e:
            LogOperationUtil.response_log_error(response, e)

        except SQLAlchemyError as e:
            db.session.rollback()
            abort(HTTP_SERVER_ERROR, description=f"Database error: {e}")
        except Exception as e:
            db.session.rollback()
            abort(
                HTTP_SERVER_ERROR,
                description=f"Error occurred while building service: {e}",
            )
        finally:
            db.session.close()


class EnvServiceLogAPI(PreBaseApi):
    def get(self, pre_uuid=None):
        basemodel = CiPreEnvInfo
        current_app.logger.info(f"pre_uuid: {pre_uuid}")
        model = CiPreServiceOpLogs if pre_uuid else CiPreOpLogs

        # 构建查询
        query = db.session.query(model, basemodel.pre_alias).join(
            basemodel, basemodel.pre_uuid == model.pre_uuid
        )

        if pre_uuid:
            query = query.filter(model.pre_uuid == pre_uuid)

        environments = query.order_by(model.create_time.desc()).all()
        result = []
        for env, pre_alias in environments:
            env_dict = env.to_dict_is_time()
            env_dict["pre_alias"] = pre_alias
            result.append(env_dict)
        for r in result:
            r["color"] = model.COLOR_DICT.get(r["step_result"])
            r["step_result"] = model.STEP_RESULT_DICT.get(r["step_result"])
        if environments:
            db.session.close()
            return jsonify({"data": result}), HTTP_SUCCESS
        else:
            db.session.close()
            return jsonify({"msg": "日志信息为空"}), HTTP_SUCCESS


class EnvGrayRuleAPI(PreBaseApi):
    def __init__(self):
        super().__init__()
        self.model = CiPreGrayRule

    @retry(
        stop=stop_after_attempt(3),
        wait=wait_exponential(multiplier=1, min=4, max=10),
        retry=retry_if_exception_type(TimeoutError),  # 仅在超时错误时重试
    )
    def get(self, pre_uuid=None):
        """获取环境规则"""
        try:
            data = get_response(request)
            page_no = int(data.get("pageNo", 1))
            page_size = int(data.get("pageSize", 15))
            query = self.model.query

            # 获取排序字段和排序方式
            sort_field = data.get("sortField", "pre_uuid")  # 默认按 pre_uuid 排序
            sort_order = data.get("sortOrder", "desc")  # 默认降序排序

            env_task = {or_(CiPreEnvInfo.status == 1,
                            CiPreEnvInfo.status == 2)}
            env_status = CiPreEnvInfo.query.filter(*env_task).all()
            envStatusMap = {env.pre_uuid: env.status for env in env_status}

            current_app.logger.info(tuple(envStatusMap.keys()))

            if pre_uuid:
                query = query.filter(self.model.pre_uuid == pre_uuid)

                if query.count() > 0:
                    data = [env.to_dict_is_time() for env in query]
                    return jsonify({"status": "success", "data": data}), HTTP_SUCCESS
                else:
                    return jsonify({"status": "failed", "data": None}), HTTP_SUCCESS

            task_filter = {
                and_(
                    self.model.pre_uuid.in_(tuple(envStatusMap.keys())),
                    or_(
                        self.model.pre_uuid.like(
                            f"%{data.get('keyword', '')}%"),
                        self.model.albumid.like(
                            f"%{data.get('keyword', '')}%"),
                    ),
                )
            }
            totalCount = query.filter(*task_filter).count()

            # 根据排序字段和排序方式进行排序
            if sort_order.lower() == "asc":
                environments = (
                    query.filter(*task_filter)
                    .order_by(getattr(self.model, sort_field).asc())
                    .limit(page_size)
                    .offset((page_no - 1) * page_size)
                    .all()
                )
            else:
                environments = (
                    query.filter(*task_filter)
                    .order_by(getattr(self.model, sort_field).desc())
                    .limit(page_size)
                    .offset((page_no - 1) * page_size)
                    .all()
                )

            if environments:
                current_app.logger.info(f"envStatusMap: {envStatusMap}")
                sorted_environments = [env.to_dict_is_time()
                                       for env in environments]

                return (
                    jsonify({"data": sorted_environments,
                             "totalCount": totalCount}),
                    HTTP_SUCCESS,
                )
            else:
                return jsonify({"msg": "没有正在运行中的环境规则"}), HTTP_SUCCESS
        finally:
            db.session.close()

    def post(self):
        """添加环境规则"""
        data = get_response(request)
        pre_uuid = data.get("pre_uuid")
        albumid = data.get("albumid", "")
        if pre_uuid == "":
            return (
                jsonify({"code": HTTP_NOT_FOUND, "msg": "环境ID不能为空"}),
                HTTP_NOT_FOUND,
            )

        # 检查 albumid 是否存在
        if albumid:
            current_rule = self.model.query.filter_by(albumid=albumid).first()
            if current_rule:
                base_url = url_for('ci_presm.grayrule_api', _external=True)
                put_url = f"{base_url}/{albumid}"
                headers = {'Content-Type': 'application/json'}
                response = requests.put(put_url, json=data, headers=headers)
                return response.text, response.status_code, {'Content-Type': 'application/json'}

        new_data = {"applicant": self.applicant, **data}
        current_app.logger.info(f"create env grayrule: {new_data}")

        try:
            new_env = self.model(**new_data)
            DbOperationUtil.save_to_db(new_env)
            response = {"msg": "新增环境规则成功"}
            LogOperationUtil.log_operation(
                5,
                pre_uuid,
                "新增环境规则",
                f"环境ID：{pre_uuid}，添加相册 ID：{albumid}",
                0,
                self.applicant,
            )
            response["log_status"] = "Success"
        except LogOperationError as e:
            LogOperationUtil.response_log_error(response, e)
        return jsonify(response), HTTP_SUCCESS

    def put(self, albumid=None):
        """
        更新环境规则信息，根据传递的 params，通过 albumid 进行匹配并修改信息
        使用 upsert 操作实现更新或插入
        """
        data = request.get_json()
        pre_uuid = data.get("pre_uuid")
        data["update_user"] = self.applicant
        current_app.logger.info(f"update env grayrule: {data}")

        if albumid:
            try:
                current_rule = self.model.query.filter_by(
                    albumid=albumid).first()

                stmt = insert(self.model).values(**data)
                stmt = stmt.on_duplicate_key_update(**data)
                db.session.execute(stmt)
                db.session.commit()

                operation_type = "新增" if not current_rule else "更新"
                log_message = (
                    f"相册ID：{albumid}，{operation_type}环境规则到{pre_uuid}"
                    if not current_rule
                    else f"相册ID：{albumid}，从环境{current_rule.pre_uuid}变更到{pre_uuid}"
                )

                LogOperationUtil.log_operation(
                    5,
                    pre_uuid,
                    f"{operation_type}环境规则",
                    log_message,
                    0,
                    self.applicant,
                )

                response = {
                    "msg": f"{albumid}所属环境{operation_type}成功",
                    "log_status": "Success",
                }

                # 删除空规则
                try:
                    empty_rules_query = self.model.query.filter(
                        or_(self.model.albumid == "",
                            self.model.albumid.is_(None))
                    )

                    count = empty_rules_query.count()

                    if count > 0:
                        DbOperationUtil.delete_from_db(empty_rules_query)
                        current_app.logger.info(f"成功删除 {count} 条空规则记录")
                    else:
                        current_app.logger.info("没有找到需要删除的空规则记录")

                except Exception as e:
                    current_app.logger.error(f"删除空规则失败: {e}")

            except LogOperationError as e:
                response = {"msg": "更新环境规则失败，日志操作失败"}
                LogOperationUtil.response_log_error(response, str(e))
                # return jsonify(response), 500

            except SQLAlchemyError as e:
                db.session.rollback()
                response = {"msg": "更新环境规则失败，数据库操作失败"}
                LogOperationUtil.response_log_error(response, str(e))
                return jsonify({"error": "数据库操作失败", "details": str(e)}), 500

            finally:
                db.session.close()

            return jsonify(response), HTTP_SUCCESS

    def delete(self, albumid=None):
        if albumid:
            query = self.model.query.filter(self.model.albumid == albumid)
            if query.count() > 0:
                try:
                    album_instance = query.first()
                    DbOperationUtil.delete_from_db(query)
                    response = {"msg": "删除环境规则成功"}
                    LogOperationUtil.log_operation(
                        5,
                        album_instance.pre_uuid,
                        "删除环境规则",
                        f"相册ID：{albumid}，从环境：{album_instance.pre_uuid} 删除",
                        0,
                        self.applicant,
                    )
                    response["log_status"] = "Success"
                except LogOperationError as e:
                    response = {"msg": "删除环境规则失败，日志操作失败"}
                    LogOperationUtil.response_log_error(response, str(e))
                    db.session.rollback()
                finally:
                    db.session.close()
                return jsonify(response), HTTP_SUCCESS
            else:
                return jsonify({"msg": "没有该环境规则"}), HTTP_NOT_FOUND

    def delete_None_preUUid(self, pre_uuid):
        """此方法可以删除，因为已经在 put 方法中实现了删除空规则的逻辑"""
        pass


class EnvGrayRuleUriAPI(PreBaseApi):
    def __init__(self):
        super().__init__()
        self.model = CiPreGrayRuleUri

    def get(self, uri_id=None):
        """
        GET:
        - 若提供uri_id，则返回对应的一条规则；
        - 若无uri_id，则支持分页查询，支持根据关键字查询（匹配 uri 或 pre_uuid）。
        """
        data = get_response(request)
        page_no = int(data.get("pageNo", 1))
        page_size = int(data.get("pageSize", 15))
        query = self.model.query

        if uri_id:
            query = query.filter(self.model.id == uri_id)
            if query.count() > 0:
                res_data = [env.to_dict_is_time() for env in query]
                return jsonify({"status": "success", "data": res_data}), HTTP_SUCCESS
            else:
                return jsonify({"status": "failed", "data": None}), HTTP_SUCCESS
        else:
            task_filter = {
                or_(
                    self.model.uri.like(f"%{data.get('keyword', '')}%"),
                    self.model.pre_uuid.like(f"%{data.get('keyword', '')}%"),
                )
            }
            totalCount = query.filter(*task_filter).count()
            environments = (
                query.filter(*task_filter)
                .order_by(self.model.uri.desc(), self.model.create_time.desc())
                .limit(page_size)
                .offset((page_no - 1) * page_size)
                .all()
            )

            if environments:
                sorted_environments = sorted(
                    [env.to_dict_is_time() for env in environments],
                    key=lambda x: x.get("uri") is None,
                    reverse=False,
                )
                return (
                    jsonify({"data": sorted_environments,
                             "totalCount": totalCount}),
                    HTTP_SUCCESS,
                )
            else:
                return jsonify({"msg": "没有找到环境规则URI"}), HTTP_SUCCESS

    def put(self, uri_id=None):
        """
        PUT:
        - 若不提供 uri_id，则按新增处理；
        - 若提供 uri_id，则按更新处理。
        """
        data = request.get_json()
        data["update_user"] = self.applicant
        current_app.logger.info(
            f"PUT env grayrule uri data: {data} with uri_id: {uri_id}"
        )
        try:
            if uri_id is None:
                # 没有uri_id, 则为新增操作
                new_entry = self.model(**data)
                DbOperationUtil.save_to_db(new_entry)
                response = {"msg": "新增环境规则URI成功"}
                LogOperationUtil.log_operation(
                    5,
                    data.get("pre_uuid"),
                    "新增环境规则URI",
                    f"URI：{data.get('uri')}",
                    0,
                    self.applicant,
                )
                response["log_status"] = "Success"
            else:
                # 有uri_id, 则进行更新操作
                current_entry = self.model.query.filter_by(id=uri_id).first()
                if current_entry:
                    for key, value in data.items():
                        if hasattr(current_entry, key):
                            setattr(current_entry, key, value)
                    DbOperationUtil.save_to_db(current_entry)
                    response = {"msg": f"URI ID {uri_id} 更新成功"}
                    LogOperationUtil.log_operation(
                        5,
                        data.get("pre_uuid"),
                        "更新环境规则URI",
                        f"URI ID：{uri_id} 从环境 {current_entry.pre_uuid} 更新到 {data.get('pre_uuid')}",
                        0,
                        self.applicant,
                    )
                    response["log_status"] = "Success"
                else:
                    response = {"msg": "没有找到该环境规则URI"}
                    return jsonify(response), HTTP_NOT_FOUND
        except LogOperationError as e:
            response = {"msg": "环境规则URI操作失败，日志记录错误"}
            LogOperationUtil.response_log_error(response, str(e))
        except SQLAlchemyError as e:
            db.session.rollback()
            response = {"msg": "环境规则URI操作失败，数据库错误"}
            LogOperationUtil.response_log_error(response, str(e))
            return (
                jsonify({"error": "数据库错误", "details": str(e)}),
                HTTP_SERVER_ERROR,
            )
        return jsonify(response), HTTP_SUCCESS

    def delete(self, uri_id=None):
        """
        DELETE:
        - 删除操作要求必须传入 uri_id，否则返回 404 提示。
        """
        if uri_id:
            query = self.model.query.filter(self.model.id == uri_id)
            if query.count() > 0:
                try:
                    entry = query.first()
                    LogOperationUtil.log_operation(
                        5,
                        entry.pre_uuid,
                        "删除环境规则URI",
                        f"URI ID：{uri_id} 从环境 {entry.pre_uuid} 被删除",
                        0,
                        self.applicant,
                    )
                    DbOperationUtil.delete_from_db(query)
                    response = {"msg": "删除环境规则URI成功", "log_status": "Success"}
                except LogOperationError as e:
                    response = {"msg": "删除环境规则URI失败，日志记录错误"}
                    LogOperationUtil.response_log_error(response, str(e))
                    db.session.rollback()
                return jsonify(response), HTTP_SUCCESS
            else:
                return jsonify({"msg": "没有找到该环境规则URI"}), HTTP_NOT_FOUND
        else:
            return jsonify({"msg": "uri_id必须提供用于删除操作"}), HTTP_NOT_FOUND


@presm_bp.route("/envs/grayrule/albumid/<albumid>")
def get_albumid_info(albumid):
    if albumid:
        url = f"http://acs.prod.cluster.szwego.com/rpc/getAlbumInfoByAlbumIdList2?targetAlbumIds={albumid}"
        r = requests.get(url)
        if r.status_code == 200:
            return jsonify(r.json()), 200
    return jsonify({"msg": "albumid参数为空"}), 200


@presm_bp.route("/envs/traffic/<pre_uuid>")
def get_traffic_query(pre_uuid):
    if pre_uuid:
        from datetime import datetime, timedelta

        now = datetime.now()
        start_time = (now - timedelta(minutes=5)).strftime("%Y-%m-%d %H:%M:00")
        end_time = now.strftime("%Y-%m-%d %H:%M:00")
        query = (
            db.session.query(
                func.date_format(CiPreTraffic.record_time, "%Y-%m-%d %H:%i").label(
                    "time_group"
                ),
                CiPreTraffic.type.label("type"),
                func.sum(CiPreTraffic.count).label(
                    "sum_of_count"
                ),  # 假设你需要对某个字段做聚合统计
            )
            .filter_by(pre_uuid=pre_uuid)
            .filter(CiPreTraffic.record_time >= start_time)
            .group_by(text("FLOOR(UNIX_TIMESTAMP(record_time)/300)"), CiPreTraffic.type)
            .order_by(
                func.date_format(CiPreTraffic.record_time,
                                 "%Y-%m-%d %H:%i").desc()
            )
            .all()
        )

        time_group = query[0].time_group if query else start_time
        data = []
        for type_, type_name in CiPreTraffic.TASK_TYPE_DICT.items():
            row = next((item for item in query if item.type == type_), None)
            sum_of_count = int(row.sum_of_count) if row else 0
            data.append(
                {
                    "time_group": time_group,
                    "type": type_name,
                    "sum_of_count": sum_of_count,
                }
            )

        return jsonify({"data": data, "time_range": f"{start_time} - {end_time}"}), 200
    return jsonify({"msg": "pre_uuid参数为空"}), 200


@presm_bp.route("/envs/buildinfo/<pre_uuid>")
def getJenkinsBuildInfo(pre_uuid):
    if pre_uuid:
        data_raw = get_response(request)
        current_app.logger.info(f"data_raw: {data_raw}")
        query = CiPreBuildInfo.query.filter_by(
            pre_uuid=pre_uuid,
            sequence_id=data_raw.get("latest_sequence_id"),
            project=data_raw.get("project"),
        ).first()
        current_app.logger.info(query.to_dict_is_time() if query else {})
        return jsonify({"data": query.to_dict_is_time() if query else {}}), 200
    return jsonify({"msg": "pre_uuid url 参数为空"}), 404


env_api = EnvironmentAPI.as_view("presm_env_api")
presm_bp.add_url_rule("/envs", view_func=env_api, methods=["GET", "POST"])
presm_bp.add_url_rule("/envs/<pre_uuid>", view_func=env_api,
                      methods=["GET", "DELETE"])
presm_bp.add_url_rule("/envs/<pre_uuid>/actions",
                      view_func=env_api, methods=["PUT"])

service_api = ServiceAPI.as_view("service_api")
BaseServiceUrl = "/envs/<pre_uuid>/services"
presm_bp.add_url_rule(
    BaseServiceUrl, view_func=service_api, methods=["GET", "POST"])
presm_bp.add_url_rule(
    f"{BaseServiceUrl}/<project_id>", view_func=service_api, methods=["DELETE"]
)
presm_bp.add_url_rule(
    f"{BaseServiceUrl}/package", view_func=service_api, methods=["PUT"]
)

log_api = EnvServiceLogAPI.as_view("log_api")
presm_bp.add_url_rule("/envs/logs", view_func=log_api, methods=["GET"])
presm_bp.add_url_rule("/envs/<pre_uuid>/logs",
                      view_func=log_api, methods=["GET"])

grayrule_api = EnvGrayRuleAPI.as_view("grayrule_api")
presm_bp.add_url_rule(
    "/envs/grayrule", view_func=grayrule_api, methods=["GET", "POST"])
presm_bp.add_url_rule(
    "/envs/grayrule/preuuid/<pre_uuid>", view_func=grayrule_api, methods=["GET"]
)
presm_bp.add_url_rule(
    "/envs/grayrule/<albumid>", view_func=grayrule_api, methods=["DELETE", "PUT"]
)

grayrule_uri_api = EnvGrayRuleUriAPI.as_view("grayrule_uri_api")
presm_bp.add_url_rule(
    "/envs/grayrule/uri",
    view_func=grayrule_uri_api,
    methods=["GET", "PUT"],
    strict_slashes=False,
)
presm_bp.add_url_rule(
    "/envs/grayrule/uri/<uri_id>",
    view_func=grayrule_uri_api,
    methods=["GET", "PUT", "DELETE"],
    strict_slashes=False,
)
