#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
@Title:   env_api.py
@Date:    2025/03/14
@Desc:    环境信息查询接口
"""
from flask import Blueprint, request, jsonify
from model import CiProjects, CiSandboxProjectiles, CiPreServiceMap
from extension import db

# 创建蓝图
env_api = Blueprint('internal_devops', __name__, url_prefix="/devInternal/api/v1")

@env_api.route('/fe_projects_list', methods=['GET'])
def get_project_details():
    """
    查询不同环境类型下的项目详情
    参数:
      - env_type: 环境类型 (test, pre, prod)
      - env_id: 环境ID
    """
    env_type = request.args.get('env_type')
    env_id = request.args.get('env_id')

    # 参数验证
    if not env_type or not env_id:
        return jsonify({
            'code': 400,
            'message': '缺少必要参数: env_type 或 env_id',
            'data': None
        }), 400

    if env_type not in ['test', 'pre', 'prod']:
        return jsonify({
            'code': 400,
            'message': '无效的环境类型。必须是: test, pre, prod',
            'data': None
        }), 400

    # 生产环境暂不实现
    if env_type == 'prod':
        return jsonify({
            'code': 501,
            'message': '生产环境查询暂未实现',
            'data': None
        }), 501

    try:
        if env_type == 'test':
            # 测试环境查询 (使用 sbx_uuid)
            results = db.session.query(
                CiProjects.appname,
                CiSandboxProjectiles.sbx_uuid,
                CiSandboxProjectiles.project_branch
            ).join(
                CiSandboxProjectiles,
                CiProjects.id == CiSandboxProjectiles.project_id
            ).filter(
                CiProjects.type == 1,
                CiSandboxProjectiles.sbx_uuid == env_id
            ).all()

            # 转换查询结果为字典列表
            data = [{
                'appname': item[0],
                'sbx_uuid': item[1],
                'project_branch': item[2]
            } for item in results]

        elif env_type == 'pre':
            # 预发环境查询 (使用 pre_uuid)
            results = db.session.query(
                CiProjects.appname,
                CiPreServiceMap.pre_uuid,
                CiPreServiceMap.project_branch
            ).join(
                CiPreServiceMap,
                CiProjects.id == CiPreServiceMap.project_id
            ).filter(
                CiProjects.type == 1,
                CiPreServiceMap.pre_uuid == env_id
            ).all()

            # 转换查询结果为字典列表
            data = [{
                'appname': item[0],
                'pre_uuid': item[1],
                'project_branch': item[2]
            } for item in results]

        if not data:
            return jsonify({
                'code': 404,
                'message': '未找到数据',
                'data': None
            }), 404

        return jsonify({
            'code': 0,
            'message': '成功',
            'data': data
        })

    except Exception as e:
        return jsonify({
            'code': 500,
            'message': f'服务器错误: {str(e)}',
            'data': None
        }), 500
