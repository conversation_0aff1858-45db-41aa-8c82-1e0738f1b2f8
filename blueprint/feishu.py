#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
@Title:     feishu.py
@Date:      2023/5/31 11:09
@Author:    wanglh
@Desc：     飞书应用
"""

import json
from typing import List, Tuple

import requests
from flask import Blueprint, current_app, jsonify, request
from sqlalchemy import or_
from sqlalchemy.exc import SQLAlchemyError

from model import CiUpgradeSchedule, FeishuApprovalDef, FeishuApprovalNode, FeishuChatData, FeishuUserData, SsousersModel
from utils import get_response, get_tenant_access_token, get_timestamp, send_feishu_message
from utils.feishu import config_read, env
from utils.feishu.approvalClass import build_form, build_node_approver_open_id_list, get_def_data, get_node_list

fs_approval_bp = Blueprint('fs_approval', __name__, url_prefix='/feishu/api/v1/approval')
fs_chat_cp = Blueprint('fs_chat', __name__, url_prefix='/feishu/api/v1/chat')
fs_msg_bp = Blueprint('fs_message', __name__, url_prefix="/feishu/api/v1/message")

approval_code = config_read.get(f'approval-{env}', 'approval_code')

@fs_msg_bp.route("/send/release", methods=['POST'])
def send_release() -> jsonify:
    """
    通知模板
    Args
        name (str): 消息人，或者抄送人
        tempalte_variable (dict): 消息内容
    Returns
        jsonify
    """
    LOCAL_HOST = current_app.config.get('LOCAL_HOST')
    LOCAL_PORT = current_app.config.get('LOCAL_PORT')
    data_raw = get_response(request)
    template_variable = data_raw.get('template_variable')
    user_name = data_raw.get('name') if data_raw.get('name') else None  # type：str
    user_name_list = user_name.split(',')
    chat_name = data_raw.get("chat_name", "版本发布群")
    card_id = data_raw.get("card_id", '')
    current_app.logger.info(f"接收到消息发型通知: {data_raw}", data_raw)
    chatRes = FeishuChatData.query.filter_by(name=chat_name, approval_code=approval_code).first()
    if user_name:
        task_filter = {
            or_(
                SsousersModel.username.in_(user_name_list),
                SsousersModel.displayname.in_(user_name_list)
            )
        }

        userResult = FeishuUserData.query \
            .join(SsousersModel, or_(SsousersModel.email == FeishuUserData.email,
                                     SsousersModel.displayname == FeishuUserData.name)) \
            .filter(*task_filter).with_entities(FeishuUserData.user_id, FeishuUserData.open_id, FeishuUserData.name) \
            .all()

        # 将name添加到群组中
        try:
            id_list = [uRes.open_id for uRes in userResult]
            id_list = list(set(id_list))
            if len(id_list) > 0:
                # members_url = url_for('fs_chat.chat_members', _external=True)
                members_url = f"http://{LOCAL_HOST}:{LOCAL_PORT}/feishu/api/v1/chat/members"  # 将 name 添加到群组
                headers = {'Content-Type': 'application/json'}
                data = {"chat_id": chatRes.chat_id, "id_list": id_list}
                current_app.logger.info(f"将用户添加到版本发布群的参数的：{data}")
                response = requests.post(members_url, headers=headers, data=json.dumps(data))
                current_app.logger.info(f"版本发布群发送消息-拉群结果：{response.text}")
            else:
                current_app.logger.info("没有找到用户")
        except Exception as e:
            current_app.logger.error(f"{e}")

        # 添加版本发布的归属者(排除运维人员)，兼容多个抄送者或无的情况 传递过来的change_owner需要为字符串
        if chat_name == "版本发布群":
            devops_groups = current_app.config.get('DEVOPS_GROUP')
            open_ids = [f"<at id={uRes.open_id}></at>" for uRes in userResult if uRes.name not in devops_groups]
            template_variable['change_owner'] = " ".join(open_ids)
        if env == "dev":
            # 测试环境 测试验证
            open_ids = [f"<at id={config_read.get('TEST-OPEN-ID-dev', 'receive_id')}></at>"]

    response = send_feishu_message(receive_id=chatRes.chat_id, msg_type="interactive", receive_id_type='chat_id',
                                   template_variable=template_variable, card_id=card_id)

    current_app.logger.info(f"发送消息参数：{template_variable}, {chatRes.chat_id}, {card_id}")
    current_app.logger.info(f"发送消息-参数：{env}")
    current_app.logger.info(f"发送消息结果：{response.text}")
    current_app.logger.info(f"发送消息状态码：{response.status_code}")
    return response.text, response.status_code


@fs_msg_bp.route("/send/alert", methods=['POST'])
def send_alert() -> jsonify:
    """
    飞书告警
    Args
        content (json): 消息文本
    Returns
        jsonify
    """
    data_raw = get_response(request)
    chatRes = FeishuChatData.query.filter_by(name="运维告警", approval_code=approval_code).first()

    if data_raw.get('content'):
        response = send_feishu_message(receive_id=chatRes.chat_id, msg_type="text", receive_id_type='chat_id',
                                       template_variable=data_raw.get('content'))
        return response.text, response.status_code
    return jsonify({'code': 13000991, 'msg': '参数不正确'}), 400


@fs_approval_bp.route('/info')
def approval_def():
    """
    返回审批定义的相关参数

    Returns
        JSON响应，包含审批定义数据和审批节点数据
    """

    try:
        def_query = FeishuApprovalDef.query.filter_by(approval_code=approval_code).all()
        def_data = get_def_data(def_query)

        node_query = FeishuApprovalNode.query.filter_by(approval_code=approval_code).all()
        node_list = get_node_list(node_query)
        if not def_data and not node_list:
            return jsonify({'code': 3, 'msg': 'No data found for the given approval code'}), 404
    except SQLAlchemyError as e:
        return jsonify({'code': 1, 'msg': 'Database error: {}'.format(str(e))}), 500
    return jsonify({'code': 0, 'def_data': def_data, 'node_data': node_list}), 200


@fs_approval_bp.route('/create', methods=['POST'])
def approval_create() -> Tuple[jsonify, int]:
    """
    创建审批

    Returns:
        jsonify
    """
    url = "https://open.feishu.cn/open-apis/approval/v4/instances"
    user_name = request.headers.get("X-User")

    # 获取用户信息
    user = (
        FeishuUserData.query.join(
            SsousersModel, SsousersModel.email == FeishuUserData.email
        )
        .filter(
            or_(
                SsousersModel.username == user_name,
                SsousersModel.displayname == user_name,
            )
        )
        .with_entities(
            FeishuUserData.user_id,
            FeishuUserData.open_id,
            FeishuUserData.name,
        )
        .first()
    )

    if user is None:
        return jsonify({"msg": "User not found."}), 404

    id_list = [user.open_id]
    current_app.logger.info(f"当前版本发布申请人:{user.name}，open_id：{user.open_id}")

    # 获取审批定义
    fieldlist_query = (
        FeishuApprovalDef.query.filter_by(
            type="fieldList", approval_code=approval_code
        ).first()
    )

    if fieldlist_query is None:
        return jsonify({"msg": "Approval definition not found."}), 404

    def_query = (
        FeishuApprovalDef.query.filter(
            FeishuApprovalDef.type != "fieldList",
            FeishuApprovalDef.approval_code == approval_code,
        ).all()
    )
    def_query_dict = {record.html_id: record for record in def_query}

    node_query = (
        FeishuApprovalNode.query.filter_by(approval_code=approval_code).all()
    )
    node_query_dict = {record.html_id: record for record in node_query}

    # 获取参数
    get_params = get_response(request)
    get_params["plan_datetime"] = (
        f"{get_params.get('plan_date')} {get_params.get('plan_time')}"
    )

    # get_params.pop("plan_date", None)
    # get_params.pop("plan_time", None)

    # 构建表单数据
    form = build_form(fieldlist_query, def_query_dict, get_params)

    # 构建审批节点数据
    node_approver_open_id_list, id_list = build_node_approver_open_id_list(
        node_query_dict, get_params, id_list
    )

    # 发起审批请求
    headers = {
        "Content-Type": "application/json",
        "Authorization": f"Bearer {get_tenant_access_token()}",
    }
    data = {
        "approval_code": approval_code,
        "open_id": user.open_id,
        "form": json.dumps(form, ensure_ascii=False),
        "node_approver_open_id_list": node_approver_open_id_list,
    }
    response = requests.post(url, headers=headers, json=data)

    if response.status_code != 200:
        current_app.logger.error(f"审批创建失败：{response.text}")
        return jsonify({"msg": "Failed to create approval."}), 500

    # 解析响应数据
    response_data = response.json()
    if response_data.get("code") != 0:
        current_app.logger.error(f"审批创建失败：{response_data}")
        return (
            jsonify({"msg": "Failed to create approval.", "details": response_data}),
            500,
        )

    current_app.logger.info(f'审批自动创建完成，创建者：{user.name}')

    # 创建审批成功，记录审批实例ID
    approval_instance_id = response_data.get("data").get("instance_code")
    get_params['fs_instanceid'] = approval_instance_id
    current_app.logger.info(f"审批创建成功，实例ID：{approval_instance_id}")

    # 将当前审批的人员添加到 "版本发布群中"
    chat_query = FeishuChatData.query.filter_by(name="版本发布群", approval_code=approval_code).first()
    if chat_query is None:
        return jsonify({"msg": "Chat not found."}), 404

    chat_id = chat_query.chat_id
    id_list = list(set(id_list))
    LOCAL_HOST = current_app.config.get('LOCAL_HOST')
    LOCAL_PORT = current_app.config.get('LOCAL_PORT')
    members_url = f"http://{LOCAL_HOST}:{LOCAL_PORT}/feishu/api/v1/chat/members"
    # members_url = url_for('fs_chat.chat_members', _external=True)
    current_app.logger.info({"chat_id": chat_id, "id_list": id_list})
    try:
        headers = {'Content-Type': "application/json"}
        res = requests.post(members_url, headers=headers, data=json.dumps({"chat_id": chat_id, "id_list": id_list}))
    except Exception as e:
        current_app.logger.info(f"{e}")

    # 记录审批实例ID到数据库
    upgradeplan_url = f"http://{LOCAL_HOST}:{LOCAL_PORT}/ci/api/v1/upgradePlan/update"
    # upgradeplan_url = url_for('ci_sandbox.upgradeplan_update', _external=True)
    get_params['owner'] = request.headers.get('X-User')
    response = requests.post(upgradeplan_url, json=get_params)
    if response.status_code != 200:
        msg = f'审批数据记录到数据库表：{CiUpgradeSchedule.__tablename__}失败'
        current_app.logger.error(msg)
    msg = f'审批数据记录到数据库表：{CiUpgradeSchedule.__tablename__}成功'
    current_app.logger.info(msg)
    return jsonify({"msg": msg, "approval_instance_id": approval_instance_id}), 200


@fs_approval_bp.route('/instance_id')
def approval_instance_id() -> jsonify:
    """
    获取单个审批实例详情
    Return
    """
    instance_id = (get_response(request)).get('instance_id')
    if instance_id:
        url = f"https://open.feishu.cn/open-apis/approval/v4/instances/{instance_id}"
        headers = {
            'Authorization': f'Bearer {get_tenant_access_token()}'
        }
        response = requests.request("GET", url, headers=headers)
        return response.text
    return jsonify({'code': 1, 'msg': '参数确实'}), 400


@fs_approval_bp.route('/instances')
def approval_instances() -> jsonify:
    """
    批量获取审批实例 ID
    Returns
    """

    data_raw = get_response(request)
    _, str_millisecond = get_timestamp(data_raw.get('start_time'))
    _, end_millisecond = get_timestamp(data_raw.get('end_time'))
    params = {
        "approval_code": approval_code,
        "end_time": end_millisecond,
        "page_size": data_raw.get('page_size'),
        "start_time": str_millisecond,
    }
    headers = {
        'Authorization': f'Bearer {get_tenant_access_token()}'
    }
    url = "https://open.feishu.cn/open-apis/approval/v4/instances"
    response = requests.request("GET", url, headers=headers, params=params)
    return response.text, response.status_code


@fs_chat_cp.route('/members', methods=['POST'])
def chat_members() -> jsonify:
    """
    将用户或者机器人添加到指定群聊中
    Args
        chat_id: 群号ID
        id_list: 需要添加的群成员列表, (open_id/user_id) 可为空，为空跳过，返回403
    Returns
        jsonify
    """

    params_data = get_response(request)
    if params_data.get('chat_id') and params_data.get('id_list'):
        url = f"https://open.feishu.cn/open-apis/im/v1/chats/{params_data.get('chat_id')}/members?member_id_type=open_id"
        id_list = params_data.get('id_list')
        payload = json.dumps({
            "id_list": id_list.split(',') if isinstance(id_list, str) else id_list
        })
        current_app.logger.info(f"需要添加的群成员列表参数：{payload}")
        headers = {
            'Content-Type': 'application/json',
            'Authorization': f'Bearer {get_tenant_access_token()}'
        }
        response = requests.request("POST", url, headers=headers, data=payload)
        current_app.logger.info(response)
        return response.text, response.status_code
    msg = "缺少必要的参数id_list" if params_data.get('chat_id') else "缺少必要的参数chat_id"
    return jsonify({"code": 13009991, "msg": msg}), 400


@fs_chat_cp.route('/query/group/name')
def get_query_group_name():
    try:
        alert_groups = FeishuChatData.query.filter(FeishuChatData.name.like('%告警%')).all()
        result = FeishuChatData.to_all_json(alert_groups)
        name_list = [{"label": item['name'],"value":item['chat_id']} for item in result ]
        return jsonify({"data":name_list}), 200

    except SQLAlchemyError as e:
        error_msg = f"Database error: {e}"
        return jsonify({"error": error_msg}), 500

@fs_chat_cp.route('/query/robot/<chat_id>')
def get_group_robot_url(chat_id):
    try:
        alert_groups = FeishuChatData.query.filter_by(chat_id=chat_id).first()
        if alert_groups.robot_url is None:
            return jsonify({"webhook":""}), 200
        return jsonify({"webhook":alert_groups.robot_url}), 200

    except SQLAlchemyError as e:
        error_msg = f"Database error: {e}"
        return jsonify({"error": error_msg}), 500


class MessageCardVariable:
    """
    飞书消息卡片变量结构化模型
    """
    def __init__(self, msgDetail: str = "", msgObject: str = "", msgTitle: str = "", msgUrl: str = ""):
        self.msgDetail = msgDetail  # 具体信息
        self.msgObject = msgObject  # 消息对象
        self.msgTitle = msgTitle    # 消息标题
        self.msgUrl = msgUrl        # 自定义链接（分端差异化链接）

    def to_dict(self) -> dict:
        return {
            "msgDetail": self.msgDetail,
            "msgObject": self.msgObject,
            "msgTitle": self.msgTitle,
            "msgUrl": self.msgUrl
        }

    @classmethod
    def from_dict(cls, data: dict):
        return cls(
            msgDetail=data.get("msgDetail", ""),
            msgObject=data.get("msgObject", ""),
            msgTitle=data.get("msgTitle", ""),
            msgUrl=data.get("msgUrl", "")
        )


def get_user_open_ids(user_ids: str) -> List[str]:
    if not user_ids:
        return []

    user_id_list = [uid.strip() for uid in user_ids.split(',') if uid.strip()]

    if not user_id_list:
        return []

    try:
        user_results = FeishuUserData.query.filter(
            FeishuUserData.user_id.in_(user_id_list)
        ).with_entities(FeishuUserData.open_id).all()

        return [user.open_id for user in user_results if user.open_id]

    except SQLAlchemyError as e:
        current_app.logger.error(f"查询用户open_id失败: {e}")
        return []


@fs_msg_bp.route("/send/user", methods=['POST'])
def send_message_to_users() -> Tuple[str, int]:
    try:
        data_raw = get_response(request)
        current_app.logger.info(f"接收到发送消息请求: {data_raw}")

        user_ids = data_raw.get('user_ids')
        template_id = data_raw.get('template_id')
        template_version_name = data_raw.get('template_version_name')
        template_variable = data_raw.get('template_variable', {})

        if not user_ids:
            return jsonify({'code': 400, 'msg': '用户ID不能为空'}), 400

        if not template_id:
            template_id = config_read.get(f"message-id-{env}", "commonMsgCardId")
            if not template_id:
                return jsonify({'code': 400, 'msg': '模板ID不能为空，且配置文件中未找到commonMsgCardId'}), 400

        if not template_version_name:
            template_version_name = config_read.get(f"message-id-{env}", "commonMsgCardVersion")

        open_ids = get_user_open_ids(user_ids)
        if not open_ids:
            return jsonify({'code': 404, 'msg': '未找到有效的用户信息'}), 404

        msg_card = MessageCardVariable.from_dict(template_variable)

        message_content = {
            "type": "template",
            "data": {
                "template_id": template_id,
                "template_variable": msg_card.to_dict()
            }
        }

        if template_version_name:
            message_content["data"]["template_version_name"] = template_version_name

        success_count = 0
        failed_users = []

        for open_id in open_ids:
            try:
                params = {
                    "receive_id": open_id,
                    "msg_type": "interactive",
                    "content": json.dumps(message_content)
                }

                headers = {
                    'Content-Type': 'application/json',
                    'Authorization': f'Bearer {get_tenant_access_token()}'
                }

                url = "https://open.feishu.cn/open-apis/im/v1/messages?receive_id_type=open_id"
                response = requests.post(url, headers=headers, data=json.dumps(params))

                if response.status_code == 200:
                    response_data = response.json()
                    if response_data.get('code') == 0:
                        success_count += 1
                        current_app.logger.info(f"消息发送成功，用户open_id: {open_id}")
                    else:
                        failed_users.append(open_id)
                        current_app.logger.error(f"消息发送失败，用户open_id: {open_id}, 错误: {response_data}")
                else:
                    failed_users.append(open_id)
                    current_app.logger.error(f"消息发送失败，用户open_id: {open_id}, HTTP状态码: {response.status_code}")

            except Exception as e:
                failed_users.append(open_id)
                current_app.logger.error(f"发送消息异常，用户open_id: {open_id}, 错误: {e}")

        result = {
            'code': 200,
            'msg': '消息发送完成',
            'data': {
                'total_users': len(open_ids),
                'success_count': success_count,
                'failed_count': len(failed_users),
                'failed_users': failed_users
            }
        }

        current_app.logger.info(f"消息发送结果: {result}")
        return jsonify(result), 200

    except Exception as e:
        current_app.logger.error(f"发送消息接口异常: {e}")
        return jsonify({'code': 500, 'msg': f'服务器内部错误: {str(e)}'}), 500