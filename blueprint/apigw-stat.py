from flask import Blueprint, abort, request, jsonify, current_app
from flask.views import MethodView
from sqlalchemy import func, case, desc, not_, and_, or_, distinct
from datetime import datetime, timedelta, timezone
from utils import *
import traceback
import decimal  # 确保导入decimal模块

# 假设已有这些模型，如果没有请定义它们
# from models import ApiRequest, ApiRequest1m, ApiRequest1h

# 定义东八区时区
EAST_EIGHT_TZ = timezone(timedelta(hours=8))

# 将Decimal转换为float的辅助函数
def to_float(value):
    if value is None:
        return 0.0
    if isinstance(value, decimal.Decimal):
        return float(value)
    return float(value)

api_monitor = Blueprint('api_monitor', __name__, url_prefix='/devmonitor/api/v1/apigw')

# 根据开始时间与当前时间的差值选择合适的表
def select_table_by_start_time(start_time):
    """
    根据查询起始时间与当前时间的差值选择合适的数据表：
      - 如果查询起始时间与当前时间的差值 <= 30 分钟，使用原始表 ApiRequest
      - 如果超过 30 分钟但不超过 72 小时，使用聚合表 ApiRequest1m
      - 如果超过 72 小时，使用聚合表 ApiRequest1h
    """
    now = datetime.now()
    time_diff = (now - start_time).total_seconds()
    current_app.logger.info(f"查询起始时间与当前时间的差值：{time_diff}")
    # 30分钟+小时 = 30660秒, 72小时+8小时 = 259200秒
    if time_diff <= 30660:
        return ApiRequest
    elif time_diff <= 288000:
        return ApiRequest1m
    else:
        return ApiRequest1h

# 获取表对应的时间桶字段
def get_time_bucket_column(table):
    """
    根据表类型获取对应的时间桶字段：
      - 原始表使用time_bucket
      - ApiRequest1m表使用minute_bucket
      - ApiRequest1h表使用hour_bucket
    """
    if table == ApiRequest:
        return table.time_bucket
    elif table == ApiRequest1m:
        return table.minute_bucket
    elif table == ApiRequest1h:
        return table.hour_bucket
    else:
        raise ValueError("Unknown table type")

# 日期解析函数，用于兼容不同 Python 版本
def parse_datetime(date_str):
    """解析ISO 8601格式的日期字符串"""
    try:
        # 处理带毫秒和Z时区的ISO 8601格式
        if 'Z' in date_str:
            # 去掉Z时区标识
            date_str = date_str.replace('Z', '')

        # 如果有毫秒，去掉毫秒部分
        if '.' in date_str:
            date_str = date_str.split('.')[0]

        # 尝试解析ISO 8601格式
        return datetime.strptime(date_str, "%Y-%m-%dT%H:%M:%S")
    except ValueError:
        abort(400, description="Invalid date format. Expected ISO 8601 format (YYYY-MM-DDTHH:MM:SS)")

@api_monitor.route('/overview', methods=['GET'])
def get_overview():
    """获取时间段内的主要指标概览"""
    try:
        start_time = request.args.get('start_time')
        end_time = request.args.get('end_time')

        # 验证并解析时间参数
        if not start_time or not end_time:
            abort(400, description="`start_time` and `end_time` are required.")
        start_time_parsed = parse_datetime(start_time)
        end_time_parsed = parse_datetime(end_time)

        # 根据开始时间选择表
        table = select_table_by_start_time(start_time_parsed)

        # 构建过滤条件
        filters = create_filters_from_request(table)

        # 计算时间差(秒)用于 QPS 计算
        time_diff = (end_time_parsed - start_time_parsed).total_seconds()

        # 查询概览指标
        result = db.session.query(
            func.sum(table.count).label('total_requests'),
            func.sum(
                case((table.status >= 400, table.count), else_=0)
            ).label('error_count'),
            func.sum(table.total_time).label('total_time')
        ).filter(*filters).first()

        # 将decimal.Decimal转换为float，然后计算平均响应时间
        total_requests = float(result.total_requests) if result.total_requests else 0
        total_time = float(result.total_time) if result.total_time else 0
        error_count = float(result.error_count) if result.error_count else 0

        # 计算平均响应时间
        avg_rt = (total_time / total_requests) if total_requests > 0 else 0

        error_filters = []
        # 时间范围过滤（使用created_at字段）
        error_filters.append(CiProdStatApiErrors.created_at >= parse_datetime(start_time)+ timedelta(hours=8))
        error_filters.append(CiProdStatApiErrors.created_at <= parse_datetime(end_time)+ timedelta(hours=8))
        # 根据请求中的其他过滤条件（如 host、env）进行过滤
        for param in ['host', 'env']:
            if hasattr(CiProdStatApiErrors, param):
                value = request.args.get(param)
                if value:
                    tokens = [v.strip() for v in value.split(',') if v.strip()]
                    if len(tokens) == 1:
                        error_filters.append(getattr(CiProdStatApiErrors, param) == tokens[0])
                    else:
                        error_filters.append(getattr(CiProdStatApiErrors, param).in_(tokens))
                not_value = request.args.get('not_' + param)
                if not_value:
                    tokens = [v.strip() for v in not_value.split(',') if v.strip()]
                    if len(tokens) == 1:
                        error_filters.append(getattr(CiProdStatApiErrors, param) != tokens[0])
                    else:
                        error_filters.append(~getattr(CiProdStatApiErrors, param).in_(tokens))

        # 统计去重的trace_id数量
        errorTracingCount = db.session.query(
            func.count(distinct(CiProdStatApiErrors.trace_id))
        ).filter(*error_filters).scalar()

        return jsonify({
            'totalQps': total_requests / time_diff if time_diff > 0 else 0,
            'errorCount': error_count,
            'avgResponseTime': avg_rt,
            'errorTracingCount': errorTracingCount
        })
    except Exception as e:
        traceback.print_exc()
        abort(500, description="An error occurred while fetching the overview.")

@api_monitor.route('/topn', methods=['GET'])
def get_topn():
    """获取不同维度的TopN数据"""
    try:
        dimension = request.args.get('dimension')
        limit = int(request.args.get('limit', 5))

        # 解析时间参数
        start_time = request.args.get('start_time')
        end_time = request.args.get('end_time')
        if not start_time or not end_time:
            abort(400, description="`start_time` and `end_time` are required.")
        start_time_parsed = parse_datetime(start_time)
        end_time_parsed = parse_datetime(end_time)

        # 根据开始时间选择表
        table = select_table_by_start_time(start_time_parsed)

        # 构建过滤条件
        filters = create_filters_from_request(table)

        # 计算时间差(秒)用于 QPS 计算
        time_diff = (end_time_parsed - start_time_parsed).total_seconds()

        # 根据不同维度查询 TopN 数据
        queries = {
            'url_qps': lambda: db.session.query(
                table.host,
                table.path,
                func.sum(table.count).label('total_requests')
            ).filter(*filters).group_by(table.host, table.path)
                .order_by(desc('total_requests')).limit(limit),
            'service_qps': lambda: db.session.query(
                table.host,
                func.sum(table.count).label('total_requests')
            ).filter(*filters).group_by(table.host)
                .order_by(desc('total_requests')).limit(limit),
            'url_rt': lambda: db.session.query(
                table.host,
                table.path,
                (func.sum(table.total_time) / func.sum(table.count)).label('avg_rt')
            ).filter(*filters).group_by(table.host, table.path)
                .order_by(desc('avg_rt')).limit(limit),
            'service_rt': lambda: db.session.query(
                table.host,
                (func.sum(table.total_time) / func.sum(table.count)).label('avg_rt')
            ).filter(*filters).group_by(table.host)
                .order_by(desc('avg_rt')).limit(limit),
            'url_error': lambda: db.session.query(
                table.host,
                table.path,
                func.sum(case((table.status >= 400, table.count), else_=0)).label('error_count')
            ).filter(*filters).group_by(table.host, table.path)
                .order_by(desc('error_count')).limit(limit),
            'error_host': lambda: db.session.query(
                table.host,
                func.sum(case((table.status >= 400, table.count), else_=0)).label('error_count')
            ).filter(*filters).group_by(table.host)
                .order_by(desc('error_count')).limit(limit)
        }

        if dimension not in queries:
            abort(400, description="Invalid dimension parameter.")

        query = queries[dimension]()
        results = []

        # 处理结果
        for r in query:
            if dimension in ['url_qps', 'service_qps']:
                results.append({
                    'qps': to_float(r.total_requests) / time_diff if time_diff > 0 else 0,
                    **({ 'host': r.host, 'path': r.path } if 'url' in dimension else { 'host': r.host })
                })
            elif dimension in ['url_rt', 'service_rt']:
                results.append({
                    'avg_response_time': r.avg_rt,
                    **({ 'host': r.host, 'path': r.path } if 'url' in dimension else { 'host': r.host })
                })
            elif dimension  == 'error_host':
                results.append({
                    'host': r.host,
                    'error_count': r.error_count
                })
            elif dimension == 'url_error':
                results.append({
                    'host': r.host,
                    'path': r.path,
                    'error_count': r.error_count
                })

        return jsonify(results)
    except Exception as e:
        traceback.print_exc()
        abort(500, description="An error occurred while fetching the TopN data.")

@api_monitor.route('/details', methods=['GET'])
def get_details():
    """获取分页明细数据"""
    try:
        page = int(request.args.get('page', 1))
        page_size = int(request.args.get('page_size', 20))

        # 解析时间参数
        start_time = request.args.get('start_time')
        end_time = request.args.get('end_time')
        if not start_time or not end_time:
            abort(400, description="`start_time` and `end_time` are required.")
        start_time_parsed = parse_datetime(start_time)
        end_time_parsed = parse_datetime(end_time)

        # 根据开始时间选择表
        table = select_table_by_start_time(start_time_parsed)

        # 获取时间桶字段
        time_bucket_col = get_time_bucket_column(table)

        # 构建过滤条件
        filters = create_filters_from_request(table)

        # 统计总记录数
        total = db.session.query(func.count()).select_from(table).filter(*filters).scalar()

        # 获取分页数据
        query = db.session.query(table).filter(*filters) \
            .order_by(time_bucket_col.desc()) \
            .offset((page - 1) * page_size).limit(page_size)

        results = [{
            'time_bucket': getattr(r, time_bucket_col.name).replace(tzinfo=timezone.utc).astimezone(EAST_EIGHT_TZ).isoformat(),
            'host': r.host,
            'path': r.path,
            'status': r.status,
            'count': r.count,
            'avg_time': r.avg_time,
            'server_name': r.server_name
        } for r in query]

        return jsonify({
            'total': total,
            'data': results
        })
    except Exception as e:
        traceback.print_exc()
        abort(500, description="An error occurred while fetching the details.")

@api_monitor.route('/timeseries', methods=['GET'])
def get_timeseries():
    """获取时间段内的QPS趋势数据，根据传入的compare_date对比相同时间段"""
    try:
        start_time = request.args.get('start_time')
        end_time = request.args.get('end_time')
        compare_date = request.args.get('compare_date')

        if not start_time or not end_time:
            abort(400, description="`start_time` and `end_time` are required.")

        start_time_parsed = parse_datetime(start_time)
        end_time_parsed = parse_datetime(end_time)

        # 根据开始时间选择表
        table = select_table_by_start_time(start_time_parsed)

        # 获取时间桶字段
        time_bucket_col = get_time_bucket_column(table)

        time_diff_seconds = (end_time_parsed - start_time_parsed).total_seconds()

        # 根据选择的表和时间跨度调整时间间隔
        if table == ApiRequest:
            bucket_interval = 30  # 原始表使用30秒
            if time_diff_seconds > 3600:
                bucket_interval = 300  # 超过1小时，使用5分钟
        elif table == ApiRequest1m:
            bucket_interval = 60  # 1分钟聚合表使用60秒
            if time_diff_seconds > 6 * 3600:
                bucket_interval = 300  # 超过6小时，使用5分钟
        else:  # ApiRequest1h
            bucket_interval = 3600  # 1小时聚合表使用3600秒

        base_filters = create_filters_from_request_without_time(table)
        main_filters = base_filters.copy()
        main_filters.extend([
            time_bucket_col >= start_time_parsed,
            time_bucket_col <= end_time_parsed
        ])

        bucket_expr = func.from_unixtime(
            func.floor(func.unix_timestamp(time_bucket_col) / bucket_interval) * bucket_interval
        ).label('bucket_time')

        main_query = db.session.query(
            bucket_expr,
            func.sum(table.count).label('total_requests')
        ).filter(*main_filters).group_by('bucket_time').order_by('bucket_time')

        main_series = []
        for r in main_query.all():
            qps = to_float(r.total_requests) / bucket_interval
            bucket_time_east = r.bucket_time.replace(tzinfo=timezone.utc).astimezone(EAST_EIGHT_TZ)
            main_series.append({
                'time': bucket_time_east.isoformat(),
                'qps': qps
            })

        compare_series = []
        if compare_date:
            try:
                compare_date_parsed = parse_datetime(compare_date)
                compare_start = compare_date_parsed.replace(
                    hour=start_time_parsed.hour,
                    minute=start_time_parsed.minute,
                    second=start_time_parsed.second,
                    microsecond=start_time_parsed.microsecond
                )
                compare_end = compare_start + (end_time_parsed - start_time_parsed)

                # 对比数据也根据开始时间选择表
                compare_table = select_table_by_start_time(compare_start)

                # 获取对比表的时间桶字段
                compare_time_bucket_col = get_time_bucket_column(compare_table)

                compare_filters = create_filters_from_request_without_time(compare_table)
                compare_filters.extend([
                    compare_time_bucket_col >= compare_start,
                    compare_time_bucket_col <= compare_end
                ])

                # 使用与主查询相同的桶间隔
                compare_bucket_expr = func.from_unixtime(
                    func.floor(func.unix_timestamp(compare_time_bucket_col) / bucket_interval) * bucket_interval
                ).label('bucket_time')

                compare_query = db.session.query(
                    compare_bucket_expr,
                    func.sum(compare_table.count).label('total_requests')
                ).filter(*compare_filters).group_by('bucket_time').order_by('bucket_time')

                date_shift = start_time_parsed.date() - compare_start.date()

                for r in compare_query.all():
                    qps = to_float(r.total_requests) / bucket_interval
                    bucket_time_east = r.bucket_time.replace(tzinfo=timezone.utc).astimezone(EAST_EIGHT_TZ)
                    adjusted_time = (bucket_time_east + timedelta(days=date_shift.days)).isoformat()
                    compare_series.append({
                        'time': adjusted_time,
                        'qps': qps
                    })
            except Exception as e:
                traceback.print_exc()

        return jsonify({
            'main_series': main_series,
            'compare_series': compare_series
        })

    except Exception as e:
        traceback.print_exc()
        abort(500, description="An error occurred while fetching the timeseries data.")

def create_filters_from_request_without_time(table):
    """构建不包含时间条件的过滤条件"""
    filters = []

    # 添加响应时间过滤
    response_time_min = request.args.get('response_time_min')
    response_time_max = request.args.get('response_time_max')

    if response_time_min:
        try:
            min_value = float(response_time_min) / 1000.0
            filters.append(table.avg_time >= min_value)
        except ValueError:
            abort(400, description="Invalid value for response_time_min. Must be a number.")

    if response_time_max:
        try:
            max_value = float(response_time_max) / 1000.0
            filters.append(table.avg_time < max_value)
        except ValueError:
            abort(400, description="Invalid value for response_time_max. Must be a number.")

    for param in ['host', 'path', 'server_name', 'status', 'env']:
        if hasattr(table, param):  # 确保表有这个属性
            column = getattr(table, param)
            # 正向过滤：支持多个值，用逗号分割
            value = request.args.get(param)
            if value:
                tokens = [v.strip() for v in value.split(',') if v.strip()]
                if param == 'status':
                    try:
                        tokens_int = [int(tok) for tok in tokens]
                    except ValueError:
                        abort(400, description=f"Invalid value for {param}.")
                    if len(tokens_int) == 1:
                        filters.append(column == tokens_int[0])
                    else:
                        filters.append(column.in_(tokens_int))
                else:
                    conditions = []
                    for token in tokens:
                        if "*" in token:
                            pattern = token.replace("*", "%")
                            conditions.append(column.like(pattern))
                        else:
                            conditions.append(column.like(f"%{token}%"))
                    if conditions:
                        if len(conditions) == 1:
                            filters.append(conditions[0])
                        else:
                            filters.append(or_(*conditions))

            # 排除过滤：支持多个值，用逗号分割，如 not_host=acs,bcs 表示 host 不等于 acs 且不等于 bcs
            not_value = request.args.get('not_' + param)
            if not_value:
                tokens = [v.strip() for v in not_value.split(',') if v.strip()]
                if param == 'status':
                    try:
                        tokens_int = [int(tok) for tok in tokens]
                    except ValueError:
                        abort(400, description=f"Invalid value for not_{param}.")
                    if len(tokens_int) == 1:
                        filters.append(column != tokens_int[0])
                    else:
                        filters.append(~column.in_(tokens_int))
                else:
                    conditions = []
                    for token in tokens:
                        if "*" in token:
                            pattern = token.replace("*", "%")
                            conditions.append(column.notlike(pattern))
                        else:
                            conditions.append(column != token)
                    if conditions:
                        if len(conditions) == 1:
                            filters.append(conditions[0])
                        else:
                            filters.append(and_(*conditions))
    return filters

def create_filters_from_request(table):
    """从请求参数构建SQLAlchemy过滤条件（包含时间过滤条件）"""
    filters = []

    # 时间范围过滤(必需)
    start_time = request.args.get('start_time')
    end_time = request.args.get('end_time')

    if start_time and end_time:
        time_bucket_col = get_time_bucket_column(table)
        filters.append(time_bucket_col >= parse_datetime(start_time))
        filters.append(time_bucket_col <= parse_datetime(end_time))

    # 添加其他过滤条件
    time_filters = create_filters_from_request_without_time(table)
    filters.extend(time_filters)

    return filters

@api_monitor.route('/filters', methods=['GET'])
def get_filter_options():
    """获取过滤条件选项"""
    filter_type = request.args.get('type')  # host, path, server_name
    keyword = request.args.get('keyword', '')

    # 默认使用1小时聚合表查询过滤选项，避免原始表数据量过大
    table = ApiRequest1h

    if filter_type in ['host', 'path', 'server_name'] and hasattr(table, filter_type):
        column = getattr(table, filter_type)
        query = db.session.query(column).filter(
            column.like(f'%{keyword}%')
        ).distinct().limit(20)

        results = [getattr(r, filter_type) for r in query]
        return jsonify(results)

    return jsonify([])

@api_monitor.route('/trace-details', methods=['GET'])
def get_error_details():
    """查询错误信息接口，支持按各种条件过滤（含分页）"""
    try:
        page = int(request.args.get('page', 1))
        page_size = int(request.args.get('page_size', 20))

        filters = []

        # 根据 created_at 时间范围进行过滤（非必填）
        start_time = request.args.get('start_time')
        end_time = request.args.get('end_time')
        if start_time:
            filters.append(CiProdStatApiErrors.created_at >= parse_datetime(start_time) + timedelta(hours=8) )
        if end_time:
            filters.append(CiProdStatApiErrors.created_at <= parse_datetime(end_time) + timedelta(hours=8))

        # 支持的过滤字段
        for field in ['host', 'env', 'err_code', 'trace_id', 'uri', 'user_id']:
            # 正向过滤：支持多个值，用逗号分割，可使用通配符"*"（转换为SQL LIKE）
            value = request.args.get(field)
            if value:
                tokens = [v.strip() for v in value.split(',') if v.strip()]
                column = getattr(CiProdStatApiErrors, field)
                conditions = []
                for token in tokens:
                    if "*" in token:
                        conditions.append(column.like(token.replace("*", "%")))
                    else:
                        conditions.append(column == token)
                if conditions:
                    if len(conditions) == 1:
                        filters.append(conditions[0])
                    else:
                        filters.append(or_(*conditions))

            # 排除过滤，如 not_host=xxx,not_env=prd
            not_value = request.args.get("not_" + field)
            if not_value:
                tokens = [v.strip() for v in not_value.split(',') if v.strip()]
                column = getattr(CiProdStatApiErrors, field)
                conditions = []
                for token in tokens:
                    if "*" in token:
                        conditions.append(column.notlike(token.replace("*", "%")))
                    else:
                        conditions.append(column != token)
                if conditions:
                    if len(conditions) == 1:
                        filters.append(conditions[0])
                    else:
                        filters.append(and_(*conditions))

        # 统计符合条件记录总数
        total = db.session.query(func.count()).select_from(CiProdStatApiErrors).filter(*filters).scalar()

        # 查询错误信息，按created_at降序排序
        query = db.session.query(CiProdStatApiErrors).filter(*filters) \
            .order_by(CiProdStatApiErrors.created_at.desc()) \
            .offset((page - 1) * page_size).limit(page_size)

        results = []
        for err in query:
            results.append({
                'id': err.id,
                'trace_id': err.trace_id,
                'request_id': err.request_id,
                'user_id': err.user_id,
                'status': err.status,
                'uri': err.uri,
                'host': err.host,
                'env': err.env,
                'err_code': err.err_code,
                'err_msg': err.err_msg,
                'created_at': err.created_at.isoformat()
            })

        return jsonify({
            'total': total,
            'data': results
        })

    except Exception:
        traceback.print_exc()
        abort(500, description="An error occurred while fetching error details.")