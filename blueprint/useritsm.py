#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
@Title:     useritsm.py
@Date:      2022/7/26 16:11
@Author:    wanglh
@Desc：     sso权限相关操作，用户，告警等监控蓝图
"""
import datetime

import timeago
from dateutil.relativedelta import relativedelta
from flask import Blueprint, render_template_string, request
from sqlalchemy import desc, update

from utils import *
from utils.devmanager import setenvreservation
from utils.feishu.message import feishu_robot

userbp = Blueprint("user_blue", __name__, url_prefix="/user/api/v1")
itsmbp = Blueprint("itsm_blue", __name__, url_prefix="/itsm/api/v1")


# @userbp.route('/test/getnamedict', methods=['POST'])
def get_name_dictionary(keyword: str, display: bool = False) -> str:
    user_query = db.session.query(SsousersModel.username, SsousersModel.displayname).filter().all()
    user_query_dict = {line[0]: line[1] for line in user_query}
    if keyword in user_query_dict:
        if display:
            return user_query_dict[keyword]
        return keyword
    return ""


@userbp.route('/test', methods=['GET'])
def ssousertest():
    ak = SsoUserroles()
    roleaccess = ak.userroleaccess()
    username = "wanglinhao"
    select_query = SsousersModel.query.filter_by(username=username).all()
    select_data = SsousersModel.to_all_json(select_query)
    print(select_data)
    return jsonify({"code": 200, "data": roleaccess})


html_str = '''
        <p>查询关键字：{{ client_ip }}，匹配结果数：{{ total }}</p>
        <div style="max-height: 500px;overflow-y:scroll;">
            <table align="center" border="1" cellpadding="2" cellspacing="0" style="table-layout:fixed;" width="100%">
                <tr>
                    <th align="center" width="40">IP</th>
                    <th align="center" width="45">名称</th>
                    <th align="center" width="16">类型</th>
                    <th align="center" width="29">命名空间</th>
                    <th align="center" width="29">所属账号</th>
                    <th align="center" width="24">备注</th>
                </tr>
                {% for i in query %}
                <tr>
                    <td align="center">{{ i.private_ip }}</td>
                    <td align="center">{{ i.name }}</td>
                    <td align="center">{{ i.type }}</td>
                    <td align="center">{{ i.namespace }}</td>
                    <td align="center">{{ i.commit }}</td>
                    <td align="center">{{ i.cluster_name }}</td>
                </tr>
                {% endfor %}
            </table>
        </div>'''


# @userbp.route('/ipsearch')
def getIpsearch():
    """
    ip地址查询
    :return:
    """
    client_ip = request.args.get('ip')
    if client_ip:
        query = db.session.execute("""SELECT distinct private_ip,name,type,namespace,commit,cluster_name
        FROM `ip_list` WHERE
	    (( `private_ip` = '{str}' OR `public_ip` = '{str}' OR `cluster_ip` = '{str}' OR cluster_name LIKE '%{str}%') 
	    OR (( namespace = '{str}' OR `name` LIKE '%{str}%') AND `type` = 'services' ) 
	    OR concat(name,'.',namespace)='{str}' OR (`type` = '{str}'))
	    AND namespace != 'kube-system';""".format(str=client_ip))
        query = query.fetchall()
        results = render_template_string(html_str, query=query, client_ip=client_ip, total=len(query))
        return jsonify({"code": 200, "data": results, 'total': len(query)})
    return jsonify({"code": 400, "msg": "参数不存在"})


@userbp.route('/ipsearch')
def search_ip():
    data_raw = get_response(request)
    ip_list = data_raw.get('ip').split(',')
    ip_list = [i for i in ip_list if i != ""]
    current_app.logger.info(f"参数IP：{ip_list}")
    result = []
    for ip in ip_list:
        dest = into_ip_get_result(ip)
        result = compare_list(result, dest)
    current_app.logger.info(f"结果：{result}")
    result_str = render_template_string(html_str, query=result, client_ip=ip_list, total=len(result))
    return jsonify({"code": 200, "data": result_str, 'total': len(result)})


@userbp.route('/ipsearch/pod')
def search_pod():
    def compare(src: list, dest: list) -> list:
        if len(src) == 0:
            return dest
        save_column = ["type", 'namespace', 'deployment_name', 'cluster_name']
        a = [{column: d[column] for column in save_column} for d in dest]  # 获取dest中的每一项d，并提取d中的private_ip和public_ip，并组成新的列表a
        r = []
        for x in src:
            # x: dict
            src_slice = {column: x[column] for column in save_column}  # 提取x中的private_ip和public_ip，组成新的字典src_slice
            if src_slice in a:  # 判断src_slice是否在a中
                r.append(src_slice)
        return r

    data_raw = get_response(request)
    ip_list = data_raw.get('ip').split(',')
    ip_list = [i for i in ip_list if i != ""]
    current_app.logger.info(f"参数IP：{ip_list}")
    compare_result = []  # 获取多个IP交集的信息
    for ip in ip_list:
        compare_result = compare(compare_result, into_ip_get_result(ip))
    data = []
    print(compare_result)
    # 查询交集的详细信息（deployment的相关ip信息）
    for i in compare_result:
        sql = f"""SELECT * FROM ip_list WHERE type='services' AND name='{i["deployment_name"]}' AND namespace='{i["namespace"]}' and cluster_name = '{i["cluster_name"]}'"""
        fetchall = into_ip_get_result("", sql)
        if len(fetchall) == 0:
            data.append(i)
            continue
        data.extend(fetchall)
    return jsonify({"code": 200, "data": data, 'total': len(data)})


@userbp.route('/getnotices')
def getnotices():
    """
    首页动态窗口信息获取
    :return:
    """
    role = request.args.get('role', 'all')
    user = request.args.get('user', 'none')
    now = datetime.now().strftime('%Y-%m-%d %H:%M:%S')
    sql = '''SELECT info,date FROM `notices_list` where (role="{}" or user="{}") and date > curdate()  ORDER BY date DESC limit 10'''.format(role, user)
    query = db.session.execute(sql)
    query = query.fetchall()
    result = [{"title": i.info, "date": timeago.format(i.date, now, "zh_CN")} for i in query]
    return json.dumps(result, ensure_ascii=False)


@userbp.route('/ssouserlist', methods=['POST'])
def sso_user_list():
    """
    sso权限-获取用户列表 验证通过
    :return:
    """
    # dataraw = json.loads(request.data)
    dataraw = get_response(request)
    pageSize = dataraw['pageSize']
    pageNo = dataraw['pageNo']
    userName = dataraw.get('userName', '')
    Count, data = sso_getlimit(pageSize=pageSize, pageNo=pageNo, userName=userName)
    current_app.logger.info("sso权限信息列表获取成功")
    return jsonify({"code": 200, "msg": "success", "totalCount": Count, "data": data})


@userbp.route('/ssouserupdates', methods=['POST'])
def sso_user_updates():
    """
    sso权限-创建更新用户数据表信息  验证通过
    :return:
    """
    data_raw = get_response(request)
    try:
        sk = SsoUserroles()
        message = sk.main(**data_raw)
        # 写入数据库
        save_to_operation_log(request, action="用户信息变更", result=200)
        return jsonify({"code": 200, "msg": message})
    except Exception as e:
        current_app.logger.error(f"{data_raw['username']}用户权限数据写入失败, 具体报错信息：{e}")
        save_to_operation_log(request, action="用户信息变更", result=500)
        return jsonify({"code": 500, "msg": f"{data_raw['username']}用户权限数据写入失败"})


@userbp.route('/ssoupdate', methods=['POST'])
def sso_rsync():
    """
    SSO权限-用户信息同步更新到sso-user.yml文件 验证通过
    :return:
    """
    username = request.headers.get('X-User')
    client_ip = request.headers.get('X-Real-Ip')
    url = request.headers.get('X-Forwarded-Uri')
    action = "同步账号信息到sso"

    data = execute_job_1000131()
    sql = f"""INSERT INTO operation_log (username, occur_time, client_ip, action, url, result) VALUES ('{username}',NOW(),'{client_ip}','{action}','{url}',200)"""
    try:
        db.session.execute(sql)
        db.session.commit()
        current_app.logger.info(action + ",日志信息写入成功")
        return jsonify({"code": 200, "msg": data})
    except Exception as e:
        current_app.logger.error(f'日志信息数据写入失败,数据库语法异常，具体报错信息：\n{e}')
        return jsonify({'code': 502, 'msg': '日志信息数据写入失败'})


@userbp.route('/getRoles', methods=['GET'])
def get_roles():
    """
    sso权限-获取roles列表 完成
    :return:
    """
    select_role = """SELECT id, IFNULL(NULL, 'role') as type, rolename, role_desc FROM sso_role;"""
    select_access = """SELECT id+10000 AS id, IFNULL(NULL, 'access') as type, access as rolename, description as role_desc FROM sso_access;"""
    column = ('id', 'type', 'rolename', 'role_desc')
    role_list = db.session.execute(select_role).fetchall()
    access_list = db.session.execute(select_access).fetchall()
    role_list = [dict(zip(column, i)) for i in role_list]
    access_list = [dict(zip(column, i)) for i in access_list]
    roles = role_list + access_list
    return jsonify({"code": 200, "msg": "success", "totalCount": len(roles), "data": roles})


@userbp.route('/yearning/update', methods=['POST'])
def yearning_update():
    """
    更新yearning信息
    :return:
    """
    action = "yearning 更新"
    data_raw = get_response(request)
    username = data_raw.get('username', '')
    displayname = data_raw.get('displayname', '')
    email = data_raw.get('email', f'{username}@wegooooo.com')
    print(f"yearning/update参数：{data_raw}")
    if username == '':
        save_to_operation_log(request, action, result=404)
        return jsonify({'code': 404, 'msg': '没有传入用户，取消更新'})
    sql_account = """INSERT INTO yearning.core_accounts ( username, password, rule, department, real_name, email ) SELECT
            '{username}',
            'pbkdf2_sha256$120000$HoQiN5kd6kj1$uZEv5UulEWva8QKIOdV8bNAVn9bzf0S2wkEj08D7teg=',
            'guest',
            '研发',
            '{real_name}',
            '{email}' 
            FROM DUAL 
            WHERE NOT EXISTS (
                SELECT username,password,rule,department,real_name,email 
                FROM yearning.core_accounts 
                WHERE username = '{username}');""".format(username=username, real_name=displayname, email=email)
    sql_graineds = """INSERT INTO yearning.core_graineds ( `username`, `group` ) SELECT '{username}', '["变更申请"]' 
            FROM DUAL  WHERE NOT EXISTS ( SELECT `username`, `group` FROM yearning.core_graineds WHERE username = '{username}' );
            """.format(username=username)

    print(sql_account)
    print(sql_graineds)
    try:
        db.session.execute(sql_account)
        db.session.execute(sql_graineds)
        db.session.commit()
    except Exception as e:
        db.session.rollback()
        save_to_operation_log(request, action, result=500)
        return json.dumps({'code': 500, 'msg': f'写入数据库出错\n {e}'})
    finally:
        db.session.close()
    save_to_operation_log(request, action, result=200)
    return jsonify({'code': 200, 'msg': f'{displayname} 获取yearning权限成功'})


# SLA相关功能
other_dict = {'监控告警': 0, '用户反馈': 1, '其他': 2, 'P0级故障': 0, 'P1级故障': 1, 'P2级故障': 2, '一般事故': 3, "未知": 2, "是": 0, "否": 1}
channels_dict = {'监控告警': 0, '用户反馈': 1, '其他': 2}
levels_dict = {'P0级故障': 0, 'P1级故障': 1, 'P2级故障': 2, '一般事故': 3}
warneds_dict = {"是": 0, "否": 1, '未知': 2}  # 有无告警和有无变更
sla_dict = {0: 1, 1: 0.5, 2: 0.25, 3: 0}

notice_dict = {"企微现网问题群": 1, "飞书中后台群": 2, "微信群": 3}
other_keys = list(other_dict.keys())


@itsmbp.route('/getslaevents', methods=['POST'])
def get_slaevents():
    """
    获取SLA事件信息
    :return:
    """
    data_raw = get_response(request)
    keyword = data_raw.get('keyword', '')
    str_time = get_time_stamp(data_raw.get('str_time', ''))
    end_time = get_time_stamp(data_raw.get('end_time', ''))
    level = data_raw.get('level', '')
    dept_name = data_raw.get('dept_name', '')
    pageIndex = data_raw.get("pageNo", 1)
    pageSize = data_raw.get("pageSize", 10)
    try:
        for key, value in levels_dict.items():
            if key == level:
                level = value
        if str_time and end_time:  # 存在str_time和end_time 是在分析页面查询
            task_filters = {
                and_(
                    db.cast(SlaEvents.occur_time, db.DATE) >= db.cast(str_time, db.DATE),
                    db.cast(SlaEvents.occur_time, db.DATE) < db.cast(end_time, db.DATE),
                    SlaEvents.level.like("%{}%".format(level)),
                    SlaEvents.dept_name.like("%{}%".format(dept_name))
                )
            }
        else:  # 没有str_time和end_time会使用关键字查询
            task_filters = {
                or_(
                    SlaEvents.title.like("%{}%".format(keyword)),
                    SlaEvents.event_id.like("%{}%".format(keyword)),
                    SlaEvents.occur_time.like("%{}%".format(keyword))
                )
            }
        # 分页与排序
        query = db.session.query(SlaEvents).filter(*task_filters).order_by(SlaEvents.occur_time.desc()).limit(
            pageSize).offset((pageIndex - 1) * pageSize).all()
        count = db.session.query(func.count(SlaEvents.id)).filter(*task_filters).scalar()
        query_items = SlaEvents.to_all_json(query)
        level_dict = {v: k for k, v in levels_dict.items()}
        channel_dict = {v: k for k, v in channels_dict.items()}
        influenced_dict = {v: k for k, v in warneds_dict.items()}
        warned_dict = {v: k for k, v in warneds_dict.items()}
        # 获取用户信息
        user_query = SsousersModel.query.filter().all()
        user_data = SsousersModel.to_all_json(user_query)
        user_data = {data['username']: data['displayname'] for data in user_data if data['status'] == 1}
        for data in query_items:
            data['end_time'] = get_time_stamp(data['end_time']) if data.get('end_time', '') else None
            data['channel'] = str(channel_dict[data['channel']]) if data.get('channel') else None
            data['level'] = str(level_dict[data['level']])
            data['influenced'] = str(influenced_dict[data['influenced']])
            data['warned'] = str(warned_dict[data['warned']])
            data['upgraded'] = str(warned_dict[data['upgraded']])
            data['occur_time'] = get_time_stamp(data['occur_time'])
            data['notice'] = data['notice'].split(',') if data.get('notice', '') else []
            data['dept_name'] = data['dept_name'] if data.get('dept_name') else None

            if data.get('owner', ''):
                owners = data.get('owner').split(',')
                owners = [user_data.get(owner) if owner in list(user_data.keys()) else owner for owner in owners]
                data['owner'] = ",".join(owners)
        print(len(query_items))
        current_app.logger.info(f"数据获取成功")
        return jsonify({"code": 200, "msg": "数据获取成功", "data": query_items, "totalCount": count})
    except Exception as e:
        current_app.logger.error(f"数据获取失败，查询存在问题:\n{e}")
        return jsonify({"code": 500, "msg": "数据获取失败"})


@itsmbp.route('/slaevents', methods=['POST'])
def sla_svents():
    """
    写入SLA事件记录
    :return: 
    """
    action = "SLA事件记录"
    insert_data = {}
    data_raw = get_response(request)
    for k, v in data_raw.items():
        if k != "notice":
            if v in list(other_dict.keys()):
                v = other_dict[v]
            insert_data[k] = v
    insert_data['channel'] = data_raw.get('channel', 0)
    insert_data['occur_time'] = get_time_stamp(data_raw.get('occur_time')) if data_raw.get('occur_time', '') else ''
    insert_data['end_time'] = get_time_stamp(data_raw.get('end_time')) if data_raw.get('end_time', '') else ''
    insert_data["event_id"] = data_raw['event_id'] if data_raw.get("event_id", "") else "E" + datetime.now().strftime(
        "%Y%m%d%H%M%S")
    insert_data['notice'] = ",".join(data_raw["notice"]).replace("\n", "").replace(" ", "") if data_raw.get("notice",
                                                                                                            "") else ""
    insert_data['owner'] = data_raw['owner'] if data_raw.get('owner', '') else ""
    insert_data['progress'] = data_raw['progress'] if data_raw.get('progress', '') else ""
    insert_data['dept_name'] = data_raw['dept_name'] if data_raw.get('dept_name') else ""
    insert_data['reportlink'] = data_raw['reportlink'] if data_raw.get('reportlink') else ""
    # 通过传入的负责人，获取dept_name
    try:
        if insert_data['owner'] != "":
            query_sql = """
                SELECT
                    ul.name as owner,
                    dl.dept_name as dept_name
                FROM
                    user_list ul
                    INNER JOIN user_dept ud ON ul.user_code = ud.usercode
                    INNER JOIN dept_list dl ON dl.id = ud.dept_id 
                WHERE
                    user_id LIKE '%{keyword}%' OR name LIKE '%{keyword}%'
                union all
                select '{keyword}' as owner, '{keyword}' as dept_name from user_list where not exists (
                select name from user_list where user_id LIKE '%{keyword}%' OR name LIKE '%{keyword}%'
                ) LIMIT 1""".format(keyword=insert_data['owner'])
            data = acs_select_one(query_sql)
            insert_data.update(data)
    except Exception as e:
        current_app.logger.warning(e)

    if insert_data['end_time'] == '':
        del insert_data['end_time']
        # insert_sql = f"""INSERT INTO sla_events {str(tuple(insert_data.keys())).replace("'", "")} VALUES {tuple(insert_data.values())} ON DUPLICATE KEY UPDATE event_id='{insert_data[
        #     "event_id"]}', title='{insert_data['title']}', occur_time='{insert_data['occur_time']}',
        #     level={insert_data['level']}, influenced={insert_data['influenced']}, warned={insert_data['warned']},
        #     upgraded={insert_data['upgraded']}, progress='{insert_data['progress']}', notice='
        #     {insert_data['notice']}', owner='{insert_data['owner']}', dept_name='{insert_data['dept_name']}', reportlink='{insert_data['reportlink']}';"""
        inserted = insert(SlaEvents).values(**insert_data)
        on_duplicate_key_stmt = inserted.on_duplicate_key_update(**insert_data)
        qw_values = {
            "msgtype": "markdown",
            "markdown": {
                "content": f"## <font color=\"red\">【IT事件通报】 </font>\n"
                           f"> **【当前状态】**：<font color=\"red\">处理中</font>\n"
                           f"> **【事件ID】**：{insert_data['event_id']}\n"
                           f"> **【事件描述】**：{data_raw['title']} \n"
                           f"> **【故障发生时间】**：{insert_data['occur_time']} \n"
                           f"> **【事件等级】**：{data_raw['level']} \n"
                           f"> **【是否影响用户】**：{data_raw['influenced']} \n"
                           f"> **【是否有告警】**：{data_raw['warned']} \n"
                           f"> **【今日是否有变更】**：{data_raw['upgraded']} \n"
                           f"> **【当前进展】**：{data_raw.get('progress', '')} \n"
            }
        }
        feishu_value = f"【IT事件通报】\n【当前状态】：处理中\n【事件ID】：{insert_data['event_id']}\n【事件描述】：{data_raw['title']} " \
                       f"\n【故障发生时间】：{insert_data['occur_time']} \n" \
                       f"【事件等级】：{data_raw['level']} \n【是否影响用户】：{data_raw['influenced']} \n【是否有告警】：{data_raw['warned']} \n" \
                       f"【今日是否有变更】：{data_raw['upgraded']} \n【当前进展】：{data_raw.get('progress', '')}"
    else:
        # 计算故障时间和SLA占比时间
        fault_time = compare_min(datetime.strptime(insert_data['occur_time'], '%Y-%m-%d %H:%M:%S'), datetime.strptime(insert_data['end_time'], '%Y-%m-%d %H:%M:%S'))
        insert_data['fault_time'] = fault_time
        sla_time = fault_time * sla_dict[insert_data['level']]
        insert_data['sla_time'] = int(Decimal(sla_time).quantize(Decimal("1."), rounding="ROUND_HALF_UP"))  # 四舍五入取整
        qw_values = {
            "msgtype": "markdown",
            "markdown": {
                "content": f"## <font color=\"green\">【IT事件通报】</font>\n"
                           f"> **【当前状态】**：<font color=\"green\">已恢复</font>\n"
                           f"> **【事件ID】**：{insert_data['event_id']}\n"
                           f"> **【事件描述】**：{data_raw['title']} \n"
                           f"> **【故障发生时间】**：{insert_data['occur_time']} \n"
                           f"> **【事件等级】**：{data_raw['level']} \n"
                           f"> **【是否影响用户】**：{data_raw['influenced']} \n"
                           f"> **【是否有告警】**：{data_raw['warned']} \n"
                           f"> **【今日是否有变更】**：{data_raw['upgraded']} \n"
                           f"> **【当前进展】**：{data_raw.get('progress', '')} \n"
                           f"> **【故障结束时间】**：{insert_data['end_time']} \n"
            }
        }
        feishu_value = f"【IT事件通报】\n【当前状态】：已恢复\n【事件ID】：{insert_data['event_id']}\n【事件描述】：{data_raw['title']} \n【故障发生时间】：{insert_data['occur_time']} " \
                       f"\n" \
                       f"【事件等级】：{data_raw['level']} \n【是否影响用户】：{data_raw['influenced']} \n【是否有告警】：{data_raw['warned']} \n" \
                       f"【今日是否有变更】：{data_raw['upgraded']} \n【当前进展】：{data_raw.get('progress', '')} \n" \
                       f"【故障结束时间】：{data_raw['end_time']} \n"
        inserted = insert(SlaEvents).values(**insert_data)
        on_duplicate_key_stmt = inserted.on_duplicate_key_update(**insert_data)
        # insert_sql = f"""INSERT INTO sla_events {str(tuple(insert_data.keys())).replace("'", "")} VALUES {tuple(insert_data.values())} ON DUPLICATE KEY UPDATE event_id='{insert_data[
        #     "event_id"]}', title='{insert_data['title']}', occur_time='{insert_data['occur_time']}',
        #     level={insert_data['level']}, influenced={insert_data['influenced']}, warned={insert_data['warned']},
        #     upgraded={insert_data['upgraded']}, progress='{insert_data['progress']}', end_time='
        #     {insert_data['end_time']}', notice='{insert_data['notice']}', owner='{insert_data['owner']}', dept_name='{insert_data['dept_name']}', fault_time={insert_data['fault_time']},
        #     sla_time={insert_data['sla_time']}, reportlink='{insert_data['reportlink']}';"""
        # res = db.engine.execute(upserted)
        # print(res.lastrowid)
        # return res.lastrowid
    # insert_sql = insert_sql.replace("'None',", "null,").replace("None,", "null,")
    # print("插入SQL语句", insert_sql)

    try:
        # 更新写入数据库
        db.engine.execute(on_duplicate_key_stmt)
        # 判断status是否为0，为0则不发送通知
        save_to_operation_log(request, action, result=200)

        def messageSend(insert_data, feishu_value):
            if insert_data['notice']:
                if "企微" in insert_data['notice']:
                    bot_msg(qw_values, bot_name='001_problem')
                if "飞书" in insert_data['notice']:
                    feishu_robot('feishu_bk', feishu_value)
                if "运维" in insert_data['notice']:
                    feishu_robot('feishu_ops', feishu_value)
                if "微信" in insert_data['notice']:
                    pass
                current_app.logger.info(f"{','.join(insert_data['notice'])}通知：发送成功")
            else:
                current_app.logger.info("不发送通知")

        query = SlaEvents.query.filter_by(event_id=insert_data.get('event_id')).first()
        print(query.status)

        if query.status != 0:
            messageSend(insert_data, feishu_value)

        # 最后完成写入对数据库进行更新操作
        update_statement = update(SlaEvents).filter(SlaEvents.end_time.isnot(None)).values(status=0)
        db.session.execute(update_statement)
        db.session.commit()
        return jsonify({"msg": f"事件:{query.event_id}，更新完成"}), 200
    except Exception as e:
        db.session.rollback()
        current_app.logger.error(f"数据库写入出错，具体信息:\n{e}")
        save_to_operation_log(request, action, result=500)
        return jsonify({"code": 500, "msg": f"数据库写入出错，具体信息:\n{e}"})
    finally:
        db.session.close()


@itsmbp.route("/getslaanalyze", methods=['GET'])
def get_sla_analyze():
    """
    获取SLA分析结果
    :return:
    """

    def slakip(time, slatime_sum):  # 获取月数据的sla_kpi
        monthlen = calendar.monthrange(time.year, time.month)[1]
        return '%.2f' % ((monthlen * 24 * 60 - int(str(slatime_sum))) / (monthlen * 24 * 60) * 100)

    data_raw = get_response(request)
    str_time = datetime.strptime(get_time_stamp(data_raw.get('str_time', '')), "%Y-%m-%d")
    end_time = datetime.strptime(get_time_stamp(data_raw.get('end_time', '')), "%Y-%m-%d")

    if str_time and end_time:
        days = (end_time - str_time).days  # 传入的总天数
        task_filter = {
            and_(
                db.cast(SlaEvents.occur_time, db.DATE) >= db.cast(str_time, db.DATE),
                db.cast(SlaEvents.occur_time, db.DATE) < db.cast(end_time, db.DATE)
            )
        }

    else:
        return jsonify({'code': 404, "msg": "没有选择时间"})

    # 指标数据，根据选择时间进行获取
    slatime_sum = db.session.query(func.sum(SlaEvents.sla_time).label("sla_time_sum")).filter(*task_filter).scalar()
    slatime_sum = slatime_sum if slatime_sum else 0
    total_count = db.session.query(func.count(SlaEvents.id).label('total')).filter(*task_filter).scalar()
    remaining_time = days * 24 * 69 * 0.001 - int(slatime_sum)
    sla_kpi = '%.2f' % ((days * 24 * 60 - int(slatime_sum)) / (days * 24 * 60) * 100)
    # 指标详情
    level_query = db.session.query(func.count(SlaEvents.id).label("count"), SlaEvents.level.label("name")).filter(
        *task_filter).group_by(SlaEvents.level).all()
    deptname_query = db.session.query(func.count(SlaEvents.id).label("count"), SlaEvents.dept_name.label(
        'name')).filter(*task_filter).group_by(SlaEvents.dept_name).all()
    level_name = {v: k for k, v in levels_dict.items()}
    eventlist_data = [{"name": level_name[i.name], "value": i.count} for i in level_query]
    linelist_data = [{"name": i.name, "value": i.count} for i in deptname_query if i.name]

    # 折线图数据
    if end_time.month - str_time.month < 6 and end_time.year == str_time.year:
        str_time = end_time - relativedelta(months=+6)

    task_filter_two = {
        and_(
            db.cast(SlaEvents.end_time, db.DATE) >= db.cast(str_time, db.DATE),
            db.cast(SlaEvents.end_time, db.DATE) < db.cast(end_time, db.DATE)
        )
    }

    level_query = db.session.query(func.sum(SlaEvents.sla_time).label('count_time'), func.date_format(
        SlaEvents.end_time, '%Y-%m').label(
        'releaseYearMonth')).filter(*task_filter_two).group_by('releaseYearMonth').all()

    slalist_data = [{"time": i.releaseYearMonth, "slakpi": slakip(datetime.strptime(i.releaseYearMonth, '%Y-%m'),
                                                                  int(str(i.count_time)))} for i in level_query if
                    i.count_time]
    slalist_month_range = [i['time'] for i in slalist_data]
    months_range = get_month_range(datetime.strptime(slalist_month_range[0], '%Y-%m'), datetime.strptime(
        slalist_month_range[-1], '%Y-%m'))
    set_gap = set(months_range) - set(slalist_month_range)
    if len(set_gap) > 0:
        for i in set_gap:
            slalist_data.append({"time": i, "slakpi": '100.00'})

    slalist_data = sorted(slalist_data, key=lambda i: datetime.strptime(i['time'], '%Y-%m'), reverse=False)
    slalist_data = [{i["time"]: i["slakpi"]} for i in slalist_data]

    # 返回函数
    return jsonify({'code': 200, 'eventlist': eventlist_data, 'linelist': linelist_data, 'slalist': slalist_data,
                    'total': total_count,
                    'slatime_sum': int(slatime_sum), 'sla_kpi': sla_kpi, 'remaining_time': remaining_time})


@itsmbp.route('/getslaeventreport', methods=["GET"])
def get_slaeventreport():
    """
    SLA-事件详情查询
    :return:
    """
    data_raw = get_response(request)
    keyword = data_raw.get('keyword', '')
    pageIndex = data_raw.get("pageNo", 1)
    pageSize = data_raw.get("pageSize", 10)
    try:
        task_filter = {
            or_(
                SlaEventReport.author.like('%{}%'.format(keyword))
            )
        }
        query = db.session.query(SlaEventReport).filter(*task_filter).group_by(SlaEventReport.author).limit(
            pageSize).offset(
            (pageIndex - 1) * pageSize).all()
        count = db.session.query(func.count(SlaEventReport.id)).filter(*task_filter).scalar()
        query_items = SlaEventReport.to_all_json(query)
        current_app.logger.info(f'数据获取成功')
        return jsonify({'code': 200, 'msg': '数据获取成功', 'data': query_items, "totalCount": count})
    except Exception as e:
        current_app.logger.error(f'Exception：数据获取失败，具体信息：\n{e}')
        return jsonify({'code': 500, 'msg': f'数据获取失败，具体信息：\n{e}'})


@itsmbp.route('/slaeventsreport', methods=['POST'])
def sla_events_report():
    """
    SLA-事件详情写入
    :return:
    """
    action = "SLA事件详情"
    data_raw = get_response(request)
    if data_raw.get("event_id", "") == "":
        event_id = "E" + datetime.now().strftime("%Y%m%d%H%M%S")
    try:
        insert_sql = f"""INSERT INTO sla_event_report {str(tuple(data_raw.keys())).replace("'", "")} VALUES {tuple(data_raw.values())}"""
        db.session.execute(insert_sql)
        db.session.commit()
        db.session.close()
        save_to_operation_log(request, action, result=200)
        return jsonify({'code': 200, 'msg': "写入成功！"})
    except Exception as e:
        current_app.logger.error(f"SLA事件详情：记录失败，写入表：sla_event_report；具体错误：{e}")
        save_to_operation_log(request, action, result=500)
        return jsonify({'code': 500, 'msg': '写入失败！'})


@userbp.route('/openvpn/signissue', methods=['POST'])
def openvpn_sign_adn_issue():
    """
    OPENVPN证书：签发
    :param request:
    :request username: 签名证书ID（用户名）
    :request creat_by: 创建人（create,通过登录的X-User获取）
    :return:
    """
    action = "OPENVPN-签发"
    data_raw = get_response(request)
    username = data_raw.get('username')
    create_by = request.headers.get('X-User')
    query = SsousersModel.query.filter_by(username=username).first()
    if query:
        user_email = query.email
        # 执行蓝鲸任务
        result = execute_job_1000183(**{"username": username, "create_by": create_by, "email": user_email})
        save_to_operation_log(request, action, result=200)
        return jsonify({"code": 200, "data": result})
        # return jsonify({"code": 200, "data": {"username": username, "user_email": user_email, "create_by": create_by}})
    save_to_operation_log(request, action, result=404)
    current_app.logger.info(f"OPENVPN签发失败，没有查询到用户:{username}")
    return jsonify({"code": 404, "msg": "没有查询到用户"})


@userbp.route('/apiclient', methods=['GET'])
def t_a_apiclient():
    """
    tencent ali API client
    ---
    tags:
      - users
    """
    data_raw = get_response(request)
    return jsonify(
        {'data': [
            {'aliyun': {'client_count': 1233, '成功率': '90.22%'}},
            {'tencent': {'client_count': 222, '成功率': '99.22%'}}
        ],
            'msg': '查询完成'}
    ), 200


@userbp.route('/setEnvReservation', methods=['GET'])
def post_reservation():
    # print(request.headers)
    action = "环境预定添加"
    url = request.headers.get('X-Forwarded-Uri')
    client_ip = request.headers.get('X-Real-Ip')
    data, user = setenvreservation.request_parse(request)
    num, info = setenvreservation.parse_value(data, user)
    sql = f"""INSERT INTO wego_ops.operation_log (username, occur_time, client_ip, action, url, parameters, result, method) VALUES ('{user}',NOW(),'{client_ip}','{action}','{url}',"{info}",200,'GET')"""
    # print(sql)
    db.session.execute(sql)
    db.session.commit()
    if int(num) == 0:
        setenvreservation.Sendto(info, int(num))
        return request.method, 200
    else:
        return "错误", 500


@userbp.route('/ip2city', methods=['POST'])
def ip2city():
    try:

        data = request.data.decode('utf-8')
        url = "https://ops.szwego.com/user/api/v1/ip2city"
        response = requests.post(url, data=data)
        return jsonify(response.json()), 200
    except Exception as e:
        return jsonify({'data': str(e), 'msg': '查询失败'}), 500

# @userbp.route('/getOfficeIp')
# def getOfficeIp():
#     try:
#         data = request.data.decode('utf-8')
#         url = "https://ops.szwego.com/user/api/v1/getOfficeIp"
#         response = requests.post(url, data=data)
#         return jsonify(response.json()), 200
#     except Exception as e:
#         return jsonify({'data': str(e), 'msg': '查询失败'}), 500

@userbp.route('/decodealbum', methods=['POST'])
def decode_album():
    try:
        data = request.data.decode('utf-8')
        print(data)
        url = "https://ops.szwego.com/user/api/v1/decodealbum"
        response = requests.post(url, data=data)
        return jsonify(response.json()), 200
    except Exception as e:
        return jsonify({'data': str(e), 'msg': '查询失败'}), 500


@itsmbp.route("/weekly/cost/report")
def get_weekly_cost_report():
    try:
        params = get_response(request)
        start_time, end_time = params.get('start_time'), params.get('end_time')
        if start_time and end_time:
            query = WeeklyCostReport.query.filter(
                WeeklyCostReport.start_date >= start_time,
            ).order_by(desc(WeeklyCostReport.week)).all()
            data = WeeklyCostReport.to_all_json(query)  # 所有数据
            tableData(data)
        else:
            query = WeeklyCostReport.query.order_by(desc(WeeklyCostReport.year), desc(WeeklyCostReport.week)).all()
            data = WeeklyCostReport.to_all_json(query)[:7]  # 前 7 条数据
            tableData(data)
        owner_cost_all = []
        if len(data) <= 0:
            return jsonify({"msg": "数据为空"}), 200
        for line in data:
            owner_dict = {"weekly": line.get('week'), "year": line.get('year')}
            for owner in ["qiniu", "tx", "ali"]:
                cost_all = 0
                for key in line.keys():
                    if owner in key and 'cost' in key:
                        value = line[key]
                        if isinstance(value, (int, float, str)):
                            try:
                                cost_all += value
                            except (ValueError, TypeError):
                                pass
                owner_dict[owner] = cost_all
            owner_cost_all.append(owner_dict)
        owner_cost_all = sorted(owner_cost_all, key=lambda item: (item['year'], item['weekly']), reverse=True)
        if start_time and end_time:
            return jsonify({'data': data,
                            'owner_cost': owner_cost_all[0],
                            "owner_cost_all": owner_cost_all}), 200
        return jsonify({'data': data,
                        'weekly': owner_cost_all[0].get('weekly'),
                        'owner_cost': owner_cost_all[0],
                        "owner_cost_all": owner_cost_all}), 200
    except Exception as e:
        return jsonify({"msg": e}), 500


def get_number_of_weeks(year):
    dec31 = datetime(year, 12, 31)
    return dec31.isocalendar()[1]


def get_previous_week(year, week):
    from datetime import timedelta
    # week num for Monday
    date = datetime.strptime(f'{year} {week} 1', '%Y %W %w')
    # get previous_week date
    previous_week_date = date - timedelta(weeks=1)
    # get year and week
    previous_week_year = previous_week_date.year
    previous_week = previous_week_date.isocalendar()[1]
    if previous_week > week:
        # get previous year week
        num_weeks_last_year = get_number_of_weeks(year - 1)
        return year - 1, num_weeks_last_year
    return previous_week_year, previous_week


def tableData(datas):
    DEFAULT_TOP_NUM = "nano"
    data_dict = {(data['year'], data['week']): data for data in datas}
    # print(data_dict)

    for data in datas:
        year, week = data['year'], data['week']
        prev_year, prev_week = get_previous_week(year, week)
        # print(prev_week, prev_year)

        prev_week_data = data_dict.get((prev_year, prev_week))

        curr_url_stat = data['url_stat']
        curr_userupload_stat = data['userupload_stat']

        for curr_url in curr_url_stat:
            prev_url = next((prev_url for prev_url in (prev_week_data['url_stat'] if prev_week_data else []) if prev_url['url'] == curr_url['url']), None)
            curr_url['topNum'] = prev_url['topRank'] - curr_url['topRank'] if prev_url else DEFAULT_TOP_NUM

        for albumId in curr_userupload_stat:
            prev_albumId = next((prev_a for prev_a in (prev_week_data['userupload_stat'] if prev_week_data else []) if prev_a['albumId'] == albumId['albumId']), None)
            albumId['topNum'] = prev_albumId['topRank'] - albumId['topRank'] if prev_albumId else DEFAULT_TOP_NUM
