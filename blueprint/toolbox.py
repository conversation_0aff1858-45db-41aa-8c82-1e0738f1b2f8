#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
@Title:   toolbox.py
@Date:    2022/6/23 10:09
@Author:  Wanglh
@Desc:    工具箱页面接口
"""
from pathlib import Path

import paramiko
from flask import Blueprint, request

from utils import *
from utils import get_response
from utils.apolloapi import getApps
# from utils.mysql import MysqlAcs
from utils.nacosapi import getTenantInfo

toolbox = Blueprint("toolbox", __name__, url_prefix="/toolbox/api/v1")
wechat_msg_bp = Blueprint('wechat_message', __name__, url_prefix="/wechat/api/v1/message")


def command_run(command, hostname, username, key_filepath):
    """
    通过 SSH 使用密钥执行远程命令
    """

    private_key = paramiko.RSAKey.from_private_key_file(key_filepath)
    ssh_client = paramiko.SSHClient()
    ssh_client.set_missing_host_key_policy(paramiko.AutoAddPolicy())

    try:
        ssh_client.connect(hostname, username=username, pkey=private_key)
        stdin, stdout, stderr = ssh_client.exec_command(command)
        stdout, stderr = stdout.read().decode('utf-8'), stderr.read().decode('utf-8')

        if not stderr:
            current_app.logger.info(f"执行命令：{command}")
            return stdout, stderr
        else:
            current_app.logger.error(f"执行命令: {stderr}")
            return "", f"Error: {stderr}"
    except Exception as e:
        return "", str(e)
    finally:
        ssh_client.close()


@toolbox.route("/client/jenkins", methods=['POST'])
def client_jenkins():
    data_raw = get_response(request)
    env_ = data_raw.get("env", "")
    if env_ == "":
        return jsonify({"msg": "没有选择环境，请选择环境重试"})
    if env_ == 'local':
        # dir_path = "/usr/local/maven/repo/com/wego"
        dir_paths = ["/usr/local/maven/repo/com/wego", "/usr/local/maven/repo/com/szwego"]
    else:
        # dir_path = f"/data/maven/mvn-{env_}/repo/com/wego"
        dir_paths = [f"/data/maven/mvn-{env_}/repo/com/wego", f"/data/maven/mvn-{env_}/repo/com/szwego"]
    services = []
    for dir_path in dir_paths:
        command = f"ls {dir_path}"
        hostname = "*********"
        username = "root"
        current_directory = Path(__file__).resolve().parent
        key_filepath = current_directory / '..' / 'ssh' / 'xc_root_key'
        output, error = command_run(command, hostname, username, key_filepath)
        if error:
            return jsonify({"msg": f"获取失败, {error}"})
        else:
            services.extend([{"value": line} for line in output.split('\n') if line != ""])
    services = list(set(tuple(service.items()) for service in services))
    services = [dict(service) for service in services]
    services.sort(key=lambda x: x['value'])
    return jsonify({"data": services})


@toolbox.route('/mavenclean', methods=['POST'])
def maven_clean():
    """
    maven文件夹清理
    :return:
    """
    action = "maven文件清理"
    dataraw = get_response(request)
    dataraw["username"] = request.headers.get('X-User')
    data = execute_job_1000169(**dataraw)
    current_app.logger.info("蓝鲸作业ID：1000169，调用结束")
    save_to_operation_log(request, action, result=200)
    return jsonify({'code': 200, 'data': data})


@toolbox.route('/releasetableDown', methods=['POST'])
def release_table_down():
    data_raw = get_response(request)
    return jsonify({"code": 200, 'msg': data_raw})


@wechat_msg_bp.route("/send/alert", methods=["POST"])
def wechat_send_alert() -> jsonify:
    """
    告警消息发送
    Args
        content (json): 消息文本
    Returns
        jsonify
    """
    data_raw = request.get_json()
    res = bot_msg(data_raw, "group_ops")
    if res == 200:
        return jsonify({'code': 200, 'msg': '发送成功'}), 200
    return jsonify({'code': 13000991, 'msg': '参数不正确'}), 400


@toolbox.route('/aesencrypt')
def aes_encrypt():
    try:
        data = get_response(request)
        org_str = data.get("value")
        if org_str == "" or bool(org_str) is False:
            return jsonify({"msg": "空字符串跳过"}), 404
        key = current_app.config.get('AES_KEY')
        aes = AES.new(cut_value(key), AES.MODE_ECB)
        # aes加密
        encrypt_aes = aes.encrypt(cut_value(org_str))
        encryptd_text = str(base64.encodebytes(encrypt_aes), encoding='utf-8')
        encryptd_text = encryptd_text.strip()
        return jsonify({"msg": "加解密", "encrypt_str": encryptd_text}), 200
    except Exception as e:
        return jsonify({"msg": e}), 500


@toolbox.route('/type/service', methods=['POST'])
def cnf_type_service():
    try:
        data = get_response(request)
        type_str = data.get('queryString')
        if type_str == "" or bool(type_str) is False:
            return jsonify({"msg": "空字符串跳过"}), 404
        if type_str == "nacos":
            tenantIds = getTenantInfo()
            typeServices = [tenantid['tenant_name'] for tenantid in tenantIds]
            return jsonify({"typeServices": typeServices}), 200
        else:
            appids = getApps()
            return jsonify({"typeServices": appids}), 200
    except Exception as e:
        return jsonify({"msg": f"{e}"}), 500


@toolbox.route("/save/exclude/conf", methods=["POST"])
def save_exclude_conf():
    data = get_response(request)
    if all(data.get(key) for key in data.keys()):
        # file 兼容处理
        file = re.sub(r'[，：\s]', ',', data.get('file'))
        execute_job_1000215(**{"file": file, "type": data.get("type"), "service": data.get("service")})
        return jsonify({"msg": "数据写入完成"}), 200
    return jsonify({"msg": "数据写入失败,存在空参数"}), 404


def get_header():
    import time
    token = "4b457c44-b16a-11eb-bdbf-1a86975cd1ce"
    timestamp = str(time.time())[:10]
    ori_str = timestamp + token
    signature = hashlib.md5(ori_str.encode(encoding='utf-8')).hexdigest()
    header = dict(signature=signature, timestamp=timestamp, appname="devtest", username="admin")
    return header


def callhook_run(ticket_id, msg="", result="true"):
    url = "http://loonflow.default.devops.szwego.com/api/v1.0/tickets/{}/hook_call_back".format(ticket_id)
    header = get_header()
    params = {
        "result": result,
        "msg": str(msg)
    }
    current_app.logger.info(f"工单回调接口，header: {header}")
    req = requests.post(url=url, headers=header, json=params, timeout=120)
    call_backresult = req.json()
    return call_backresult


@toolbox.route("/restart/album", methods=["POST"])
def restart_album():
    try:
        data_raw = get_response(request)
        current_app.logger.info(data_raw)
        bk = execute_job_1000221(**data_raw)
        result = bk.get("result","success")
        msg=f"调度状态：{result}, 工单{data_raw[id]}执行中...."
        current_app.logger.info(msg)
        save_to_operation_log(request, action="蓝鲸-重启album", result=200)
        callhook_run(data_raw["id"], msg=msg, result='true')
        return jsonify({"msg": msg}), 200
    except Exception as e:
        save_to_operation_log(request, action="蓝鲸-重启album", result=500)
        callhook_run(data_raw["id"], msg=str(e), result='false')
        return jsonify({"msg": str(e)}), 500


"""
@toolbox.route("/get/mindoc/list", methods=['GET'])
def get_mindoc_list():
    data_raw = get_response(request)
    # 我需要连接数据库***********:3306/mrdoc,并获取表app_doc_doc下的所有数据
    mrdoc_conn = MysqlAcs(host="***********", user="mrdoc", password="GOLs2BBYBBvWUQ", db="mrdoc")
    mrdoc_cursor = mrdoc_conn.select(f"SELECT id,name,pre_content, top_doc FROM app_doc_doc WHERE status=1")
    mrdoc_conn.__disconnect__()
    return jsonify({"code": 200, 'data': mrdoc_cursor})
"""


@toolbox.route("/generate/string")
def generate_random_string():
    length = int(request.args.get("length"))
    letters_and_digits = string.ascii_letters + string.digits
    res_result = ''.join(random.choice(letters_and_digits) for i in range(length))
    return jsonify({"data": res_result}), 200


@toolbox.route("/reload/mongodb/node", methods=['POST'])
def reload_mongodb_node():
    user = request.headers.get('X-User')
    if user not in ["dongxin", "lijiana", "wanglinhao", "wuxuan", "zhangzhenhuan"]:
        return jsonify({"msg": "权限不够，请联系管理员进行重启"}), 403
    data_raw = get_response(request)
    node_params = data_raw.get('nodeParams')
    resDict = execute_job_1000238(**{"nodeParams": node_params})
    if resDict.get("result") is True:
        msg = f"{node_params} 重启完成"
        return jsonify({"msg": msg}), 200
    else:
        msg = f"{node_params} 重启失败"
        return jsonify({"msg": msg}), 500
