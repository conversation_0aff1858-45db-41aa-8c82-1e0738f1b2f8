#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
@Title:     alarm.py
@Date:      2022/9/8 16:03
@Author:    wanglh
@Desc：     业务监控数据视图函数
"""

import inspect
from datetime import timedelta
from typing import List

from flask import Blueprint, request, url_for
from flask.views import MethodView
from impala.dbapi import connect
from sqlalchemy.exc import IntegrityError

from utils import *
from utils.customModelLib import CustomAlert, MqTopicAlert, PinPointAlert
from utils.feishu.message import feishu_robot_v2
from .cicd import get_page_keyword
from .moniterCenter.alerts_processor import customer_alert_for_alarm_subscription, customer_alert_for_send_webhook_general
from .useritsm import get_name_dictionary

monitor_bp = Blueprint("monitor_alert_blue", __name__, url_prefix="/monitoring/api/v1")
monitor_bp_v2 = Blueprint(
    "monitor_alert_blue_v2", __name__, url_prefix="/monitoring/api/v2"
)

# 告警相关实参
service_level_args = {0: "核心业务流程", 1: "重要业务", 2: "一般业务", 3: "边缘业务"}
status_args = {0: "未启用", 1: "启用"}
monitor_type_args = {0: "数据", 1: "拨测", 2: "日志"}
alarm_type_args = {0: "alarmmanager", 1: "grafana", 2: "script"}


# 告警事件项信息列表
@monitor_bp.route("/alarmhistory/update", methods=["POST"])
def alarmhistory_update():
    """
    告警历史记录
    :param query: alertmanager告警数据信息
    :return:
    """
    data_raw = get_response(request)
    create_time = datetime.now()
    value_data = []
    type = 0 if data_raw["status"] == "firing" else 1
    try:
        for alert in data_raw["alerts"]:
            title = alert["labels"]["alertname"]
            sql = """SELECT id,title from wego_ops.mon_service_alarm where title = '{}'""".format(
                title
            )
            query = db.session.execute(sql)
            data = query.fetchall()
            message = alert["annotations"]["description"]
            value_data.append(
                {
                    "alarm_id": data[0][0],
                    "type": type,
                    "create_time": create_time,
                    "message": message,
                }
            )

        db.session.execute(MonServicealarmHistory.__table__.insert(), value_data)
        db.session.commit()
        save_to_operation_log(request, action="告警历史更新", result=200)
        return "写入MonServicealarmHistory数据表成功！"
    except Exception as e:
        db.session.rollback()
        current_app.logger.error(f"Exception:{str(e)}")
        save_to_operation_log(request, action="告警历史更新", result=500)
        return str(e)
    finally:
        db.session.close()


@monitor_bp.route("/alarmhistory/query")
def alarmhistory_query():
    """
    告警历史查询
    :return:
    """
    str_time = datetime.now().strftime("%Y-%m-%d 00:00:00")
    end_time = (datetime.now() + timedelta(days=1)).strftime("%Y-%m-%d 00:00:00")
    task_filter = {
        and_(
            db.cast(MonServicealarmHistory.create_time, db.DATE)
            >= db.cast(str_time, db.DATE),
            db.cast(MonServicealarmHistory.create_time, db.DATE)
            < db.cast(end_time, db.DATE),
        )
    }
    try:
        query = MonServicealarmHistory.query.filter(*task_filter).all()
        query_data = MonServicealarmHistory.to_all_json(query)
        for line in query_data:
            line["create_time"] = get_time_stamp(line["create_time"])
        totalCount = (
            db.session.query(func.count(MonServicealarmHistory.id).label("total"))
            .filter(*task_filter)
            .scalar()
        )
        return jsonify({"code": 200, "data": query_data, "total": totalCount})
    except Exception as e:
        current_app.logger.error(f"Exception:{str(e)}")
        return jsonify({"code": 500, "msg": str(e)})


@monitor_bp.route("/alarm/query", methods=["POST"])
def alarm_query():
    """
    告警配置管理查询
    :return:
    """
    data_raw = get_response(request)
    pageIndex = data_raw.get("pageNo", 1)
    pageSize = data_raw.get("pageSize", 10)
    keyword = data_raw.get("keyword", "")

    task_filter = {
        or_(
            MonServiceAlarm.title.like("%{}%".format(keyword)),
            MonServiceAlarm.service_scenario.like("%{}%".format(keyword)),
        )
    }
    try:
        query = (
            db.session.query(MonServiceAlarm)
            .filter(*task_filter)
            .order_by(MonServiceAlarm.id.asc())
            .limit(pageSize)
            .offset((pageIndex - 1) * pageSize)
            .all()
        )
        data = MonServiceAlarm.to_all_json(query)
        total_count = (
            db.session.query(func.count(MonServiceAlarm.id).label("total"))
            .filter(*task_filter)
            .scalar()
        )
        for line in data:
            line["create_time"] = get_time_stamp(line["create_time"])
            line["update_time"] = get_time_stamp(line["update_time"])
            line["alarm_type"] = alarm_type_args[line["alarm_type"]]
            line["monitor_type"] = monitor_type_args[line["monitor_type"]]
            line["serice_level"] = service_level_args[line["serice_level"]]
            line["status"] = status_args[line["status"]]
        return jsonify(
            {"code": 200, "data": data, "totalCount": total_count, "msg": "查询完成"}
        )
    except Exception as e:
        current_app.logger.error(f"Exception:{str(e)}")
        return jsonify({"code": 500, "msg": str(e)})


@monitor_bp.route("/alarm/update", methods=["POST"])
def alarm_update():
    """
    告警配置更新
    :param query: 类别配置信息
    :return:
    """
    data_raw = get_response(request)
    # 设置只允许修改：status, interval, 所有的value
    threshold_list = [
        i for i in dir(MonServiceAlarm) if "threshold" in i and "value" in i
    ]
    update_dir = {"status": data_raw["status"], "interval": data_raw["interval"]}
    threshold_dir = {name: data_raw[name] for name in threshold_list if data_raw[name]}
    update_dir = dict(update_dir, **threshold_dir)
    current_app.logger.info(f"告警配置更新数据：{update_dir}")
    try:
        MonServiceAlarm.query.filter_by(title=data_raw["title"]).update(update_dir)
        db.session.commit()
        save_to_operation_log(request, action="告警配置更新", result=200)
        return jsonify({"code": 200, "msg": "更新写入完成"})
    except Exception as e:
        current_app.logger.error(f"Exception:{str(e)}")
        db.session.rollback()
        save_to_operation_log(request, action="告警配置更新", result=500)
        return jsonify({"code": 500, "msg": str(e)})
    finally:
        db.session.close()


@monitor_bp.route("/alarm/add", methods=["POST"])
def alarm_add():
    """
    告警配置新增
    :param query: 类别配置信息
    :return:
    """
    try:
        data_raw = get_response(request)
        del data_raw["update_time"]

        with db.engine.begin() as connection:
            insert_stmt = insert(MonServiceAlarm).values(**data_raw)
            on_duplicate_key_stmt = insert_stmt.on_duplicate_key_update(**data_raw)
            connection.execute(on_duplicate_key_stmt)
        return jsonify({"msg": "The update write is complete"}), 200
    except IntegrityError as e:
        db.session.rollback()
        return jsonify({"msg": f"Duplicate key error: {e}"}), 400

    except Exception as e:
        db.session.rollback()
        return jsonify({"msg": str(e)}), 500

    finally:
        db.session.close()


@monitor_bp.route("/datasource/registration/query", methods=["POST"])
def datasource_registration_query():
    """
    业务监控-数据源类型查询
    :return:
    """
    params_ages = get_response(request)
    keyword = params_ages.get("keyword", "")
    query = MonDatasourceRegistration.query.filter(
        MonDatasourceRegistration.type.like("%{}%".format(keyword))
    ).all()
    try:
        data = MonDatasourceRegistration.to_all_json(query)
        return jsonify({"code": 200, "data": data})
    except Exception as e:
        return jsonify({"code": 500, "msg": str(e)})


@monitor_bp.route("/datasource/registration/update", methods=["POST"])
def datasource_registration_add():
    """
    业务监控-数据源类型更新
    :return:
    """
    params_ages = get_response(request)
    insert_stmt = insert(MonDatasourceRegistration).values(**params_ages)
    on_duplicate_key_stmt = insert_stmt.on_duplicate_key_update(**params_ages)
    action = "业务监控-数据源类型更新"
    try:
        db.engine.execute(on_duplicate_key_stmt)
        save_to_operation_log(request, action=action, result=200)
        return jsonify({"code": 200, "msg": "更新写入完成"})
    except Exception as e:
        db.session.rollback()
        save_to_operation_log(request, action=action, result=500)
        return jsonify({"code": 500, "msg": str(e)})
    finally:
        db.session.close()


@monitor_bp.route("/business/mobile_phone_h5", methods=["POST"])
def moniter_business():
    """
    Prometheus mobile_phone_h5 自定义监控
    :return:
        code: 接口查询状态码
        data: 数据信息
    """
    data_raw = get_response(request)
    moniter_title = data_raw.get("keyword")
    action = "业务监控-phone-h5"
    try:
        query = MonServiceAlarm.query.filter(
            MonServiceAlarm.title.like(f"%{moniter_title}%")
        ).first()
        if int(query.status) == 1:  # 1: 启动， 0：未启用
            seconds = int(int(query.interval) * 60)
            now = (datetime.now() - timedelta(seconds=seconds)).strftime(
                "%Y-%m-%d %H:%M:%S"
            )
            conn = connect(
                host="***********",
                port=21050,
                database="rawdata",
                auth_mechanism="NOSASL",
            )
            cursor = conn.cursor()
            cursor.execute("use rawdata")
            business_sql = query.alarm_sql.format(now)
            current_app.logger.info(business_sql)
            cursor.execute(business_sql)
            data = cursor.fetchall()
            cursor.close()
            conn.close()

            column_name = ("Android_num", "iOS_num", "Error_Android", "Error_iOS")
            datas = [dict(zip(column_name, line)) for line in data]

            current_app.logger.info(f"获取的数据信息：{datas}")
            str_list = [
                "# HELP business_system_info Metric read from /data/prometheus/node_exporter/key/business.prom",
                "# TYPE business_system_info untyped",
            ]
            for line in datas:
                str_line = """
                business_system_info[system="Android"] {Android_num}
                business_system_info[system="iOS"] {iOS_num}
                service_alarm_threshold[type="threshold_value",system="Android"] {Error_Android}
                service_alarm_threshold[type="threshold_value",system="iOS"] {Error_iOS}\n""".format(
                    **line
                )
                str_line = str_line.replace("[", "{").replace("]", "}")
                str_list.append(inspect.cleandoc(str_line))
            datainfo = "\n".join(str_list)
            save_to_operation_log(request, action=action, result=200)
            return jsonify(
                {
                    "code": 200,
                    "data": datainfo,
                    "alert_status": query.status,
                    "title": query.title,
                }
            )
        save_to_operation_log(request, action=action, result=402)
        return jsonify(
            {
                "code": 402,
                "msg": "服务停止监控",
                "alert_status": query.status,
                "title": query.title,
            }
        )
    except Exception as e:
        current_app.logger.error(f"Exception:{str(e)}")
        save_to_operation_log(request, action=action, result=500)
        return jsonify({"code": 500, "msg": str(e)})


@monitor_bp.route("/webchat/selfhealing", methods=["POST"])
def webchat_token_self_healing():
    """
    webchat程序自愈接口
    """
    LOCAL_HOST = current_app.config.get("LOCAL_HOST")
    LOCAL_PORT = current_app.config.get("LOCAL_PORT")
    healing_url = "http://album.prod.cluster.szwego.com/lookbook-server/rpc/miniapp/ewmp/compensateEwmpToken"
    fs_msg_url = f"http://{LOCAL_HOST}:{LOCAL_PORT}/feishu/api/v1/message/send/alert"
    try:
        content = {
            "text": "<b>【%s】</b> \n"
                    "【详情】：%s"
                    % ("Wechat-Token 自愈功能", "检测Wechat-Token过期，重新获取中")
        }
        response = requests.request(
            "POST",
            url=fs_msg_url,
            data=json.dumps({"content": content}, ensure_ascii=False),
            headers={"Content-Type": "application/json"},
        )
        current_app.logger.info(response.text)
    except Exception as e:
        current_app.logger.error(f"飞书消息发送异常: {e}")
    response = requests.request("GET", healing_url)
    if response.status_code == 200:
        content = {
            "text": "<b>【%s】</b> \n"
                    "【详情】：%s" % ("Wechat-Token 自愈功能启动", "Wechat-Token获取成功")
        }
        res = requests.request(
            "POST",
            url=fs_msg_url,
            data=json.dumps({"content": content}, ensure_ascii=False),
            headers={"Content-Type": "application/json"},
        )
        return res.text
    return jsonify({"code": 130999, "msg": "Wechat-Token启动失败"}), 404

@monitor_bp.route("/alarm/subscription", methods=["POST"])
def alarm_subscription():
    data_raw = get_response(request)
    alertMethod = data_raw.get("type").split(",")
    alertRecipient = data_raw.get("recipient")
    alertTitle = data_raw.get("title")
    alertService = data_raw.get("service", "")
    alertContent = data_raw.get("content")
    user_name_list = alertRecipient.split(",")

    if isinstance(alertTitle, list):
        alertTitle = ",".join(map(str, alertTitle))
    elif isinstance(alertTitle, dict):
        import json

        alertTitle = json.dumps(alertTitle)
    elif not isinstance(alertTitle, str):
        alertTitle = str(alertTitle)

    if len(alertMethod) == 0:
        current_app.logger.info("告警失败，没有选择告警方式")
        return jsonify({"msg": "告警失败，没有选择告警方式", "type": "failed"}), 404

    # 接收人查询
    try:
        if "all" not in user_name_list:
            task_filter = {
                and_(
                    SsousersModel.status == 1,
                    or_(
                        SsousersModel.username.in_(user_name_list),
                        SsousersModel.displayname.in_(user_name_list),
                    ),
                )
            }
            userResult = (
                FeishuUserData.query.join(
                    SsousersModel,
                    or_(
                        SsousersModel.email == FeishuUserData.email,
                        SsousersModel.displayname == FeishuUserData.name,
                    ),
                )
                .filter(*task_filter)
                .with_entities(
                    FeishuUserData.user_id,
                    FeishuUserData.open_id,
                    FeishuUserData.name,
                    FeishuUserData.mobile,
                    SsousersModel.username,
                )
                .all()
            )
            userOpenIds = [userRes.open_id for userRes in userResult]
            usernames = [userRes.username for userRes in userResult]
        else:
            userOpenIds = ["all"]
            usernames = ["@all"]
        alertTime = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        msg = {}
        for method in alertMethod:
            if method == "1":  # 飞书
                chat_name = "告警中心"
                alertName = " ".join(
                    [f"<at id={userid}></at>" for userid in userOpenIds]
                )

                # 消息模板
                template_variable = {
                    "alertTitle": alertTitle,
                    "alertService": alertService,
                    "alertTime": alertTime,
                    "alertName": alertName,
                    "alertContent": alertContent,
                }
                current_app.logger.info(f"告警消息模板参数: {template_variable}")
                current_app.logger.info(f"告警方式：飞书，告警接收人：{alertRecipient}")
                current_app.logger.info(f"告警方式：飞书，告警通知群：{chat_name}")
                fs_send_url = url_for("fs_message.send_release", _external=True)
                response = current_app.test_client().post(
                    fs_send_url,
                    json={
                        "chat_name": chat_name,
                        "name": alertRecipient,
                        "template_variable": template_variable,
                        "card_id": "alarmCenter",
                    },
                )
                if response.status_code == 200:
                    current_app.logger.info("告警方式：飞书，告警成功")
                    msg["feishu"] = "success"
                else:
                    current_app.logger.info("告警方式：飞书，告警失败")
                    msg["feishu"] = "failed"
            elif method == "2":  # 企微
                # 执行企微告警逻辑
                touser = "|".join(usernames)
                values = {
                    "touser": touser,
                    "toparty": "",
                    "msgtype": "markdown",
                    "agentid": "1000018",
                    "markdown": {
                        "content": "### %s \n"
                                   ">告警时间：%s \n"
                                   '>告警服务：<font color="warning"> %s </font> \n'
                                   '>告警内容：<font color="comment"> 🟥 %s </font> \n'
                                   % (alertTitle, alertTime, alertService, alertContent)
                    },
                }
                res = gra_msg(values)
                current_app.logger.info("告警方式：企微，告警成功")
                msg["wechat"] = res
            elif method == "3":  # 电话
                alertContent = alertService + alertContent
                templatemode = [alertTitle, alertContent]
                iPhone = list(set([uRes.mobile for uRes in userResult]))
                res = phone_msg(iPhone, templatemode)
                current_app.logger.info("告警方式：手机，告警成功")
                msg["phone"] = res
        current_app.logger.info(msg)

        # 保存记录到mon_center_alert
        try:
            payload = {
                "alertTitle": alertTitle,
                "alertService": alertService,
                "alertTime": alertTime,
                "alertName": alertRecipient,
                "alertContent": alertContent,
            }
            customer_alert_for_alarm_subscription(payload)
        except Exception as e:
            current_app.logger.error(f"保存记录到mon_center_alert失败: {e}")

        return jsonify({"msg": msg, "type": "success"}), 200
    except Exception as e:
        current_app.logger.error(f"Func Name: alarm_subscription, Error: \n{e}")
        return jsonify({"msg": "告警失败", "type": "failed"}), 500


@monitor_bp.route("/ruisu/acdn")
def ruisu_acdn():
    from datetime import datetime

    try:
        dataraw = get_response(request)

        today = datetime.now()
        start_of_week = today - timedelta(days=today.weekday())
        end_of_week = start_of_week + timedelta(days=6)
        start_of_week_str = start_of_week.strftime("%Y-%m-%d 00:00:00")
        end_of_week_str = end_of_week.strftime("%Y-%m-%d 23:59:59")
        startTime = dataraw.get("BeginTime", start_of_week_str)
        endTime = dataraw.get("EndTime", end_of_week_str)

        country = dataraw.get("Country")

        granularity = dataraw.get("Granularity", "10m")

        base_query = OvsACDN.query.filter(
            OvsACDN.begin_time.between(startTime, endTime)
        )
        if country:
            base_query = base_query.filter(OvsACDN.country == country)
        time_format = get_time_format(granularity, OvsACDN.begin_time)
        time_agg_func = func.date_format(OvsACDN.begin_time, time_format).label("time")
        group_by_columns = [time_agg_func]
        select_columns = [time_agg_func, func.sum(OvsACDN.value).label("sumValue")]
        if country:
            group_by_columns.append(OvsACDN.country)
            select_columns.insert(0, OvsACDN.country)

        sum_query = (
            base_query.with_entities(*select_columns).group_by(*group_by_columns).all()
        )

        country_query = OvsACDN.query.with_entities(OvsACDN.country).distinct().all()
        country_items = [
            {"value": i.country, "label": i.country} for i in country_query
        ]

        sum_data = []
        for row in sum_query:
            data_row = {
                "time": row.time,
                "value": int(row.sumValue) if row.sumValue is not None else 0,
            }
            if country:
                data_row["country"] = row.country
            else:
                data_row["country"] = "All"
            sum_data.append(data_row)
        sum_totalCount = sum([row.sumValue for row in sum_query])
        sum_termsCount = len(sum_query)
        save_to_operation_log(request, action=OvsACDN.__table__, result=200)
        return (
            jsonify(
                {
                    "data": sum_data,
                    "totalCount": sum_totalCount,
                    "termsCount": sum_termsCount,
                    "countries": country_items,
                }
            ),
            200,
        )
    except Exception as e:
        return jsonify({"msg": str(e)}), 500


@monitor_bp.route("/ruisu/ipcountry")
def ruisu_ipcoutry():
    try:
        data_raw = get_response(request)
        now = datetime.now()
        start_of_week = now - timedelta(days=now.weekday())
        end_of_week = start_of_week + timedelta(days=6)
        start_of_week_str = start_of_week.strftime("%Y-%m-%d 00:00:00")
        end_of_week_str = end_of_week.strftime("%Y-%m-%d 23:59:59")

        startTime = data_raw.get("BeginTime", start_of_week_str)
        endTime = data_raw.get("EndTime", end_of_week_str)

        # 查询数据库，计算每个国家平均 ipCount
        avg_ip_counts = (
            db.session.query(
                OvsIpCountry.country,
                func.avg(OvsIpCountry.ipCount).label("averageIpCount"),
            )
            .filter(
                OvsIpCountry.begin_time >= startTime, OvsIpCountry.end_time <= endTime
            )
            .group_by(OvsIpCountry.country)
            .all()
        )

        data = [
            {
                "country": row.country,
                "ipCount": int(row.averageIpCount) if row.averageIpCount else 0,
            }
            for row in avg_ip_counts
        ]
        max_ip_count = max([item["ipCount"] for item in data]) if data else 0
        return (
            jsonify(
                {
                    "data": data,
                    "totalCount": len(data),
                    "termsCount": 0,
                    "MaxAvg": max_ip_count,
                    "dataRange": f"{startTime.split(' ')[0]} - {endTime.split(' ')[0]}",
                }
            ),
            200,
        )

    except Exception as e:
        return jsonify({"error": str(e)}), 500


@monitor_bp.route("/ruisu/v1/ipcountry")
def ruisu_v1_ipcoutry():
    from utils.feishu import config_read

    dataraw = get_response(request)
    todayStart = datetime.now().strftime("%Y-%m-%d 00:00:00")
    todayEnd = datetime.now().strftime("%Y-%m-%d 23:59:59")
    startTime = dataraw.get("BeginTime", todayStart)
    endTime = dataraw.get("EndTime", todayEnd)
    Authorization = config_read.get("ruisu", "Authorization")
    start_time = datetime.now()
    try:
        url = "https://user.ruisuyun.cn/prod-api/acdn/analyticsRest/describeIPByCountry"
        headers = {
            "Accept": "application/json",
            "Authorization": f"Bearer {Authorization}",
            "Content-Type": "application/json;charset=utf-8",
        }
        data = {
            "action": [
                {"metricName": "client_geo_country_name", "pageNo": 1, "pageSize": 30}
            ],
            "distributionNames": ["E26CGWMQ3GFLKX", "E1XC6MR8MQT8FW", "E3IRLEI0NZ7DVD"],
            "otherFilter": [],
            "params": {"beginTime": startTime, "endTime": endTime},
        }

        response = requests.post(url, headers=headers, data=json.dumps(data))
        end_time = datetime.now()
        if response.status_code != 200:
            return (
                jsonify({"msg": f"请求失败，状态码：{response.status_code}"}),
                response.status_code,
            )

        api_data = response.json().get("data", {}).get("list", [])
        totalCount = sum([line["ipCount"] for line in api_data])
        termsCount = len(api_data)
        return (
            jsonify(
                {"data": api_data, "totalCount": totalCount, "termsCount": termsCount}
            ),
            response.status_code,
        )
    except Exception as e:
        return jsonify({"msg": str(e)}), 500


class BaseConfigView(MethodView):
    """基础配置视图类"""

    model = None  # 子类必须覆盖此属性
    status_field = "status"  # 状态字段名,子类可覆盖
    time_field = "up_time"  # 时间字段名,子类可覆盖

    def get_query_filters(self, keyword: str) -> List:
        """
        生成查询过滤条件
        Args:
            keyword: 搜索关键字
        Returns:
            List: 过滤条件列表
        """
        raise NotImplementedError("子类必须实现此方法")

    def get(self) -> tuple:
        """通用查询处理"""
        try:
            kw, page_index, page_size = get_page_keyword(get_response(request))
            keyword = get_name_dictionary(kw) or kw

            # 构建查询
            filters = self.get_query_filters(keyword)
            query = self._build_query(filters, page_index, page_size)
            total_count = self.model.query.filter(*filters).count()

            # 处理数据
            data = self._process_query_results(query)

            return jsonify({"code": 200, "data": data, "totalCount": total_count}), 200

        except Exception as e:
            current_app.logger.error(f"查询失败: {str(e)}", exc_info=True)
            return jsonify({"code": 500, "msg": f"查询失败: {str(e)}"}), 500

    def _build_query(self, filters: List, page_index: int, page_size: int):
        """构建查询对象"""
        # 动态获取排序字段
        status_attr = getattr(self.model, self.status_field)
        time_attr = getattr(self.model, self.time_field)

        return (
            self.model.query.filter(*filters)
            .order_by(status_attr.asc(), time_attr.desc())
            .limit(page_size)
            .offset((page_index - 1) * page_size)
            .all()
        )

    def _process_query_results(self, query_results: List) -> List[Dict]:
        """处理查询结果"""
        data = self.model.to_all_json(query_results)
        for item in data:
            # 处理所有可能的时间字段
            time_fields = ["cre_time", "up_time", "created_time", "updated_time"]
            for time_field in time_fields:
                if time_field in item:
                    item[time_field] = get_time_stamp(item[time_field])
        return data

    def prepare_params(self, params: Dict) -> Dict:
        """清理请求参数"""
        return {k: v for k, v in params.items() if v is not None or v == 0}

    def update_record(self, update_conditions: Dict, update_values: Dict) -> tuple:
        """通用更新处理"""
        try:
            return self._get_or_create_record(update_conditions, update_values)
        except Exception as e:
            current_app.logger.error(f"更新失败: {str(e)}", exc_info=True)
            return jsonify({"code": 500, "msg": str(e)}), 500

    def _get_or_create_record(self, conditions: Dict, values: Dict):
        """获取或创建记录"""
        try:
            record = self.model.query.filter_by(**conditions).first()
            if conditions.get("id"):
                if record:
                    for key, value in values.items():
                        setattr(record, key, value)
            else:
                record = self.model(**{**conditions, **values})
                db.session.add(record)
            db.session.commit()
            return jsonify({"code": 200, "msg": "更新成功"}), 200
        except Exception as e:
            db.session.rollback()
            msg = str(e).split('[')[0]
            current_app.logger.error(f"获取或创建记录失败: {str(e)}", exc_info=True)
            return jsonify({"msg": msg}), 500
        finally:
            db.session.close()

    def post(self) -> tuple:
        """通用POST处理"""
        params = self.prepare_params(request.json)
        params["last_modified_by"] = request.headers.get("X-User", "api")
        update_conditions = self.get_update_conditions(params)
        return self.update_record(update_conditions, params)

    def get_update_conditions(self, params: Dict) -> Dict:
        """生成更新条件"""
        raise NotImplementedError("子类必须实现此方法")


class PpPrometheusConfigView(BaseConfigView):
    """Prometheus配置视图"""

    model = PpPrometheusConfig
    status_field = "alarm_status"  # 覆盖父类的状态字段
    time_field = "updated_time"  # 覆盖父类的时间字段

    def get_query_filters(self, keyword: str) -> List:
        return [
            or_(
                self.model.service_name.like(f"%{keyword}%"),
                self.model.metric_name.like(f"%{keyword}%"),
            )
        ]

    def get_update_conditions(self, params: Dict) -> Dict:
        return {"id": params["id"]} if "id" in params else {}

    def update_record(self, update_conditions: Dict, update_values: Dict) -> tuple:
        try:
            self._process_update_values(update_values)
            return super().update_record(update_conditions, update_values)
        except Exception as e:
            current_app.logger.error(f"更新失败: {str(e)}", exc_info=True)
            return jsonify({"code": 500, "msg": str(e)}), 500

    def _process_update_values(self, values: Dict) -> None:
        """处理更新值的特殊字段"""
        if "threshold_value" in values:
            values["threshold_value"] = int(values["threshold_value"])

        list_fields = ["notify_method", "notify_recipient", "business_group"]
        for field in list_fields:
            if field in values:
                if isinstance(values[field], list):
                    values[field] = ",".join(map(str, values[field]))
        return values


class MqTopicMonitorConfigView(BaseConfigView):
    """MQ Topic监控配置视图"""

    model = MqTopicMonitorConfig
    status_field = "alarm_status"  # 覆盖父类的状态字段
    time_field = "updated_time"  # 覆盖父类的时间字段

    def get_query_filters(self, keyword: str) -> List:
        return [
            or_(
                self.model.name.like(f"%{keyword}%"),
                self.model.topic.like(f"%{keyword}%"),
                self.model.group.like(f"%{keyword}%"),
            )
        ]

    def get_update_conditions(self, params: Dict) -> Dict:
        return {"id": params["id"]} if "id" in params else {}

    def update_record(self, update_conditions: Dict, update_values: Dict) -> tuple:
        try:
            self._process_update_values(update_values)
            return super().update_record(update_conditions, update_values)
        except Exception as e:
            current_app.logger.error(f"更新失败: {str(e)}", exc_info=True)
            return jsonify({"code": 500, "msg": str(e)}), 500

    def _process_update_values(self, values: Dict) -> None:
        """处理更新值的特殊字段"""
        if "threshold_value" in values:
            values["threshold_value"] = int(values["threshold_value"])

        list_fields = ["notify_method", "notify_recipient", "business_group"]
        for field in list_fields:
            if field in values:
                if isinstance(values[field], list):
                    values[field] = ",".join(map(str, values[field]))


def model_query_all(modelObject, status):
    """
    获取 model 模型指定 status 的数据
    :param modelObject:
    :param status:
    :return:
    """
    task_filter = {modelObject.alarm_status == status}
    query = modelObject.query.filter(*task_filter).all()
    return query


@monitor_bp.route("/pinpoint/list")
def pinpoint_list():
    """
    获取 pinpoint 中所有的 prod-A 的服务
    :return:
    """
    modelObject = PinpointCount
    query = (
        modelObject.query.with_entities(modelObject.application)
        .filter(modelObject.application.like("%{}%".format("prod-A-")))
        .distinct()
        .all()
    )

    applications = [
        {"label": item[0].split("prod-A-")[-1], "value": item[0]} for item in query
    ]  # 每个item是一个包含一个元素的元组或列表，获取第一个元素

    service_name_query = (
        CiProjects.query.with_entities(CiProjects.name, CiProjects.appname)
        .distinct()
        .all()
    )

    service_name_dict = {
        appname: {"label": appname, "name": name}
        for name, appname in service_name_query
    }

    # 合并 applications 和 service_name，不存在时name默认为label
    merged_data = [
        {**app, **service_name_dict.get(app["label"], {"name": app["label"]})}
        for app in applications
    ]

    return jsonify({"data": merged_data}), 200


@monitor_bp.route("/pinpoint/push", methods=["POST"])
def pinpoint_push():
    """
    构建 prometheus yml 规则文件，上传到 prometheus 中
    :return:
    """
    from utils.monitor import yaml_push

    modelObject = PpPrometheusConfig
    query = model_query_all(modelObject, 1)
    query_res = PpPrometheusConfig.to_all_json(query)
    current_app.logger.info(f"query_res: {query_res}")
    rule_files = {}
    for line in query_res:
        rule_files.setdefault("pinpoint-services", []).append(line)
    current_app.logger.info("Pinpoint rule_files: {} 上传".format(rule_files))
    return yaml_push.push_rule_file(rule_files, "pp")


@monitor_bp.route("/rocketmq/push", methods=["POST"])
def rocketmq_update_rule():
    """
    上传 rocketmq 规则文件到 prometheus
    :return:
    """
    # from utils.monitor import push_rule_file
    from utils.monitor import yaml_push

    modelObject = MqTopicMonitorConfig
    query = model_query_all(modelObject, 1)
    query_res = MqTopicMonitorConfig.to_all_json(query)
    current_app.logger.info(f"query_res: {query_res}")
    rule_files = {}
    for line in query_res:
        rule_files.setdefault(line["name"], []).append(line)
    current_app.logger.info("RocketMq rule_files: {} 上传".format(rule_files))
    return yaml_push.push_rule_file(rule_files, "mq")


@monitor_bp.route("/rocketmq/async/topic", methods=["POST"])
def rocketmq_async_topic():
    """
    同步 topic 列表
    :return:
    """
    from utils.monitor import get_topic

    res = get_topic.main()
    if isinstance(res, list):
        try:
            for line in res:
                insert_stmt = insert(MqTopicMonitorConfig).values(**line)
                on_duplicate_key_stmt = insert_stmt.on_duplicate_key_update(**line)
                db.engine.execute(on_duplicate_key_stmt)
            save_to_operation_log(
                request, action=MqTopicMonitorConfig.__tablename__, result=200
            )
            return jsonify({"msg": "【Topic列表】更新完成"}), 200
        except Exception as e:
            save_to_operation_log(
                request, action=MqTopicMonitorConfig.__tablename__, result=500
            )
            return jsonify({"msg": str(e)}), 500
    save_to_operation_log(
        request, action=MqTopicMonitorConfig.__tablename__, result=403
    )
    return jsonify({"msg": str(res)}), 403


@monitor_bp.route("/pinpoint/send/webhook", methods=["POST"])
def pinpoint_send_webhook():
    """
    发送 webhook
    :return:
    """
    from dateutil import parser

    data_raw = get_response(request)
    try:
        alert = data_raw.get("alerts")[0]
        alert_title = alert.get("labels").get("alertname")  # 字符串
        alert_object = alert.get("labels").get("appname")
        alert_message = alert.get("annotations").get("description")
        alert_severity = alert.get("labels").get("severity")

        starts_at_str = alert.get("startsAt")
        starts_at = parser.parse(starts_at_str)
        adjusted_time = starts_at + timedelta(hours=8)
        alert_formatted_time = adjusted_time.strftime("%Y-%m-%d %H:%M:%S")

        # 获取 receiver 告警人
        appname = alert.get("labels").get("appname")
        type_ = alert.get("labels").get("type")
        query = (
            PpPrometheusConfig.query.with_entities(PpPrometheusConfig.receiver)
            .filter_by(name=appname, type=type_)
            .first()
        )
        if query is not None:
            receiver = query.receiver if query.receiver is not None else "all"
        else:
            receiver = "all"

        content = f"""
    **告警类型**:  {alert_title}
    **告警对象**:  {alert_object}
    **告警级别**:  {alert_severity}
    **告警详情**:  {alert_message}
    **触发时间**:  {alert_formatted_time}
    **查看详情**:  [PinPoint 控制台](https://pinpoint.in.szwego.com/main/{alert_object}@TOMCAT/5m/{datetime.now().strftime("%Y-%m-%d-%H-%M-%S")})
    """
        jsondict = {
            "type": "1",
            "content": content,
            "recipient": receiver,
            "service": alert_object,
        }
        if data_raw["status"] == "firing":
            # 发送告警
            jsondict["title"] = (f"🟥 {alert_object} 告警",)
        if data_raw["status"] == "resolved":
            # 发送恢复
            jsondict["title"] = (f"🟩 {alert_object} 恢复",)
        current_app.logger.info(f"发送告警参数：{jsondict}")
        fs_send_url = url_for("monitor_alert_blue.alarm_subscription", _external=True)
        return current_app.test_client().post(fs_send_url, json=jsondict)
    except Exception as e:
        current_app.logger.error(f"发送告警失败，具体信息：\n{e}")
        return jsonify({"code": 500, "msg": str(e)}), 500


@monitor_bp.route("/rocketmq/send/webhook", methods=["POST"])
def rocketmq_send_webhook():
    """
    rocketmq 飞书消息通知
    """
    from dateutil import parser

    data_raw = get_response(request)
    current_app.logger.info(f"rocketmq 获取到的 alert：{data_raw}")
    try:

        alert = data_raw.get("alerts")[0]
        alert_title = alert.get("labels").get("alertname")
        alert_obj = alert.get("labels").get("topic")
        alert_severity = alert.get("labels").get("severity")
        alert_message = alert.get("annotations").get("description")

        starts_at_str = alert.get("startsAt")
        starts_at = parser.parse(starts_at_str)
        adjusted_time = starts_at + timedelta(hours=8)
        alert_formatted_time = adjusted_time.strftime("%Y-%m-%d %H:%M:%S")

        # 获取 receiver 告警人
        topic = alert.get("labels").get("topic")
        group = alert.get("labels").get("group")
        query = (
            MqTopicMonitorConfig.query.with_entities(MqTopicMonitorConfig.receiver)
            .filter_by(topic=topic, group=group)
            .first()
        )
        if query is not None:
            receiver = query.receiver if query.receiver is not None else "all"
        else:
            receiver = "all"

        content = f"""
    **告警类型**:  {alert_title}
    **告警对象**:  {alert_obj}
    **告警级别**:  {alert_severity}
    **告警详情**:  {alert_message}
    **触发时间**:  {alert_formatted_time}
    **查看详情**:  [RocketMQ 监控](http://***********:9090/alerts)
    """

        current_app.logger.info(f"得到的content: {content}")

        jsondict = {
            "type": "1",
            "content": content,
            "recipient": receiver,
            "service": alert_obj,
        }
        current_app.logger.info(f"得到的jsondict: {jsondict}")
        if data_raw["status"] == "firing":
            # 发送告警
            jsondict["title"] = (f"🟥 {alert_obj} 堆积告警",)

        if data_raw["status"] == "resolved":
            # 发送恢复
            jsondict["title"] = (f"🟩 {alert_obj} 恢复",)
        fs_send_url = url_for("monitor_alert_blue.alarm_subscription", _external=True)
        return current_app.test_client().post(fs_send_url, json=jsondict)
    except Exception as e:
        current_app.logger.error(f"发送告警失败，具体信息：\n{e}")
        return jsonify({"code": 500, "msg": str(e)}), 500


@monitor_bp.route("/pinpoint/public/webhook", methods=["POST"])
def pinpoint_public_webhook():
    """
    pinpoint 通用告警
    """
    from dateutil import parser

    data_raw = get_response(request)
    try:
        alert = data_raw.get("alerts")[0]
        alert_title = alert.get("labels").get("alertname")  # 字符串
        alert_message = alert.get("annotations").get("description")
        alert_severity = alert.get("labels").get("severity")
        starts_at_str = alert.get("startsAt")
        starts_at = parser.parse(starts_at_str)
        adjusted_time = starts_at + timedelta(hours=8)
        alert_formatted_time = adjusted_time.strftime("%Y-%m-%d %H:%M:%S")
        receiver = "all"

        content = f"""
    **告警类型**:  {alert_title}
    **告警级别**:  {alert_severity}
    **告警详情**:  {alert_message}
    **触发时间**:  {alert_formatted_time}
    """
        jsondict = {
            "type": "1",
            "content": content,
            "recipient": receiver,
            "service": alert_title,
        }
        if data_raw["status"] == "firing":
            jsondict["title"] = (f"🟥 {alert_title} 告警",)
        if data_raw["status"] == "resolved":
            jsondict["title"] = (f"🟩 {alert_title} 恢复",)
        current_app.logger.info(f"发送告警参数：{jsondict}")
        fs_send_url = url_for("monitor_alert_blue.alarm_subscription", _external=True)
        return current_app.test_client().post(fs_send_url, json=jsondict)

    except Exception as e:
        current_app.logger.error(f"发送告警失败，具体信息：\n{e}")
        return jsonify({"code": 500, "msg": str(e)}), 500


monitor_bp.add_url_rule(
    "/mq/conf", view_func=MqTopicMonitorConfigView.as_view("mq_monitor_config")
)
monitor_bp.add_url_rule(
    "/pinpoint/conf", view_func=PpPrometheusConfigView.as_view("pp_prometheus_config")
)


# @monitor_bp_v2.route("/send/feishu/webhook/pinpoint", methods=["POST"])
# async def pinpoint_send_webhook_v2():
#     """
#     处理并发送 Pinpoint 告警到飞书
#     """
#     try:
#         alert = await _get_validated_alert(request)
#
#         robot_urls = await _get_robot_urls(alert.business_id_list)
#         if not robot_urls:
#             return jsonify({"code": 400, "msg": "未找到有效的机器人URL"}), 400
#
#         user_open_ids = await _get_user_open_ids(alert)
#
#         card = await _build_alert_card(alert, user_open_ids)
#
#         # results = await _send_messages(robot_urls, card)
#
#         return jsonify({"code": 200, "message": card})
#
#     except Exception as e:
#         current_app.logger.error(f"发送告警失败，具体信息：\n{e}")
#         return jsonify({"code": 500, "msg": str(e)}), 500
#
#
# async def _get_validated_alert(request) -> PinPointAlert:
#     """获取并验证告警数据"""
#     data_raw = get_response(request)
#     alerts = [PinPointAlert(**alert) for alert in data_raw.get("alerts", [])]
#     if not alerts:
#         raise ValueError("请求中未找到告警数据")
#     return alerts[0]
#
#
# async def _get_robot_urls(business_id_list: Union[str, List[str]]) -> List[str]:
#     """
#     通用获取飞书机器人URLs方法
#
#     Args:
#         business_id_list: 可以是逗号分隔的字符串或字符串列表
#
#     Returns:
#         List[str]: 机器人URL列表
#     """
#     # 如果是字符串，将其分割为列表
#     if isinstance(business_id_list, str):
#         valid_business_ids = [
#             bid.strip() for bid in business_id_list.split(",") if bid.strip()
#         ]
#     else:
#         valid_business_ids = [bid for bid in business_id_list if bid]
#
#     current_app.logger.info(f"处理后的业务IDs: {valid_business_ids}")
#
#     if valid_business_ids:
#         query = FeishuChatData.query.filter(
#             FeishuChatData.chat_id.in_(valid_business_ids)
#         ).all()
#         return [item.robot_url for item in query] if query else []
#
#     query = FeishuChatData.query.filter_by(name="告警中心").first()
#     return [query.robot_url] if query else []
#
#
# async def _get_user_open_ids(alert: PinPointAlert) -> List[str]:
#     """获取用户OpenIDs"""
#     recipient_query = (
#         PpPrometheusConfig.query.with_entities(PpPrometheusConfig.notify_recipient)
#         .filter_by(service_name=alert.alert_object, metric_name=alert.type_)
#         .first()
#     )
#
#     recipient = (
#         recipient_query.notify_recipient
#         if recipient_query and recipient_query.notify_recipient
#         else "all"
#     )
#     user_name_list = recipient.split(",")
#
#     if "all" in user_name_list:
#         return ["all"]
#
#     task_filter = and_(
#         SsousersModel.status == 1,
#         or_(
#             SsousersModel.username.in_(user_name_list),
#             SsousersModel.displayname.in_(user_name_list),
#         ),
#     )
#
#     user_results = (
#         FeishuUserData.query.join(
#             SsousersModel,
#             or_(
#                 SsousersModel.email == FeishuUserData.email,
#                 SsousersModel.displayname == FeishuUserData.name,
#             ),
#         )
#         .filter(task_filter)
#         .with_entities(FeishuUserData.open_id)
#         .all()
#     )
#
#     return [user.open_id for user in user_results]
#
#
# async def _build_alert_card(alert: PinPointAlert, user_open_ids: List[str]) -> dict:
#     """构建告警卡片"""
#     from dateutil import parser
#     from utils.feishu import config_read
#
#     # 触发时间
#     starts_at = parser.parse(alert.starts_at_str)
#     alert_formatted_time = (starts_at + timedelta(hours=8)).strftime(
#         "%Y-%m-%d %H:%M:%S"
#     )
#
#     # 构建@提醒
#     alert_name = " ".join([f"<at id={userid}></at>" for userid in user_open_ids])
#
#     # 构建告警内容
#     alert_content = f"""
#     **级别**:  {alert.alert_severity}
#     **详情**:  {alert.alert_message}
#     **跳转**:  [PinPoint 控制台](https://pinpoint.in.szwego.com/main/{alert.alert_object}@TOMCAT/5m/{datetime.now().strftime("%Y-%m-%d-%H-%M-%S")})
#     **触发时间**:  {alert_formatted_time}
#     """
#
#     # 获取模板配置
#     template_id = config_read.get("message-id-prod", "alarmCenter")
#     template_version = config_read.get("message-id-prod", "alarmVersion")
#
#     return {
#         "msg_type": "interactive",
#         "card": {
#             "type": "template",
#             "data": {
#                 "template_id": template_id,
#                 "template_version_name": template_version,
#                 "template_variable": {
#                     "alertTitle": (
#                         f"🟥 {alert.alert_title} 告警"
#                         if alert.status == "firing"
#                         else f"🟩 {alert.alert_title} 恢复"
#                     ),
#                     "alertService": alert.alert_object,
#                     "alertTime": datetime.now().strftime("%Y-%m-%d %H:%M:%S"),
#                     "alertName": alert_name,
#                     "alertContent": alert_content,
#                 },
#             },
#         },
#     }
#
#
# async def _send_messages(robot_urls: List[str], card: dict) -> List[str]:
#     """发送消息到多个机器人"""
#     return [{"result": feishu_robot_v2(url, card), "robot": url} for url in robot_urls]
#


@monitor_bp_v2.route("/send/webhook/general", methods=["POST"])
async def general_alert_webhook():
    """
    通用告警处理接口
    支持不同类型的告警消息发送到飞书
    """
    try:
        # 获取告警数据
        data_raw = get_response(request)
        current_app.logger.info(f"接收到告警消息: {data_raw}")
        if not data_raw.get("alerts"):
            return jsonify({"code": 400, "msg": "请求中未找到告警数据"}), 400

        # 告警类型与处理类的映射
        alert_handlers = {
            "pinpoint": PinPointAlert,
            "rocketmq": MqTopicAlert,
            "custom": CustomAlert,
            "default": CustomAlert,
        }

        results = []
        for alert_data in data_raw.get("alerts", []):
            # 从告警数据中获取告警类型，优先从根级别获取，如果没有则尝试从 labels 中获取
            alert_type = alert_data.get("alert_type") or alert_data.get(
                "labels", {}
            ).get("alert_type", "default")

            # 获取对应的告警处理类
            alert_class = alert_handlers.get(alert_type)
            if not alert_class:
                current_app.logger.warning(
                    f"不支持的告警类型: {alert_type}，使用默认处理类"
                )
                alert_class = alert_handlers["default"]

            # 验证和处理告警数据
            alert = alert_class(**alert_data)
            current_app.logger.info(f"告警数据: {alert}")
            current_app.logger.info(f"原始 business_id_list: {alert.business_id_list}")

            robot_urls = await _get_general_robot_urls(alert.business_id_list)
            if not robot_urls:
                current_app.logger.warning(
                    f"未找到有效的机器人URL，告警类型: {alert_type}"
                )
                return jsonify({"code": 400, "msg": "未找到有效的机器人URL"}), 400

            # 获取用户OpenIDs
            user_open_ids = await _get_general_user_open_ids(alert, alert_type)

            # 构建告警卡片
            card = await _build_general_alert_card(alert, user_open_ids, alert_type)

            # 发送告警消息
            send_results = await _send_general_messages(robot_urls, card)
            results.append(
                {
                    "alert_type": alert_type,
                    "alert_title": alert.alert_title,
                    "send_results": send_results,
                }
            )

        # 数据落盘处理
        try:
            customer_alert_for_send_webhook_general(data_raw)
        except Exception as e:
            current_app.logger.error(f"数据落盘失败，具体信息：\n{e}")

        if not results:
            return jsonify({"code": 400, "msg": "没有成功发送的告警"}), 400

        return jsonify({"code": 200, "message": "告警发送成功", "results": results})

    except Exception as e:
        current_app.logger.error(f"发送告警失败，具体信息：\n{e}")
        return jsonify({"code": 500, "msg": str(e)}), 500


async def _get_general_robot_urls(business_id_list: Union[str, List[str]]) -> List[str]:
    if isinstance(business_id_list, str):
        valid_business_ids = [
            bid.strip() for bid in business_id_list.split(",") if bid.strip()
        ]
    else:
        valid_business_ids = [bid for bid in business_id_list if bid]

    robot_urls = []
    if valid_business_ids:
        query = FeishuChatData.query.filter(
            FeishuChatData.chat_id.in_(valid_business_ids)
        ).all()
        if query:
            robot_urls = [item.robot_url for item in query if item.robot_url]

    if not robot_urls:
        query = FeishuChatData.query.filter_by(name="告警中心").first()
        if query and query.robot_url:
            robot_urls = [query.robot_url]

    return robot_urls


async def _get_general_user_open_ids(
        alert: Union[PinPointAlert, MqTopicAlert, CustomAlert], alert_type: str
) -> List[str]:
    """
    通用获取用户OpenIDs方法
    """

    if alert_type == "pinpoint":
        recipient_query = (
            PpPrometheusConfig.query.with_entities(PpPrometheusConfig.notify_recipient)
            .filter_by(service_name=alert.alert_object, metric_name=alert.type_)
            .first()
        )
    elif alert_type == "rocketmq":
        recipient_query = (
            MqTopicMonitorConfig.query.with_entities(
                MqTopicMonitorConfig.notify_recipient
            )
            .filter_by(topic=alert.alert_object, group=alert.type_)
            .first()
        )
    else:
        recipient_query = None

    recipient = (
        recipient_query.notify_recipient
        if recipient_query and recipient_query.notify_recipient
        else "all"
    )
    user_name_list = recipient.split(",")

    if "all" in user_name_list:
        return ["all"]

    task_filter = and_(
        SsousersModel.status == 1,
        or_(
            SsousersModel.username.in_(user_name_list),
            SsousersModel.displayname.in_(user_name_list),
        ),
    )

    user_results = (
        FeishuUserData.query.join(
            SsousersModel,
            or_(
                SsousersModel.email == FeishuUserData.email,
                SsousersModel.displayname == FeishuUserData.name,
            ),
        )
        .filter(task_filter)
        .with_entities(FeishuUserData.open_id)
        .all()
    )

    return [user.open_id for user in user_results]


async def _build_general_alert_card(
        alert: Union[PinPointAlert, MqTopicAlert, CustomAlert],
        user_open_ids: List[str],
        alert_type: str,
) -> dict:
    from dateutil import parser
    from utils.feishu import config_read

    starts_at = parser.parse(alert.starts_at_str)
    alert_formatted_time = (starts_at + timedelta(hours=8)).strftime(
        "%Y-%m-%d %H:%M:%S"
    )

    # 构建@提醒
    alert_name = " ".join([f"<at id={userid}></at>" for userid in user_open_ids])

    # 根据告警类型构建不同的跳转链接
    jump_links = {
        "pinpoint": f"[PinPoint 控制台](https://pinpoint.in.szwego.com/main/{alert.alert_object}@TOMCAT/5m/{datetime.now().strftime('%Y-%m-%d-%H-%M-%S')})",
        "rocketmq": "[RocketMQ 控制台](http://***********:9090/alerts)",
        "custom": "",
    }

    if alert_type == "custom" and hasattr(alert, "extra_info") and alert.extra_info:
        extra_info = alert.extra_info or {}
        current_app.logger.info(f"extra_info: {extra_info}")
        jump_url = extra_info.pop("jump_url", None)
        current_app.logger.info(f"jump_url: {jump_url}")
        if jump_url:
            jump_links["custom"] = f"[{alert.alert_object}]({jump_url})"
        extra_info_str = ""
        if extra_info:
            extra_info_str = "\n".join(
                [f"          **{k}**: {v}" for k, v in extra_info.items()]
            )
            if extra_info_str:
                extra_info_str = f"\n{extra_info_str}"
    else:
        extra_info_str = ""

    jump_link = jump_links.get(alert_type, "暂无跳转链接")

    alert_content = f"""
      **级别**:  {alert.alert_severity}
      **详情**:  {alert.alert_message}{extra_info_str}
      **跳转**:  {jump_link}
      **触发时间**:  {alert_formatted_time}
    """

    # 获取模板配置
    template_id = config_read.get("message-id-prod", "alarmCenter")
    template_version = config_read.get("message-id-prod", "alarmVersion")

    # 根据优先级添加不同的图标
    current_app.logger.info(f"告警优先级: {alert}")
    priority_icons = {0: "🔵", 1: "🟡", 2: "🔴"}
    priority = getattr(alert, "priority", 0)
    alert_icon = priority_icons.get(priority, "🔵")

    return {
        "msg_type": "interactive",
        "card": {
            "type": "template",
            "data": {
                "template_id": template_id,
                "template_version_name": template_version,
                "template_variable": {
                    "alertTitle": (
                        f"{alert_icon} {alert.alert_title}"
                        if alert.status == "firing"
                        else f"🟩 {alert.alert_title} 恢复"
                    ),
                    "alertService": alert.alert_object,
                    "alertTime": datetime.now().strftime("%Y-%m-%d %H:%M:%S"),
                    "alertName": alert_name,
                    "alertContent": alert_content,
                },
            },
        },
    }


async def _send_general_messages(robot_urls: List[str], card: dict) -> List[dict]:
    return [{"result": feishu_robot_v2(url, card), "robot": url} for url in robot_urls]
