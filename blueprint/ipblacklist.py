#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
@Title:     securitywaf_Old.py
@Date:      2025/6/19 14:00
@Author:    wanglh
@Desc:      ip黑名单
"""

from flask import request, views
from sqlalchemy.exc import SQLAlchemyError

from utils import *
from utils import get_page_keyword, jsonify
from .securitywaf import waf_bp
from .useritsm import get_name_dictionary


class BaseIpBlacklistView(views.MethodView):
    model = WafIpBlackList

    def __init__(self):
        if not self.model:
            raise ValueError("Model not defined")
        self.data_raw = get_response(request)
        n_status = request.args.get('n_status', None)
        if n_status is not None and n_status != "":
            self.data_raw['n_status'] = n_status
        current_app.logger.info("BaseIpBlacklistView", self.data_raw)
    
    def __initKeyword(self):
        """
        子类可重写，提供关键词处理逻辑
        """
        self.keyword, self.pageIndex, self.pageSize = self.get_params()

    @staticmethod
    def get_params():
        """
        获取分页和关键词参数（假设这些函数已定义）
        """
        kw, pageIndex, pageSize = get_page_keyword(get_response(request))
        keyword = get_name_dictionary(kw) or kw
        return keyword, pageIndex, pageSize

    def get_query_filters(self):
        filters = []

        if self.data_raw:
            n_status = self.data_raw.get('n_status')
            if n_status is not None:
                filters.append(self.model.n_status == n_status)
        self.__initKeyword()
        current_app.logger.info("keyword", self.keyword, self.pageIndex, self.pageSize)
        if self.keyword:
            keyword_filters = or_(
                self.model.c_ip_addr.like(f"%{self.keyword}%"),
                self.model.c_ip_source.like(f"%{self.keyword}%"),
                self.model.c_remark.like(f"%{self.keyword}%")
            )
            filters.append(keyword_filters)
        return self.model.query.filter(*filters)

    def get(self):
        """
        默认：获取过滤条件后的所有数据
        """
        try:
            data_query = self.get_query_filters()
            total_count = data_query.count()

            order_case = db.case(
                (self.model.n_status == 0, 0),
                (self.model.n_status == 2, 1),
                (self.model.n_status == 1, 2),
                else_=3
            )
            data_query = data_query.order_by(
                order_case, self.model.t_create_time.desc())

            data_query = data_query.limit(self.pageSize).offset(
                (self.pageIndex - 1) * self.pageSize).all()
            data = self.model.to_all_json(data_query)

            return jsonify({
                "code": 200,
                "data": data,
                "totalCount": total_count,
            }), 200
        except Exception as e:
            current_app.logger.exception(e)
            return jsonify({"code": 500, "msg": str(e)}), 500

    def post(self):
        try:
            params = self.prepare_params(self, request.json)
            update_conditions = self.get_update_conditions(params)
            return self.update_record(update_conditions, params)
        except Exception as e:
            current_app.logger.exception(e)
            return jsonify({"code": 500, "msg": str(e)}), 500

    @staticmethod
    def prepare_params(self, params):
        """
        清理参数，移除None值，保留0值，只保留模型中存在的字段
        """

        model_columns = {column.name for column in self.model.__table__.columns}
    
        return {
            k: v for k, v in params.items()
            if k in model_columns and (v is not None or v == 0)}

    def update_record(self, update_conditions, update_values):
        """更新记录，子类可重写提供具体逻辑"""
        try:

            model_columns = {column.name for column in self.model.__table__.columns}
            filtered_values = {
                k: v for k, v in update_values.items() 
                if k in model_columns
            }

            if update_conditions:
                record = self.model.query.filter_by(
                    **update_conditions).first()
                if record:
                    for key, value in update_values.items():
                        setattr(record, key, value)
                    msg='更新成功'
                else:
                    return jsonify({"code": 404, "msg": "记录未找到"}), 404
            else:
                record = self.model(**filtered_values)
                db.session.add(record)
                msg = '新增成功'
            db.session.commit()
            return jsonify({"code": 200, "msg": msg}), 200
        except SQLAlchemyError as e:
            db.session.rollback()
            current_app.logger.exception(e)
            return jsonify({"code": 500, "msg": "数据库错误，请稍后再试"}), 500
        except Exception as e:
            db.session.rollback()
            current_app.logger.exception(e)
            return jsonify({"code": 500, "msg": str(e)}), 500
        finally:
            db.session.close()

    def get_update_conditions(self, params):
        """生成更新条件，子类实现具体逻辑"""
        if "n_id" in params and params["n_id"] is not None:
            return {"n_id": params["n_id"]}
        else:
            return None


class WafIpBlacklistView(BaseIpBlacklistView):
    pass


def ViewItem(cls_view):
    cls_view.name = f"{cls_view.model.__table__.name}_api"
    cls_view.url = f"/{'/'.join(cls_view.model.__table__.name.split('_'))}"
    return cls_view


# 注册视图
view_classes = [
    WafIpBlacklistView
]

for view_class in view_classes:
    view_class = ViewItem(view_class)
    view_func = view_class.as_view(view_class.name)
    waf_bp.add_url_rule(view_class.url, view_func=view_func)
