#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
@Title:     nginx.py
@Date:      2023/3/9 9:39
@Author:    wanglh
@Desc：     nginx 路由等相关blueprint
"""

import time
import urllib.error
import urllib.parse
import urllib.request

from flask import Blueprint, abort, request
from sqlalchemy import and_, distinct, func, or_

from utils import *
from utils import _now

nginx_bp = Blueprint("nginx_blue", __name__, url_prefix="/nginx/api/v1")


@nginx_bp.route('/while/list/add', methods=['POST'])
def while_list_create():
    """
    添加白名单
    :return:
    """
    username = request.headers.get("X-User")
    createtime = _now()
    id_str = _now("%Y%m%d")  # apply_ci_appid的格式为：“时间-num”

    data = get_response(request)
    uris = data['uri'].replace(",", "\n").split('\n')
    uris = [i.strip() for i in uris if i.strip() != ""]
    applicant = data.get('applicant', "")  # 申请人
    version_code = data.get("apply_service", "")  # 服务 可选
    status = data.get("status", 0)  # 默认为0
    current_app.logger.info("添加白名单：{}，{}，{}，{}".format(uris, applicant, version_code, status))
    filter_str = '%{}%'.format(id_str)
    cnts = db.session.query(func.count(distinct(CiUriwhitelist.apply_ci_appid))).filter(
        CiUriwhitelist.apply_ci_appid.like(filter_str)).order_by(CiUriwhitelist.apply_ci_appid.desc()).first()[0]
    num = cnts + 1
    if int(num) < 9:
        num = str(num).zfill(2)
    apply_ci_appid = "{}-{}".format(id_str, num)  # CI流程ID
    column_name = ("uri", "create_time", "apply_ci_appid", "applicant", "version_code", "status")
    values = [(uri, createtime, apply_ci_appid, applicant, version_code, status) for uri in uris]
    datas = [dict(zip(column_name, i)) for i in values]
    success_uri = []
    failed_uri = []
    try:
        db.session.execute(CiUriwhitelist.__table__.insert(), datas)
        db.session.commit()
        for uri in uris:
            query = CiUriwhitelist.query.filter_by(uri=uri).first()
            if query:
                success_uri.append(query.uri)
                continue
            failed_uri.append(uri)

        message = [{"添加成功": success_uri}, {"添加失败": failed_uri}]
        if len(success_uri) >= 1:
            values = {
                "touser": "wanglinhao|dongxin|wuxuan|lijiana",
                "toparty": "",
                "msgtype": "textcard",
                "agentid": "1000031",
                "textcard": {
                    "title": "滴滴滴：有白名单审核需要处理",
                    "description": "<div class=\"normal\"> 申请时间：%s </div>" \
                                   "<div class=\"normal\"> 申请人：%s </div>" \
                                   "<div class=\"normal\"> 版本关联：%s </div>" \
                                   "<div class=\"highlight\"> 白名单列表：%s </div>" \
                                   % (createtime, username, version_code, success_uri),
                    "url": "https://oa.in.szwego.com/wx.html?url=https://ops.in.szwego.com/%23/devops/whitelist",
                    "btntxt": "立即查看"
                }
            }
            loonflow_msg(values)
            task_filter = {
                and_(
                    IpListModel.type == 'CVM',
                    or_(
                        IpListModel.name.like("负载均衡%"),
                        IpListModel.private_ip == '************',
                    )
                )
            }
            query = IpListModel.query.filter(*task_filter).all()
            ip_result = IpListModel.to_all_json(query)
            result = []
            for ip_dict in ip_result:
                ip = ip_dict['private_ip']
                res = requests.get(f"http://{ip}/sync-uriwhitelist")
                result.append({ip: res.status_code})
            message.append({"访问结果": result})
        return jsonify({"code": 200, "msg": message})
    except Exception as e:
        current_app.logger.error(f"添加白名单失败，具体信息：{e}")
        return jsonify({"code": 500, "msg": str(e)})


@nginx_bp.route('/while/list/query', methods=['POST'])
def while_list_query():
    """
    白名单查询
    :return:
    """
    try:
        dataraw = get_response(request)
        pageNo = dataraw.get("pageNo", 1)
        pageSize = dataraw.get("pageSize", 10)
        uris = dataraw.get("uris", "")
        task_filter = {
            or_(
                CiUriwhitelist.uri.like("%{}%".format(uris)),
                CiUriwhitelist.apply_ci_appid.like("%{}%".format(uris))
            )
        }
        column_name = (
            "apply_ci_appid", "uri", "create_time", "applicant", "approver", "status", "version_code", "memo")
        select_query = db.session.query(CiUriwhitelist.apply_ci_appid, func.group_concat(CiUriwhitelist.uri),
                                        func.max(CiUriwhitelist.create_time),
                                        func.max(CiUriwhitelist.applicant),
                                        func.max(CiUriwhitelist.approver),
                                        func.max(CiUriwhitelist.status),
                                        func.max(CiUriwhitelist.version_code),
                                        func.max(CiUriwhitelist.memo)).filter(*task_filter).group_by(
            CiUriwhitelist.apply_ci_appid).order_by(
            func.max(CiUriwhitelist.create_time).desc()).limit(pageSize).offset((pageNo - 1) * pageSize).all()
        totalCount = db.session.query(func.count(func.distinct(CiUriwhitelist.apply_ci_appid))).filter(
            *task_filter).scalar()
        datas = []
        for i in select_query:
            i = list(i)
            i[1] = i[1].split(',')
            i[2] = i[2].strftime("%Y-%m-%d %H:%M:%S")
            datas.append(dict(zip(column_name, i)))
        return jsonify({"code": 200, "data": datas, "msg": "获取成功", "totalCount": totalCount})
    except Exception as e:
        current_app.logger.error(f"白名单查询失败，具体信息：{e}")
        return jsonify({"code": 500, "msg": str(e)})


@nginx_bp.route('/while/list/audit', methods=['POST'])
def while_list_audit():
    """
    白名单审核
    :return:
    """
    try:
        nowtime = _now()
        dataraw = get_response(request)
        apply_ci_appid = dataraw.get("apply_ci_appid", "")
        approver = dataraw.get("approver", "")
        status = dataraw.get("status", 1)
        update_dic = {"status": status, "approver": approver}
        CiUriwhitelist.query.filter_by(apply_ci_appid=apply_ci_appid).update(update_dic)
        db.session.commit()
        all_query = CiUriwhitelist.query.filter_by(apply_ci_appid=apply_ci_appid).all()
        data = CiUriwhitelist.to_all_json(all_query)
        info = data[0]
        message = "修改apply_ci_appid：{}，" \
                  "status：{}，" \
                  "执行结果：{}".format(apply_ci_appid, status, "更新成功")

        if status == 1:
            values = {
                "touser": info['applicant'],
                "toparty": "",
                "msgtype": "textcard",
                "agentid": "1000031",
                "textcard": {
                    "title": "白名单审核通过",
                    "description": "<div class=\"normal\"> 审核时间：%s </div>" \
                                   "<div class=\"normal\"> 审核人：%s </div>" \
                                   "<div class=\"normal\"> 版本号：%s </div>" \
                                   "<div class=\"normal\"> 流水号：%s </div>" \
                                   "<div class=\"highlight\"> 具体信息请查看名单列表 </div>"
                                   % (nowtime, info["approver"], info["version_code"], info["apply_ci_appid"]),
                    "url": "https://oa.in.szwego.com/wx.html?url=https://ops.in.szwego.com/#/devops/whitelist",
                    "btntxt": "立即查看"
                }
            }
            loonflow_msg(values)
        return jsonify({"code": 200, "msg": message, "data": data})
    except Exception as e:
        current_app.logger.error(f"白名单审核失败，具体信息：{e}")
        return jsonify({"code": 500, "msg": str(e)})


@nginx_bp.route('/nginxrouter')
def nginx_route():
    uri = request.args.get('uri')
    if uri:
        if '/' not in uri:
            uri = '/' + uri
        URL = "http://localtest.szwego.com:8080" + uri
        req = requests.get(URL)
        value = json.loads(req.text)
        html = f"""
        <style>
            .table{{width:100%;table-layout:fixed;}}
            .td{{width:100%;overflow:hidden;}}
        </style>
        <div style="max-height: 500px;overflow-y:scroll;overflow-x:none;">
        <table cellspacing="0" cellpadding="2" width="100%" border="1" style="table-layout:fixed;" align="right">
            <tr>
                <td width="10">请求uri</td>
                <td width="40">{value.get("request_uri", None)}</td>
            </tr>
            <tr>
                <td width="10">后端</td>
                <td width="40">{value.get("upstream", None)}</td>
            </tr>
            <tr>
                <td width="10">转发uri</td>
                <td width="40">{value.get("map", None)}</td>
            </tr>
            <tr>
                <td width="10">白名单</td>
                <td width="40">{value.get("whitelist", None)}</td>
            </tr>
            <tr>
                <td width="10">环境</td>
                <td width="40">{value.get("env", None)}</td>
            </tr>
        </table>
        </div>"""
        return jsonify({"code": 200, "data": html})
    return jsonify({"code": 200, "data": "None"})


# Nginx 调度程序
def iptolabel(ip):
    if NGINX_CMDB.has_key(ip):
        return NGINX_CMDB[ip]
    else:
        return "Other"


def as_num(x):
    y = '{:.0f}'.format(x)  # .1f 保留1位小数  想为十位就这样写10f
    return y


def ts2date(x):
    timeArray = time.localtime(float(as_num(float(x))))
    return time.strftime("%Y-%m-%d %H:%M:%S", timeArray)


def switchAB(ip, ab):
    url = 'http://%s/set-sysconfig?AB_Switch=%s' % (ip, ab)
    print(url)
    req = urllib.request.Request(url)
    result = urllib.request.urlopen(req)
    print(result.readlines())
    return


@nginx_bp.route('/tasks/stats', methods=['POST'])
def tasks_stats():
    # print books["connections"][0]["metrics"]["normal"]
    clusternodes = [
        {"name": "INTERNET",
         "renderer": "focusedChild",
         "class": "normal"
         }
    ]
    clusterconns = []
    # 用来保存nginx.conf文件修改时间
    modifytimelist = {}
    reloadtimelist = {}
    totalconn = 0
    for ip in NGINX_CLUSTER:
        node = {
            "name": ip,
            "renderer": "focusedChild",
            "maxVolume": 5000,
            "class": "normal",
            "notice-ord": "hey"
        }
        clusternodes.append(node)
        try:
            url = 'http://%s:9100/metrics' % (ip)
            req = urllib.request.Request(url)
            result = urllib.request.urlopen(req)
        except Exception as e:
            print("fetch error")
        # 获取nginx连接数据
        for line in result.readlines():
            if line.startswith("PUBLIC_IP"):
                outconns = line.replace("PUBLIC_IP", "").strip()
                clusterconns.append({
                    "source": "INTERNET",
                    "target": ip,
                    "metrics": {
                        "normal": outconns
                    },
                    "class": "normal"
                })
                totalconn += int(outconns)
            if line.startswith("ng_uptime"):
                mtime = line.replace("ng_uptime", "").strip()
                # timeArray = time.localtime(float(as_num(float(mtime))))
                modifytimelist[ip] = float(as_num(float(mtime)))  # time.strftime("%Y-%m-%d %H:%M:%S", timeArray)
            if line.startswith("ng_process"):
                rtime = line.replace("ng_process", "").strip()
                reloadtimelist[ip] = float(as_num(float(rtime)))
            if line.startswith("private_link"):
                nodeip = re.findall(r'mode="(.+?)"', line)[0]
                nodeconn = re.findall(r' (\d+)', line)[0]
                # totalconn += int(nodeconn)
                if iptolabel(nodeip) != "Other":
                    clusternodes.append({
                        "name": iptolabel(nodeip),
                        "renderer": "focusedChild",
                        "maxVolume": 5000,
                        "class": "normal"
                    })
                    clusterconns.append({
                        "source": ip,
                        "target": iptolabel(nodeip),
                        "metrics": {
                            "normal": nodeconn
                        },
                        "class": "normal"
                    })
    sortednodes = [dict(t) for t in set([tuple(d.items()) for d in clusternodes])]
    # print(modifytimelist)
    # 转换一下notice格式
    for node in sortednodes:
        if "notice-ord" in node:
            if node["name"] in modifytimelist and node["name"] in reloadtimelist:
                mtime = modifytimelist[node["name"]]
                rtime = reloadtimelist[node["name"]]
                severity = 0 if rtime > mtime else 1
                node["notices"] = [{"severity": 0, "title": "配置文件刷新时间：%s" % ts2date(mtime)}, {"severity": severity, "title": "服务进程重启时间：%s" % ts2date(rtime)}]

    NGINX_STATS["nodes"][1]["nodes"] = sortednodes
    NGINX_STATS["nodes"][1]["updated"] = int(time.time() * 1000)
    NGINX_STATS["nodes"][1]["connections"] = clusterconns
    NGINX_STATS["connections"][0]["metrics"]["normal"] = totalconn

    return jsonify(NGINX_STATS)


# 切换A环境
@nginx_bp.route('/switch2a', methods=['POST'])
def set2a():
    reqjson = json.loads(request.data)
    if reqjson["token"] != NGINX_TOKEN:
        abort(403)

    for key in reqjson:
        if key.startswith('ng'):
            if reqjson[key] == True:
                if key in NGINX_NGCMDB:
                    ip = NGINX_NGCMDB[key]
                    switchAB(ip, "A")

    return jsonify({"stat": "ok"})


# 切换B环境
@nginx_bp.route('/switch2b', methods=['POST'])
def set2b():
    reqjson = json.loads(request.data)
    if reqjson["token"] != NGINX_TOKEN:
        abort(403)

    for key in reqjson:
        if key.startswith('ng'):
            if reqjson[key] == True:
                if key in NGINX_NGCMDB:
                    ip = NGINX_NGCMDB[key]
                    switchAB(ip, "B")

    return jsonify({"stat": "ok"})


def executereloadjob_1000091(ip_list):
    kwargs = {
        "bk_app_code": 'wegoops',
        "bk_app_secret": '1186e36a-b156-42f0-8724-e6afd64fdc59',
        "bk_username": 'admin',
        "bk_biz_id": 3,
        "bk_job_id": 1000091,  # 重启nginx任务ID
        "global_vars": [
            {  # 全局变量信息
                "id": 1000096,
                "target_server": {  # 目标服务器
                    "ip_list": ip_list
                }},
        ]
    }
    url = "https://paas.in.szwego.com/api/c/compapi/v2/job/execute_job/"
    rs = requests.post(url, data=json.dumps(kwargs), verify=False)
    info = rs.json()
    # 获取执行的任务ID
    return info["data"]['job_instance_id']


@nginx_bp.route('/reloadserver', methods=['POST'])
def reload_server():
    """
    reload nginx server
    :return:
    """
    instanceid = ""
    reqjson = json.loads(request.data)
    if reqjson["token"] != NGINX_TOKEN:
        abort(403)

    ip_list = []
    for key in reqjson:
        if key.startswith('ng'):
            if reqjson[key] == True:
                if key in NGINX_NGCMDB:
                    ipdic = {}
                    ipdic["bk_cloud_id"] = 0
                    ipdic["ip"] = NGINX_NGCMDB[key]
                    ip_list.append(ipdic)
    if ip_list:
        instanceid = executereloadjob_1000091(ip_list)

    return jsonify({"stat": "ok", "request_id": instanceid})


# 执行作业 上传nacos的配置文件到目标ng服务器
def executeupdatejob_1000087(nginxhosts):
    kwargs = {
        "bk_app_code": 'wegoops',
        "bk_app_secret": '1186e36a-b156-42f0-8724-e6afd64fdc59',
        "bk_username": 'admin',
        "bk_biz_id": 3,
        "bk_job_id": 1000087,
        "global_vars": [
            {  # 全局变量信息
                "id": 1000096,
                "target_server": {  # 目标服务器
                    "ip_list": [{  # 主机IP列表
                        "bk_cloud_id": 0,  # 云区域ID
                        "ip": "**********"  # 主机IP
                    }]
                }},
            {
                "id": 1000100,
                "value": nginxhosts  # 控制scp传输
            }
        ]
    }

    url = "https://paas.in.szwego.com/api/c/compapi/v2/job/execute_job/"
    rs = requests.post(url, data=json.dumps(kwargs), verify=False)
    info = rs.json()
    # 获取执行的任务ID
    return info["data"]['job_instance_id']


@nginx_bp.route('/updateconfig', methods=['POST'])
def update_config():
    """
    update nginx config
    :return:
    """
    instanceid = ""
    reqjson = json.loads(request.data)
    if reqjson["token"] != NGINX_TOKEN:
        abort(403)

    ip_list = []
    for key in reqjson:
        if key.startswith('ng'):
            if reqjson[key] == True:
                if key in NGINX_NGCMDB:
                    ip_list.append(NGINX_NGCMDB[key])
    if ip_list:
        instanceid = executeupdatejob_1000087(",".join(ip_list))

    return jsonify({"stat": "ok", "request_id": instanceid})


@nginx_bp.route('/panic/sync', methods=['GET'])
def sync_paniclist():
    uri = "http://%s/sync-paniclist"
    try:
        query = IpListModel.query.with_entities(IpListModel.private_ip).filter(IpListModel.type == 'cvm', IpListModel.name.like('%负载均衡%')).all()
        private_ip = [line.private_ip for line in query]
        urls = [uri % ip for ip in private_ip]
        result = []
        for url in urls:
            response = requests.get(url)
            status = response.status_code
            result.append({url: status})
        save_to_operation_log(request, action="panic同步", result="200")
        return jsonify({"code": 200, "ip": private_ip, "result": result, "total": len(private_ip)})
    except Exception as e:
        current_app.logger.error("同步paniclist异常：%s" % e)
        save_to_operation_log(request, action="panic同步", result="500")
        return jsonify({"code": 500, "msg": "同步paniclist异常"})
