#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
@Title:     file.py
@Date:      2022/12/5 15:11
@Author:    wanglh
@Desc：     文件上传视图
"""
from io import BytesIO, StringIO

from flask import Blueprint, make_response, request
from werkzeug.utils import secure_filename

from blueprint.cicd import DbtableOperation
from utils import *
from .useritsm import get_name_dictionary

file_blue_bp = Blueprint("file_blue", __name__, url_prefix="/file/api/v1")


@file_blue_bp.route('/cos/push', methods=['GET', 'POST'])
def file_upload_to_cos():
    """
    文件上传，使用的时form-data 表单数据，所以需要通过request.forms（获取文本数据）和request.file（获取文件数据） 两种方式获取
    path结构在cos不存在会自动创建目录结构
    uploaded_file = request.files['file']  # 单个上传
    :return json:
    """
    action = "文件上传"
    creator = request.headers.get('X-User')
    key = current_app.config['COS_BUCKET_PATH'] + request.form['path'] + '/' if request.form.get('path') else current_app.config['COS_BUCKET_PATH'] + '/test/'
    bucket = request.form['bucket'] if request.form.get('bucket') else current_app.config['TX_OLD_BUCKET']
    expire_time = request.form['expiretime'] if request.form.get('expiretime') else None
    domain = request.form["domain"] if request.form.get("domain") else "static.szwego.com"
    current_app.logger.info(f"文件上传参数：{request.form}")
    # 中文 特殊字符 多个/ 处理
    key = re.sub(r'/+', '/', key)
    if re.findall(r'[^\x00-\xff]', key) or re.findall(r'[~!@#$%^&()+*<>,.\[\]\\]', key):  # 匹配中文字符
        current_app.logger.warning("不允许存在中文字符和特殊字符，上传失败")
        save_to_operation_log(request, action=action, result=403)
        return jsonify({"code": 403, "msg": "不允许存在中文字符和特殊字符，上传失败"})
    # 批量上传
    upload_file_path = current_app.config['UPLOAD_PATH']  # 后端文件存储
    if not os.path.exists(upload_file_path):
        os.makedirs(upload_file_path)

    if len(request.files.getlist('file')) >= 1:
        message = []
        for uploaded_file in request.files.getlist('file'):
            filename = secure_filename(uploaded_file.filename)
            file_save_path = os.path.join(upload_file_path, filename)
            if filename != '':
                # 下载到本地
                uploaded_file.save(file_save_path)
                # 上传到cos
                result = object_push_cos(filename=filename, bucket=bucket, path=key)
                # 结果判断
                if 'error' in result:
                    save_to_operation_log(request, action=action, result=500)
                    return jsonify({'code': 500, 'msg': f'上传COS异常，具体信息：{result["error"]}'})
                # 写入数据库
                insert_data = dict(md5=get_file_md5(file_save_path), owner=creator, filesize=os.path.getsize(file_save_path), path=key,
                                   targetname=filename, filename=filename, bucket=bucket, domain=domain, expire_time=expire_time)
                current_app.logger.info(f"写入数据库{CiOperationFileupload.__tablename__}，信息：{insert_data}")

                # # 要更新的字段和值
                # update_data = {
                #     "md5": get_file_md5(file_save_path),
                #     "owner": creator,
                #     "filesize": os.path.getsize(file_save_path),
                #     "targetname": filename,
                #     "expire_time": expire_time,
                # }
                # # 创建 insert 语句，使用 on duplicate key update
                # insert_stmt = insert(CiOperationFileupload.__table__).values(
                #     filename=filename,
                #     bucket=bucket,
                #     domain=domain,
                #     path=key,
                #     **update_data
                # ).on_duplicate_key_update(
                #     **update_data
                # )

                # insert_data = dict(filename=filename,
                #                    bucket=bucket,
                #                    domain=domain,
                #                    path=key, **update_data)
                # print(insert_data)
                try:
                    # db.session.execute(insert_stmt)
                    db.session.execute(CiOperationFileupload.__table__.insert(), [insert_data])
                    message.append({filename: f"上传成功", "ETag": result['ETag'], "status": "上传记录写入数据库成功"})

                except Exception as e:
                    db.session.rollback()
                    db.session.close()
                    current_app.logger.error(f"{filename}上传成功，写入数据库异常：{e}")
                    save_to_operation_log(request, action=action, result=500)
                    return jsonify({'code': 500, 'msg': f"{filename}上传成功，写入数据库异常：{e}"})
            if os.path.exists(file_save_path):  # 文件存在则删除
                os.remove(file_save_path)
        db.session.commit()
        db.session.close()
        current_app.logger.info(message)
        save_to_operation_log(request, action=action, result=200)
        return jsonify({'code': 200, 'msg': message})
    save_to_operation_log(request, action=action, result=404)
    return jsonify({'code': 404, 'msg': '上传文件不存在'})


@file_blue_bp.route('/cos/query', methods=['POST'])
def file_upload_query():
    """
    COS 上传记录查询：ci_operation_fileupload
    :return:
    """
    kw = get_response(request).get('keyword')
    ks = get_name_dictionary(kw)
    keyword = ks if ks else kw
    task_filter = {
        or_(
            CiOperationFileupload.path.like('%{}%'.format(keyword)),  # 根据path路径筛选
            CiOperationFileupload.owner.like('%{}%'.format(keyword)),  # 根据用户筛选
            CiOperationFileupload.filename.like('%{}%'.format(keyword)),
            CiOperationFileupload.targetname.like('%{}%'.format(keyword)),
        )
    }
    FileUpload = DbtableOperation(CiOperationFileupload, request)
    result = FileUpload.operation_query(task_filter)

    print(result)
    return result


@file_blue_bp.route('/cos/pathlist/query', methods=['POST'])
def cos_pathlist_query():
    """
    COS 路径列表查询：ci_operation_fileupload_list
    :return:
    """
    keyword = get_response(request).get('keyword')
    task_filter = {
        or_(
            CiOperationFileuploadPathlist.value.like('%{}%'.format(keyword)),
            CiOperationFileuploadPathlist.description.like('%{}%'.format(keyword))
        )
    }
    is_page = False  # 关闭分页
    FilePathlist = DbtableOperation(CiOperationFileuploadPathlist, request, is_page)
    result = FilePathlist.operation_query(task_filter)
    return result


@file_blue_bp.route('/cos/pathlist/update', methods=['POST'])
def cos_pathlist_update():
    """
    COS 路径列表更新
    :return:
    """
    is_page = False  # 关闭分页
    FilePathlist = DbtableOperation(CiOperationFileuploadPathlist, request, is_page)
    result = FilePathlist.operation_update()
    return result


@file_blue_bp.route('/cos/purge/cache', methods=['POST'])
def cos_purge_cache():
    """
    cos_url：调用刷新次数查询
    :return:
    """
    data_raw = get_response(request)
    if data_raw.get('url'):
        urls = data_raw.get('url').split(',')
        current_app.logger.info(f"刷新的额 url 列表：{urls}")
        result = purge_urls_cache(urls)
        # 调用刷新
        quota_info = describe_purge_quota()
        UrlPurge = json.loads(quota_info).get('UrlPurge')
        for purge in UrlPurge:
            purge["Area"] = "中国境内" if purge.get('Area') == "mainland" else "中国境外"
        return jsonify({"code": 200, "result": json.loads(result), "UrlPurge": UrlPurge})
    current_app.logger.info("没有找到url参数")
    return jsonify({"code": 404, "msg": "not found args url"})


@file_blue_bp.route('/cos/purge/quota')
def cos_purge_quota():
    """
    查询刷新用量配额
    :return:
    """
    quota_info = describe_purge_quota()
    if "Error" in quota_info:
        return jsonify({"code": 500, "msg": quota_info})
    return jsonify({"code": 200, "UrlPurge": json.loads(quota_info).get('UrlPurge')})


@file_blue_bp.route('/cos/compare/etag', methods=['POST'])
def cos_compare_etag():
    """文件Etag对比"""
    data_raw = get_response(request)
    path = data_raw.get('path')
    filename = data_raw.get('filename')
    wego_domain = "https://static.szwego.com"
    cos_domain = "https://xc-static-1251632793.cos.ap-guangzhou.myqcloud.com"
    wego_req = requests.get(wego_domain + path + filename)
    cos_req = requests.get(cos_domain + path + filename)
    if wego_req.status_code == 200 and cos_req.status_code == 200:
        if wego_req.headers.get('Etag') == cos_req.headers.get('Etag'):
            return jsonify({"code": 200, "msg": "文件一致", "result": True})
        return jsonify({"code": 200, "msg": "文件不一致", "result": False})
    elif wego_req.status_code == 404:
        return jsonify({"code": 404, "msg": f"CDN not found {path}{filename}", "result": False})
    elif cos_req.status_code == 404:
        return jsonify({"code": 404, "msg": f"COS not found {path}{filename}", "result": False})


@file_blue_bp.route("/download/complaint/info", methods=['POST', 'GET'])
def download_file():
    from datetime import datetime, timedelta
    dataraw = get_response(request)
    try:
        if dataraw.get("t_create_time"):
            if validate_date_time_string(dataraw.get("t_create_time")) is False:
                raise ValueError("日期格式异常")
        t_create_time = get_time_stamp(dataraw.get("t_create_time", datetime.now() - timedelta(days=1)), "%Y-%m-%d")
        conn = pymysql.Connection(
            user='root',
            password='6lvO8CL8Lw15',
            host='***********',
            database='szwego_shop_0',
            port=3306,
            cursorclass=pymysql.cursors.DictCursor)

        sql = f"""
        SELECT tb_album_complain_info.*,
           (SELECT GROUP_CONCAT(c_complain_img_url SEPARATOR ', ')
            FROM tb_album_complain_proof_img
            WHERE tb_album_complain_proof_img.c_complain_id = tb_album_complain_info.c_complain_id) AS images
        FROM tb_album_complain_info
        WHERE tb_album_complain_info.t_create_time >= '{t_create_time}'
        order by tb_album_complain_info.t_create_time; """

        cursor = conn.cursor()
        cursor.execute(sql)
        data = cursor.fetchall()
        cursor.close()

        df = pd.DataFrame(list(data))
        # 下载为 csv 文件
        csv_buffer = StringIO()
        df.to_csv(csv_buffer, index=False)
        response = make_response(csv_buffer.getvalue())
        response = make_response(csv_buffer.getvalue())
        response.headers["Content-Disposition"] = f"attachment; filename=complain_info_{datetime.now().strftime('%Y%m%d%H%M%S')}.csv"
        response.headers["Content-Type"] = "text/csv"
        return response

        # # 下载为 excel 文件
        # xlsx_buffer = BytesIO()
        # with pd.ExcelWriter(xlsx_buffer, engine='xlsxwriter') as writer:
        #     df.to_excel(writer, index=False, sheet_name='Complain Info')
        #
        # response = make_response(xlsx_buffer.getvalue())
        # response.headers["Content-Disposition"] = f"attachment; filename=complain_info_{datetime.now().strftime('%Y%m%d%H%M%S')}.xlsx"
        # response.headers["Content-Type"] = "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet"
        # return response

    except ValueError as e:
        return jsonify({'error': str(e)}), 500

    except Exception as e:
        return jsonify({"code": 500, "msg": str(e)}), 500
    finally:
        try:
            conn.close()
        except NameError:
            pass
