#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
@Title:     dnsblue.py
@Date:      2022/8/31 16:21
@Author:    wanglh
@Desc：     dns相关解析接口判断与查询
"""

from flask import Blueprint, request

from utils import *
from utils import DnspodRecord

dnsbp = Blueprint("dns_blue", __name__, url_prefix='/dns/api/v1')
base_params = ["Domain", "SubDomain", "Value", "RecordLine", "RecordType"]


@dnsbp.route('/testpsot', methods=['POST'])
def test_post():
    try:
        params_data = get_response(request)
        DnsRecord.query.filter_by(recordid=params_data['RecordId']).update({"is_delete": 2})
        db.session.commit()
        return "删除成功"
    except Exception as e:
        return str(e)


@dnsbp.route('/testget', methods=['POST'])
def test_get():
    try:
        params_data = get_response(request)
        LOCAL_HOST = current_app.config.get('LOCAL_HOST')
        LOCAL_PORT = current_app.config.get('LOCAL_PORT')
        response = requests.request("POST", f"http://{LOCAL_HOST}:{LOCAL_PORT}/dns/api/v1/recordquery", headers={'Content-Type': 'application/json'}, data=json.dumps({"Domain": params_data.get('Domain')
                                                                                                                                                                       }))
        dnsrecord = response.json()
        print(dnsrecord)
        dnsrecords_query = [i["RecordId"] for i in dnsrecord['data'] if "tes" in i["Name"]]  # 通过接口查询RecordIds
        return jsonify({'code': 200, "data": dnsrecords_query})
    except Exception as e:
        return str(e)


@dnsbp.route('/domainquery')
def domain_query():
    """
    域名列表查询
    :return:
    """
    query = DnsDomain.query.all()
    datas = DnsDomain.to_all_json(query)
    try:
        for data in datas:
            data['c_time'] = get_time_stamp(data['c_time'])
        return jsonify({'code': 200, 'data': datas, 'totalCount': len(datas)})
    except Exception as e:
        return jsonify({'code': 500, 'msg': e})


@dnsbp.route('/domainadd', methods=['POST'])
def domain_add():
    """
    域名添加
    :param domain, X-User
    :return:
    """
    action = "域名添加"
    data = get_response(request)
    creator = request.headers.get('X-User')
    try:
        dnsdomain_query = DnsDomain(domain=data['Domain'], creator=creator)
        # 写入数据
        db.session.add(dnsdomain_query)
        db.session.commit()
        save_to_operation_log(request, action=action, result=200)
        return jsonify({'code': 200, 'msg': f'{data["domain"]}添加成功'})
    except Exception as e:
        save_to_operation_log(request, action=action, result=200)
        return jsonify({'code': 500, 'msg': e})


@dnsbp.route('/recordquery', methods=['POST'])
def record_query():
    """
    域名解析记录查询
    :param Domain
    :param Keyword 当前支持搜索主机头和记录值
    :return: record_list
    """
    data = get_response(request)
    domain = data.get('Domain', 'szwego.live')

    try:
        secretId = current_app.config.get('SECRETID')
        secretKey = current_app.config.get('SECRETKEY')
        params = {"Domain": domain, "Keyword": data.get("keyword", '')}
        record_lines = DnspodRecord(secretId, secretKey).record_list(**params)
        return jsonify({'code': 200, 'data': record_lines, 'msg': '获取数据完成', "totalCount": len(record_lines)})

    except Exception as e:
        return jsonify({'code': 500, 'msg': e})


@dnsbp.route('/recordupdate', methods=['POST'])
def record_add():
    """
    实现添加，更新操作
    :return:
    """
    action = "域名解析记录更新"
    params_data = get_response(request)
    if set(base_params) > set(list(params_data.keys())):
        save_to_operation_log(request, action=action, result=403)
        return jsonify({'code': 403, 'msg': '参数不足，需要有下列参数"Domain","SubDomain","Value","RecordLine","RecordType"'})

    domain = params_data.get('Domain', '')
    domain_data = domain_query().get_json()
    domains = [i['domain'] for i in domain_data['data']]
    if domain not in domains:
        save_to_operation_log(request, action=action, result=403)
        return jsonify({'code': 403, 'msg': f'{domain}不在白名单列表，禁止添加修改记录'})

    try:
        secretId = current_app.config.get('SECRETID')
        secretKey = current_app.config.get('SECRETKEY')
        LOCAL_HOST = current_app.config.get('LOCAL_HOST')
        LOCAL_PORT = current_app.config.get('LOCAL_PORT')
        # 添加
        response = requests.request("POST", f"http://{LOCAL_HOST}:{LOCAL_PORT}/dns/api/v1/recordquery", headers={'Content-Type': 'application/json'}, data=json.dumps({"Domain": params_data.get('Domain')
                                                                                                                                                                       }))
        dnsrecord = response.json()
        dnsrecords_query = [i["RecordId"] for i in dnsrecord['data']]  # 通过接口查询RecordIds

        DnsPod = DnspodRecord(secretId, secretKey)
        RecordId = params_data.get("RecordId", '')
        if RecordId in dnsrecords_query:  # 修改
            DnsPod.record_modify(**params_data)
        if RecordId not in dnsrecords_query:  # 添加
            record_result = DnsPod.record_add(**params_data)
            if "error" not in record_result:
                recordid = record_result["RecordId"]
                params_data["recordid"] = recordid
            else:
                save_to_operation_log(request, action=action, result=403)
                return jsonify({'code': 403, "data": record_result})

        # 写入数据库
        sql_data = {k.lower(): v for k, v in params_data.items()}
        sql_data['add_author'] = request.headers.get('X-User')

        insert_sql = f"""INSERT INTO dns_record {str(tuple(sql_data.keys())).replace("'", "")} VALUES {tuple(sql_data.values())} ON DUPLICATE KEY UPDATE domain='{sql_data[
            "domain"]}', subdomain='{sql_data['subdomain']}', value='{sql_data['value']}',
        recordline='{sql_data['recordline']}', recordtype='{sql_data['recordtype']}', recordid={sql_data['recordid']}, add_author='{sql_data['add_author']}';"""

        try:
            db.session.execute(insert_sql)
            db.session.commit()
        except Exception as e:
            db.session.rollback()
            save_to_operation_log(request, action=action, result=500)
            return jsonify({'code': 500, 'msg': "记录更新添加成功，写入数据库失败"})
        finally:
            db.session.close()
        save_to_operation_log(request, action=action, result=200)
        return jsonify({'code': 200, 'msg': "记录更新添加成功，写入数据库成功"})
    except Exception as e:
        save_to_operation_log(request, action=action, result=500)
        return jsonify({'code': 500, 'msg': e})


@dnsbp.route('/recorddelete', methods=['POST'])
def record_delete():
    """
    :param: Domain 域名
    :param: RecordId 记录ID
    :return:
    """
    action = "域名解析记录删除"
    params_data = get_response(request)
    secretId = current_app.config.get('SECRETID')
    secretKey = current_app.config.get('SECRETKEY')
    DnsPod = DnspodRecord(secretId, secretKey)
    try:
        params = {"Domain": params_data['Domain'], 'RecordId': params_data['RecordId']}
        res = DnsPod.record_delete(**params)
        if "error" not in list(res.keys()):
            DnsRecord.query.filter_by(recordid=params_data['RecordId']).update({"is_delete": 2})
            db.session.commit()
        save_to_operation_log(request, action=action, result=200)
        return jsonify({'code': 200, **res})
    except Exception as e:
        save_to_operation_log(request, action=action, result=500)
        return jsonify({'code': 500, 'msg': str(e)})
