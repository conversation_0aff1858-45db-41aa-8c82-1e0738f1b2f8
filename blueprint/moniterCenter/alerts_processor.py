# !/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
@Title:     alerts_processor.py
@Date:      2025/4/3 16:40
@Author:    wanglh
@Desc:
"""

import hashlib
import re
import traceback
from datetime import datetime, timezone
from typing import Any, Dict
from zoneinfo import ZoneInfo

from flask import current_app, jsonify

from model import MonitoringCenterAlert, db


class AlertProcessor:

    CUSTOM_STATUS_MAP = {
        1: 'ALARM',  # 告警
        0: 'OK',  # 恢复
        5: 'NO_DATA',  # 数据不足
        4: 'NO_CONF'  # 已失效
    }

    CUSTOM_STATUS_STR_MAP = {
        'firing': 'ALARM',
        'resolved': 'OK',
        "warning": "ALARM",
        "major": "ALARM",
    }

    ALERT_STATUS_MAP = {
        'firing': 'ALARM',
        'resolved': 'OK',
        'suppressed': 'NO_CONF'
    }

    CHINA_TZ = ZoneInfo('Asia/Shanghai')
    UTC_TZ = timezone.utc

    @staticmethod
    def parse_time_to_china_tz(time_str: str, assume_tz=None) -> str:
        """
        解析各种格式的时间字符串为东八区时间字符串

        Args:
            time_str: 时间字符串
            assume_tz: 当时间字符串没有时区信息时，假设的时区。默认为 None，表示使用 UTC

        Returns:
            str: 格式为 "%Y-%m-%d %H:%M:%S" 的东八区时间字符串
        """
        if not time_str:
            return datetime.now(AlertProcessor.CHINA_TZ).strftime("%Y-%m-%d %H:%M:%S")

        if 'Z' in time_str or '+' in time_str or '-' in time_str:
            try:

                parsed_time = datetime.fromisoformat(
                    time_str.replace('Z', '+00:00'))
                china_time = parsed_time.astimezone(AlertProcessor.CHINA_TZ)
                return china_time.strftime("%Y-%m-%d %H:%M:%S")
            except ValueError:
                pass

        common_formats = [
            '%Y-%m-%d %H:%M:%S',
            '%Y-%m-%d %H:%M:%S.%f',
            '%Y-%m-%dT%H:%M:%S',
            '%Y-%m-%dT%H:%M:%S.%f',
            '%Y/%m/%d %H:%M:%S',
            '%Y%m%d%H%M%S',
        ]

        for fmt in common_formats:
            try:
                parsed_time = datetime.strptime(time_str, fmt)

                if assume_tz:
                    parsed_time = parsed_time.replace(tzinfo=assume_tz)
                else:
                    parsed_time = parsed_time.replace(
                        tzinfo=AlertProcessor.UTC_TZ)
                china_time = parsed_time.astimezone(AlertProcessor.CHINA_TZ)
                return china_time.strftime("%Y-%m-%d %H:%M:%S")
            except ValueError:
                continue

        current_app.logger.warning(
            f"无法解析时间字符串: {time_str}，格式不受支持。使用当前时间作为替代。"
        )
        return datetime.now(AlertProcessor.CHINA_TZ).strftime("%Y-%m-%d %H:%M:%S")

    @staticmethod
    def get_original_alert_title(alert_title: str) -> str:
        """
        从告警标题中提取原始标题
        """
        if not alert_title:
            return alert_title

        title = alert_title.replace('告警恢复:', '').strip()
        return title

    @staticmethod
    def generate_fingerprint(data: Dict[str, Any]) -> str:
        """
        生成告警指纹
        """
        sorted_items = sorted(data.items())
        fingerprint_str = ":".join(str(value or '')
                                   for _, value in sorted_items)
        full_hash = hashlib.sha256(fingerprint_str.encode()).hexdigest()
        return full_hash[:16]

    @staticmethod
    def normalize_customer_alert(alert: Dict[str, Any]) -> Dict[str, Any]:
        """
        规范化自定义告警数据
        """

        def _process_time(time_str: str) -> str:
            has_tz_indicator = bool(re.search(r'[Z+-]', time_str))
            assume_tz = None if has_tz_indicator else AlertProcessor.CHINA_TZ
            return AlertProcessor.parse_time_to_china_tz(time_str, assume_tz)

        alert_time = _process_time(alert.get('alertTime', ''))
        alertTitle = alert.get('alertTitle', '')
        alertObject = alert.get('alertObject', '')

        if "告警恢复" in alertTitle:
            alertTitle = alertTitle.split("告警恢复:")[1].strip()
            recovered_time = AlertProcessor.extract_alert_time_from_message(
                alert.get('alertContent', ''))
            if recovered_time:
                alert_time = _process_time(recovered_time)
            ends_at = _process_time(alert.get('alertTime', ''))
        if "自定义告警:" in alertTitle:
            alertTitle = alertTitle.split("自定义告警:")[1].strip()

        external_dict = {
            "alertObject": alertObject,
        }

        external_id = AlertProcessor.generate_fingerprint(external_dict)

        fingerprint_str = {
            "external_id": external_id,
            "alertTime": alert_time,
        }

        fingerprint = AlertProcessor.generate_fingerprint(fingerprint_str)

        labels = {
            "alert_title": alertTitle,
            "alert_object": alertObject,
            "alert_service": alert.get("alertService", ""),
            "alert_name": alert.get("alertName", ""),
        }

        description_parts = [
            "【告警基本信息】",
            f"告警标题: {alertTitle}",
            f"告警对象: {alertObject}",
            f"告警时间: {alert_time}",
            "",
            "【告警详情】",
            f"告警内容: {alert.get('alertContent', '')}",
            "",
            "【相关人员】",
            f"通知对象: {alert.get('alertName', '')}"
        ]

        if alert.get('alertService'):
            description_parts.extend([
                "",
                "【服务信息】",
                f"相关服务: {alert.get('alertService', '')}"
            ])

        description = "\n".join(description_parts)

        alert_status = AlertProcessor.CUSTOM_STATUS_MAP.get(
            alert.get('status', 1), 'ALARM')

        if "告警恢复" in alertTitle:
            return {
                "source": "custom",
                "fingerprint": fingerprint,
                "external_id": external_id,
                "status": "OK",
                "ends_at": ends_at,
                "alarm_status": alert_status
            }

        return {
            "source": "custom",
            "fingerprint": fingerprint,
            "external_id": external_id,
            "status": alert_status,
            "severity": "warning",
            "summary": alertTitle,
            "description": description,
            "labels": labels,
            "annotations": {
                "alert_source": "custom_webhook",
                "alert_type": "custom"
            },
            "starts_at": alert_time,
            "ends_at": None,
            "alarm_status": alert_status
        }

    @staticmethod
    def validate_customer_alert(alert: Dict[str, Any]) -> bool:
        """
        验证自定义告警数据的必需字段
        """
        required_fields = ['alertTitle', 'alertTime']
        return all(field in alert and alert[field] for field in required_fields)

    @staticmethod
    def _parse_alert_time(time_str: str) -> str:
        """处理告警时间字符串，返回格式化的时间"""
        if not time_str:
            return datetime.now(AlertProcessor.CHINA_TZ).strftime("%Y-%m-%d %H:%M:%S")

        try:
            if '.' in time_str:
                time_str = time_str.split('.')[0] + 'Z'

            parsed_time = datetime.fromisoformat(
                time_str.replace('Z', '+00:00'))
            return parsed_time.astimezone(AlertProcessor.CHINA_TZ).strftime("%Y-%m-%d %H:%M:%S")
        except (ValueError, TypeError):
            return datetime.now(AlertProcessor.CHINA_TZ).strftime("%Y-%m-%d %H:%M:%S")

    @staticmethod
    def _normalize_alertmanager_alert(alert, source):
        """
        处理 Prometheus Alertmanager 告警的通用方法
        :param alert: 告警数据
        :param source: 告警来源（pinpoint/rocketmq等）
        :return: 标准化的告警数据
        """
        labels = alert.get('labels', {})
        annotations = alert.get('annotations', {})

        starts_at = AlertProcessor._parse_alert_time(alert.get('startsAt', ''))

        ends_at = None
        if alert.get('status') == 'resolved':
            ends_at = AlertProcessor._parse_alert_time(alert.get('endsAt', ''))

        external_id = alert.get("fingerprint")

        fingerprint_data = {
            "external_id": external_id,
            'alertname': labels.get('alertname'),
            'source': source,
            'starts_at': starts_at
        }

        fingerprint = AlertProcessor.generate_fingerprint(fingerprint_data)

        alert_status = AlertProcessor.ALERT_STATUS_MAP.get(
            alert.get('status'), 'ALARM')

        description_parts = ["【告警基本信息】"]

        important_labels = ['alertname', 'severity']
        for key in important_labels:
            if key in labels:
                description_parts.append(f"{key}: {labels[key]}")

        source_specific_labels = {
            'pinpoint': ['appname', 'type'],
            'rocketmq': ['topic', 'group']
        }.get(source, [])

        appname = ''
        pinpoint_type = ''
        for key in source_specific_labels:
            if key in labels:
                description_parts.append(f"{key}: {labels[key]}")
            if key == 'appname':
                appname = labels[key]
            if key == 'type':
                pinpoint_type = labels[key]

        serviceType = ''
        other_labels = set(labels.keys()) - \
            set(important_labels) - set(source_specific_labels)
        if other_labels:
            description_parts.extend(["", "【其他标签信息】"])
            for key in sorted(other_labels):
                if key not in ['alert_type', 'alertStatus']:
                    description_parts.append(f"{key}: {labels[key]}")
                if key == 'serviceType':
                    serviceType = labels[key]

        if annotations:
            description_parts.extend(["", "【告警详情】"])
            for key, value in annotations.items():
                description_parts.append(f"{key}: {value}")

            if source == 'pinpoint':
                annotations["extra_info"] = {
                    "jump_url": f"https://pinpoint.in.szwego.com/main/{appname}@{serviceType}/{pinpoint_type}"}

        description = "\n".join(description_parts)

        return {
            "source": source,
            "fingerprint": fingerprint,
            "external_id": external_id,
            "status": alert_status,
            "severity": labels.get('severity', 'warning'),
            "summary": labels.get('alertname'),
            "description": description,
            "labels": labels,
            "annotations": annotations,
            "starts_at": starts_at,
            "ends_at": ends_at,
            "alarm_status": "ALARM" if alert.get('status') == 'firing' else "OK"
        }

    @staticmethod
    def _normalize_general_pinpoint_alert(alert):
        return AlertProcessor._normalize_alertmanager_alert(alert, 'pinpoint')

    @staticmethod
    def _normalize_general_rocketmq_alert(alert):
        return AlertProcessor._normalize_alertmanager_alert(alert, 'rocketmq')

    @staticmethod
    def extract_alert_time_from_message(message: str) -> str:
        """
        从告警消息中提取告警时间
        Args:
            message: 告警消息字符串
        Returns:
            str: 告警时间字符串，格式为 "%Y-%m-%d %H:%M:%S"
        """
        try:

            alert_time_marker = "告警时间：\t"
            start_index = message.find(alert_time_marker)
            if start_index == -1:
                return ""

            start_index += len(alert_time_marker)
            end_index = message.find("\n", start_index)

            if end_index == -1:
                return message[start_index:].strip()

            return message[start_index:end_index].strip()
        except Exception:
            return ""

    @staticmethod
    def _normalize_general_custom_alert(alert: Dict[str, Any]) -> Dict[str, Any]:
        """
        处理自定义类型的告警

        Args:
            alert: 告警数据字典

        Returns:
            Dict[str, Any]: 标准化后的告警数据
        """

        def _process_time(time_str: str) -> str:
            has_tz_indicator = bool(re.search(r'[Z+-]', time_str))
            assume_tz = None if has_tz_indicator else AlertProcessor.CHINA_TZ
            return AlertProcessor.parse_time_to_china_tz(time_str, assume_tz)

        def _build_description() -> str:
            """构建告警描述信息"""
            parts = [
                "【告警基本信息】",
                f"告警标题: {alert.get('alert_title', 'Unknown')}",
                f"告警对象: {alert.get('alert_object', 'Unknown')}",
                f"告警级别: {alert.get('alert_severity', 'warning')}",
                "",
                "【告警详情】",
                f"描述: {alert.get('alert_message', 'No message provided')}",
                "",
                "【业务信息】",
                f"业务ID: {alert.get('business_ids', 'Unknown')}",
                f"通知方式: {alert.get('notify_methods', 'Unknown')}"
            ]

            extra_info = alert.get('extra_info', {})
            if extra_info:
                parts.extend(["", "【额外信息】"])
                parts.extend(f"{key}: {value}" for key,
                             value in extra_info.items())

            return "\n".join(parts)

        def _get_alert_status() -> str:
            """确定告警状态"""
            severity = alert.get('alert_severity', 'warning')
            if severity == 'resolved':
                return 'OK'
            try:
                if str(alert.get('status', '')) in {"1", "2", "3", "4"}:
                    return AlertProcessor.CUSTOM_STATUS_MAP.get(int(alert.get('status')), 'ALARM')
                if str(alert.get('status', '')) in {"firing", "resolved"}:
                    return AlertProcessor.CUSTOM_STATUS_STR_MAP.get(alert.get('status'), 'ALARM')
                return "ALARM"
            except Exception as e:
                current_app.logger.info(f"处理自定义告警的状态字段出错: {str(e)}, 获取的状态字段：{alert.get('status')}")
                current_app.logger.info(traceback.format_exc())
                return 'ALARM'

        starts_at = _process_time(alert.get('starts_at_str', ''))
        current_app.logger.info(f"自定义告警开始/结束时间: {starts_at}")

        original_title = AlertProcessor.get_original_alert_title(
            alert.get('alert_title', ''))
        external_data = {
            "alert_title": original_title,
            "alert_object": alert.get('alert_object'),
        }
        external_id = AlertProcessor.generate_fingerprint(external_data)

        alert_time = starts_at
        if "告警恢复" in alert.get('alert_title', ''):
            recovered_time = AlertProcessor.extract_alert_time_from_message(
                alert.get('alert_message', ''))
            current_app.logger.info(f"自定义告警恢复获取到的告警时间: {recovered_time}")
            if recovered_time:
                alert_time = _process_time(recovered_time)

        fingerprint = AlertProcessor.generate_fingerprint({
            "external_id": external_id,
            "alert_time": alert_time
        })

        alert_status = _get_alert_status()

        if "告警恢复" in alert.get('alert_title', ''):
            return {
                "source": "custom",
                "fingerprint": fingerprint,
                "external_id": external_id,
                "status": alert_status,
                "ends_at": starts_at,
                "alarm_status": alert_status
            }

        return {
            "source": "custom",
            "fingerprint": fingerprint,
            "external_id": external_id,
            "status": alert_status,
            "severity": alert.get('alert_severity', 'warning'),
            "summary": alert.get('alert_title'),
            "description": _build_description(),
            "labels": {
                'alert_object': alert.get('alert_object'),
                'business_ids': alert.get('business_ids'),
                'severity': alert.get('alert_severity'),
                'notify_methods': alert.get('notify_methods')
            },
            "annotations": {
                'description': alert.get('alert_message'),
                'extra_info': alert.get('extra_info', {})
            },
            "starts_at": starts_at,
            "ends_at": None,
            "alarm_status": alert_status
        }

    @staticmethod
    def process_general_alerts(alerts):
        """
        处理通用告警接口的告警列表
        """
        processed_alerts = []
        for alert in alerts:
            try:
                normalized_alert = AlertProcessor.normalize_general_alert(
                    alert)
                processed_alert = AlertProcessor.process_alert(
                    normalized_alert)
                processed_alerts.append(processed_alert)
            except Exception as e:
                current_app.logger.exception(f"处理告警失败: {str(e)}")
                raise
        return processed_alerts

    @staticmethod
    def normalize_general_alert(alert):
        """
        规范化通用告警接口的数据
        """
        alert_type = alert.get('alert_type') or alert.get(
            "labels", {}).get("alert_type", "unknown")

        if alert_type == 'pinpoint':
            return AlertProcessor._normalize_general_pinpoint_alert(alert)
        elif alert_type == 'rocketmq':
            return AlertProcessor._normalize_general_rocketmq_alert(alert)
        elif alert_type == 'custom':
            return AlertProcessor._normalize_general_custom_alert(alert)
        else:
            raise ValueError(f"Unsupported alert type: {alert_type}")

    @staticmethod
    def process_alert(normalized_alert):
        """
        处理规范化后的告警，进行去重和状态管理
        """

        current_time = datetime.now().strftime("%Y-%m-%d %H:%M:%S")

        fingerprint = normalized_alert.get('fingerprint')
        current_app.logger.info(f"开始处理告警, fingerprint: {fingerprint}")

        existing_alert = MonitoringCenterAlert.query.filter_by(
            fingerprint=fingerprint
        ).order_by(
            MonitoringCenterAlert.starts_at.desc()
        ).first()
        try:
            if existing_alert:
                current_app.logger.info(
                    f"找到现有告警记录 ID: {existing_alert.id}, "
                    f"当前状态: {existing_alert.status}, "
                    f"新状态: {normalized_alert.get('status')}"
                )

                update_fields = {
                    key: value for key, value in normalized_alert.items()
                    if key not in ['fingerprint', 'id']
                }

                current_app.logger.info(f"更新字段: {list(update_fields.keys())}")

                for key, value in update_fields.items():
                    old_value = getattr(existing_alert, key, None)
                    if old_value != value:
                        current_app.logger.debug(
                            f"字段 {key} 值变更: {old_value} -> {value}"
                        )
                    setattr(existing_alert, key, value)

                existing_alert.updated_at = current_time
                db.session.commit()

                current_app.logger.info(
                    f"告警记录更新完成 ID: {existing_alert.id}, "
                    f"更新时间: {existing_alert.updated_at}"
                )
                return existing_alert
            else:
                current_app.logger.info(
                    f"未找到现有告警记录，创建新记录. "
                    f"Summary: {normalized_alert.get('summary')}"
                )

                new_alert = MonitoringCenterAlert(**normalized_alert)
                new_alert.created_at = current_time
                new_alert.updated_at = current_time
                new_alert.severity = normalized_alert.get('severity', 'warning')

                db.session.add(new_alert)
                db.session.commit()

                current_app.logger.info(
                    f"新告警记录创建完成 ID: {new_alert.id}, "
                    f"创建时间: {new_alert.created_at}"
                )
                return new_alert
        except Exception as e:
            current_app.logger.exception(f"处理告警时发生错误: {str(e)}")
            raise
        finally:
            db.session.close()


def customer_alert_for_alarm_subscription(payload):
    current_app.logger.info(f"接收到的自定义告警数据: {payload}")

    if not payload or not all(key in payload for key in ['alertTitle', 'alertTime']):
        current_app.logger.exception(f"无效的请求数据: {payload}")
        return jsonify({'status': 'error', 'message': 'Invalid payload - missing required fields'}), 400

    current_app.logger.info("开始规范化自定义告警数据")
    normalized_alert = AlertProcessor.normalize_customer_alert(payload)
    current_app.logger.info(f"规范化后的数据: {normalized_alert}")

    current_app.logger.info("开始处理告警")
    alert = AlertProcessor.process_alert(normalized_alert)
    current_app.logger.info(f"告警处理完成: {alert.id}")

    return jsonify({
        'status': 'success',
        'alert_id': alert.id,
        'fingerprint': alert.fingerprint
    }), 200


def customer_alert_for_send_webhook_general(payload):
    try:
        if not payload or 'alerts' not in payload:
            current_app.logger.info(f"无效的请求数据: {payload}")
            current_app.logger.info(traceback.format_exc())
            return jsonify({
                'code': 400,
                'message': 'Invalid payload - missing alerts field'
            }), 400

        alerts = payload.get('alerts', [])
        if not alerts:
            current_app.logger.info("告警列表为空")
            current_app.logger.info(traceback.format_exc())
            return jsonify({
                'code': 400,
                'message': 'Empty alerts list'
            }), 400

        current_app.logger.info("开始处理告警")
        processed_alerts = AlertProcessor.process_general_alerts(alerts)

        results = []
        for alert in processed_alerts:
            result = {
                "alert_title": alert.summary,
                "alert_type": alert.source,
                "send_results": [{
                    "result": "告警保存成功",
                    "alert_id": alert.id
                }]
            }
            results.append(result)

        response = {
            "code": 200,
            "message": "告警处理成功",
            "results": results
        }

        current_app.logger.info(f"告警处理完成，共处理 {len(processed_alerts)} 条告警")
        return jsonify(response), 200

    except Exception as e:
        current_app.logger.exception("处理告警时发生错误")
        return jsonify({
            'code': 500,
            'message': str(e)
        }), 500
