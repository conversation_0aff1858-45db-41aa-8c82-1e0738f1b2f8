# !/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
@Title:     alerts_center.py
@Date:      2025/4/3 16:47
@Author:    wanglh
@Desc:      告警中心API接口
"""

from datetime import datetime, timedelta
from typing import Dict, List, Optional, Tuple, Set, Any
import time
from functools import lru_cache

from flask import Blueprint, current_app, jsonify, request
from sqlalchemy import case, func

from model import MonitoringCenterAlert, TencentProductList, TencentCateProduct, db

alerts_center_bp = Blueprint(
    'alerts_center_api', __name__, url_prefix='/alertscenter/api/v1')

# 常量定义
HEALTH_STATUS_PRIORITY = {
    'healthy': 1,
    'info': 2,
    'warning': 3,
    'error': 4,
    'critical': 5
}

# 产品名称映射表（提取为常量以便复用和维护）
PRODUCT_NAME_MAPPING = {
    # 中文到英文映射
    '云服务器': 'cvm',
    '云数据库': 'rds',
    '内容分发网络': 'cdn',
    '负载均衡': 'clb',
    '云硬盘': 'cbs',
    '私有网络': 'vpc',
    '对象存储': 'cos',
    '云监控': 'monitor',
    '弹性伸缩': 'as',
    '云安全中心': 'cwp',
    # 英文别名映射
    'ecs': 'cvm',
    'rds-mysql': 'rds',
    'rds-postgres': 'rds',
    'mysql': 'rds',
    'postgresql': 'rds',
    'redis': 'redis',
    'mongodb': 'mongodb',
    'elasticsearch': 'es',
    'kafka': 'ckafka',
    'mq': 'cmq',
    'api-gateway': 'apigateway',
    'scf': 'scf',
    'tke': 'tke',
    'tcr': 'tcr'
}


def format_datetime(dt: Optional[datetime]) -> Optional[str]:
    """格式化日期时间为字符串

    Args:
        dt: 日期时间对象

    Returns:
        格式化后的日期时间字符串，如果输入为None则返回None
    """
    return dt.strftime("%Y-%m-%d %H:%M:%S") if dt else None


@lru_cache(maxsize=1)
def get_product_category_mapping() -> Dict[str, str]:
    """获取产品分类映射（带缓存）

    Returns:
        产品名称到分类的映射字典
    """
    try:
        category_products = TencentCateProduct.query.all()
        return {cp.product.lower(): cp.categories for cp in category_products}
    except Exception as e:
        current_app.logger.error(f"获取产品分类映射失败: {e}")
        return {}


def validate_panel_request_params(start_time: str, end_time: str, date: Optional[str] = None) -> Tuple[bool, Optional[str]]:
    """验证健康面板请求参数

    Args:
        start_time: 开始时间字符串
        end_time: 结束时间字符串
        date: 日期字符串

    Returns:
        (是否有效, 错误消息)
    """
    if not start_time or not end_time:
        return False, "开始时间和结束时间为必填参数"

    try:
        start_dt = datetime.strptime(start_time, "%Y-%m-%d %H:%M:%S")
        end_dt = datetime.strptime(end_time, "%Y-%m-%d %H:%M:%S")

        if start_dt >= end_dt:
            return False, "开始时间必须早于结束时间"

        # 检查时间范围是否合理（不超过30天）
        if (end_dt - start_dt).days > 30:
            return False, "查询时间范围不能超过30天"

    except ValueError as e:
        return False, f"时间格式错误: {e}"

    return True, None


def map_status_to_health(status: str, severity: str) -> str:
    """将数据库状态映射为前端健康状态

    Args:
        status: 告警状态
        severity: 严重程度

    Returns:
        健康状态字符串
    """
    if status == 'OK':
        return 'healthy'
    elif status == 'ALARM':
        if severity == 'critical':
            return 'critical'
        elif severity == 'warning':
            return 'warning'
        elif severity == 'minor':
            return 'warning'
        else:
            return 'error'
    elif status == 'NO_DATA':
        return 'warning'
    elif status == 'NO_CONF':
        return 'warning'
    else:
        return 'warning'


def parse_policy_name(policy_name: str) -> Tuple[Optional[str], Optional[str], Optional[str], Optional[str], Optional[str]]:
    """解析policy_name，支持3-5段式命名规范

    Args:
        policy_name: 格式为 product-business_line-criticality[-severity[-purpose]]

    Returns:
        (product, business_line, criticality, severity, purpose)
        如果解析失败返回 (None, None, None, None, None)
    """
    # 检查空值
    if not policy_name or policy_name in ('', 'null', 'NULL'):
        current_app.logger.debug(f"policy_name为空或无效: {policy_name}")
        return None, None, None, None, None

    # 检查是否包含分隔符
    if '-' not in policy_name:
        current_app.logger.debug(f"policy_name格式错误，缺少分隔符'-': {policy_name}")
        return None, None, None, None, None

    parts = policy_name.split('-')

    # 验证至少包含3个段
    if len(parts) < 3:
        current_app.logger.debug(f"policy_name格式不符合3段式规范，实际段数: {len(parts)}, policy_name: {policy_name}")
        return None, None, None, None, None

    # 解析各个段，支持3-5段式
    product = parts[0].strip() if len(parts) > 0 else None
    business_line = parts[1].strip() if len(parts) > 1 else None
    criticality = parts[2].strip() if len(parts) > 2 else None
    severity = parts[3].strip() if len(parts) > 3 else None
    purpose = parts[4].strip() if len(parts) > 4 else None

    # 验证必要字段不为空
    if not product or not business_line or not criticality:
        current_app.logger.debug(f"policy_name必要字段为空: product={product}, business_line={business_line}, criticality={criticality}")
        return None, None, None, None, None

    return product, business_line, criticality, severity, purpose


def normalize_and_match_product(product_name: str, product_category_map: Dict[str, str]) -> Optional[str]:
    """标准化产品名称并进行模糊匹配

    Args:
        product_name: 原始产品名称
        product_category_map: 产品分类映射字典

    Returns:
        匹配到的产品分类，如果未匹配到则返回None
    """
    if not product_name:
        return None

    # 转换为小写
    normalized_name = product_name.lower().strip()

    # 1. 直接匹配
    if normalized_name in product_category_map:
        return product_category_map[normalized_name]

    # 2. 中英文映射匹配
    if normalized_name in PRODUCT_NAME_MAPPING:
        mapped_name = PRODUCT_NAME_MAPPING[normalized_name]
        if mapped_name in product_category_map:
            return product_category_map[mapped_name]

    # 3. 模糊匹配 - 检查是否包含关键词
    for key, category in product_category_map.items():
        if normalized_name in key or key in normalized_name:
            current_app.logger.debug(f"模糊匹配成功: {product_name} -> {key} -> {category}")
            return category

    # 4. 通过映射表进行反向模糊匹配
    for original, mapped in PRODUCT_NAME_MAPPING.items():
        if normalized_name in original.lower() or original.lower() in normalized_name:
            if mapped in product_category_map:
                current_app.logger.debug(f"反向模糊匹配成功: {product_name} -> {original} -> {mapped} -> {product_category_map[mapped]}")
                return product_category_map[mapped]

    return None


def normalize_statuses(statuses_list: List[str]) -> List[str]:
    """标准化状态数组

    Args:
        statuses_list: 状态列表

    Returns:
        标准化后的状态列表
    """
    if not statuses_list:
        return ['healthy']

    # 转换为集合去重
    statuses_set = set(statuses_list)

    # 如果只有healthy状态或为空，返回healthy
    if not statuses_set or statuses_set == {'healthy'}:
        return ['healthy']

    # 如果包含healthy和其他状态，移除healthy
    if 'healthy' in statuses_set and len(statuses_set) > 1:
        statuses_set.remove('healthy')

    # 返回排序后的状态列表，确保输出一致性
    return sorted(list(statuses_set), key=lambda x: HEALTH_STATUS_PRIORITY.get(x, 999))


def generate_date_range(date: Optional[str], end_time: str, days: int = 7) -> List[str]:
    """生成日期范围

    Args:
        date: 基准日期字符串
        end_time: 结束时间字符串
        days: 天数，默认7天

    Returns:
        日期字符串列表
    """
    try:
        end_date = datetime.strptime(date, "%Y-%m-%d") if date else datetime.strptime(end_time.split()[0], "%Y-%m-%d")
    except (ValueError, IndexError):
        end_date = datetime.now()

    return [(end_date - timedelta(days=i)).strftime("%Y-%m-%d") for i in range(days)]


def fetch_history_alerts(start_time: str, end_time: str, today_start: str) -> List[Any]:
    """查询历史告警数据（排除今日数据）

    Args:
        start_time: 开始时间
        end_time: 结束时间
        today_start: 今日开始时间（用于排除今日数据）

    Returns:
        历史告警列表
    """
    try:
        # 查询历史数据，排除今日数据
        history_alerts = MonitoringCenterAlert.query.filter(
            MonitoringCenterAlert.starts_at >= start_time,
            MonitoringCenterAlert.starts_at <= end_time,
            # MonitoringCenterAlert.starts_at < today_start  # 排除今日数据
        ).all()

        current_app.logger.info(f"查询到历史告警数据: {len(history_alerts)}条")
        return history_alerts

    except Exception as e:
        current_app.logger.error(f"查询历史告警数据失败: {e}")
        return []


def fetch_today_alerts(today_start: str, today_end: str) -> List[Any]:
    """查询今日告警数据（独立于前端时间参数）

    Args:
        today_start: 今日开始时间
        today_end: 今日结束时间

    Returns:
        今日告警列表
    """
    try:
        # 独立查询今日数据，不受前端时间参数影响
        today_alerts = MonitoringCenterAlert.query.filter(
            MonitoringCenterAlert.starts_at >= today_start,
            MonitoringCenterAlert.starts_at <= today_end,
        ).all()

        current_app.logger.info(f"查询到今日告警数据: {len(today_alerts)}条")
        return today_alerts

    except Exception as e:
        current_app.logger.error(f"查询今日告警数据失败: {e}")
        return []


def process_alert_data(alert: Any, product_category_map: Dict[str, str]) -> Optional[Tuple[str, str, str, str]]:
    """处理单个告警数据，提取产品信息

    Args:
        alert: 告警对象
        product_category_map: 产品分类映射

    Returns:
        (product, product_category, health_status, alert_date) 或 None
    """
    # 提取policy_name
    if not alert.labels or not isinstance(alert.labels, dict):
        return None

    policy_name = alert.labels.get('policy_name')
    if not policy_name:
        return None

    # 解析policy_name
    product, business_line, criticality, _, _ = parse_policy_name(policy_name)
    if not product or not business_line or not criticality:
        return None

    # 获取产品分类
    product_category = normalize_and_match_product(product, product_category_map)
    if not product_category:
        product_category = business_line

    # 映射健康状态
    health_status = map_status_to_health(alert.status, alert.severity)

    # 获取告警日期
    alert_date = alert.starts_at.strftime("%Y-%m-%d")

    return product, product_category, health_status, alert_date


def build_history_data(history_alerts: List[Any], product_category_map: Dict[str, str]) -> Tuple[Dict[str, Set[str]], Dict[str, Dict[str, Any]]]:
    """构建历史数据结构

    Args:
        history_alerts: 历史告警列表
        product_category_map: 产品分类映射

    Returns:
        (category_stats, product_stats)
    """
    category_stats = {}
    product_stats = {}

    for alert in history_alerts:
        result = process_alert_data(alert, product_category_map)
        if not result:
            continue

        product, product_category, health_status, alert_date = result

        # 统计分类数据
        if product_category not in category_stats:
            category_stats[product_category] = set()
        category_stats[product_category].add(product)

        # 统计产品数据
        product_key = f"{product_category}_{product}"
        if product_key not in product_stats:
            product_stats[product_key] = {
                'product_name': product,
                'category': product_category,
                'fingerprints': set(),
                'date_data': {}
            }

        product_stats[product_key]['fingerprints'].add(alert.fingerprint)

        # 按日期存储历史数据
        date_key = f"date_{alert_date.replace('-', '_')}"
        if date_key not in product_stats[product_key]['date_data']:
            product_stats[product_key]['date_data'][date_key] = {
                'statuses': set(),
                'hasData': True,
                'timestamp': format_datetime(alert.starts_at)
            }
        product_stats[product_key]['date_data'][date_key]['statuses'].add(health_status)

    return category_stats, product_stats


def build_today_status_data(today_alerts: List[Any], product_category_map: Dict[str, str]) -> List[Dict[str, Any]]:
    """构建今日状态数据

    Args:
        today_alerts: 今日告警列表
        product_category_map: 产品分类映射

    Returns:
        今日状态数据列表
    """
    today_status_data = {}

    for alert in today_alerts:
        result = process_alert_data(alert, product_category_map)
        if not result:
            continue

        product, product_category, health_status, _ = result

        # 构建today_status数据
        product_key = f"{product_category}_{product}"
        if product_key not in today_status_data:
            today_status_data[product_key] = {
                'product_name': product,
                'category': product_category,
                'statuses': set(),
                'hasData': True,
                'timestamp': format_datetime(alert.starts_at)
            }

        today_status_data[product_key]['statuses'].add(health_status)

    # 转换为列表格式
    return [
        {
            'product_name': data['product_name'],
            'category': data['category'],
            'statuses': normalize_statuses(list(data['statuses'])),
            'hasData': data['hasData'],
            'timestamp': data['timestamp']
        }
        for data in today_status_data.values()
    ]


def build_response_items(category_stats: Dict[str, Set[str]], product_stats: Dict[str, Dict[str, Any]], date_range: List[str]) -> List[Dict[str, Any]]:
    """构建响应的items数据

    Args:
        category_stats: 分类统计数据
        product_stats: 产品统计数据
        date_range: 日期范围

    Returns:
        items数据列表
    """
    items = []

    # 按分类分组
    for category in sorted(category_stats.keys()):
        # 添加分类行
        category_item = {
            'id': category,
            'product_name': "",
            'category': category,
            'isCategory': True
        }

        # 为分类项添加所有日期字段
        for date_str in date_range:
            date_key = f"date_{date_str.replace('-', '_')}"
            category_item[date_key] = {
                'statuses': normalize_statuses([]),
                'hasData': False,
                'timestamp': None
            }

        items.append(category_item)

        # 添加该分类下的产品
        category_products = [k for k in product_stats.keys() if product_stats[k]['category'] == category]
        for product_key in sorted(category_products):
            product = product_stats[product_key]

            # 获取该产品的第一个fingerprint作为id
            product_fingerprint = list(product['fingerprints'])[0] if product['fingerprints'] else None

            # 构建产品项
            product_item = {
                'id': product_fingerprint if product_fingerprint else f"{category}_{product['product_name']}",
                'product_name': product['product_name'],
                'category': category,
                'isCategory': False
            }

            # 添加所有日期字段
            for date_str in date_range:
                date_key = f"date_{date_str.replace('-', '_')}"
                if date_key in product['date_data']:
                    # 如果有该日期的数据，使用实际数据
                    date_data = product['date_data'][date_key]
                    product_item[date_key] = {
                        'statuses': normalize_statuses(list(date_data['statuses'])),
                        'hasData': date_data['hasData'],
                        'timestamp': date_data['timestamp']
                    }
                else:
                    # 如果没有该日期的数据，设置默认值
                    product_item[date_key] = {
                        'statuses': normalize_statuses([]),
                        'hasData': False,
                        'timestamp': None
                    }

            items.append(product_item)

    return items


def parse_datetime(date_str):
    """解析多种格式的日期字符串为datetime对象

    Args:
        date_str: 日期时间字符串

    Returns:
        datetime: 解析后的datetime对象

    Raises:
        ValueError: 如果日期格式不支持或无效
    """
    if not date_str:
        raise ValueError("日期字符串不能为空")

    if 'Z' in date_str:
        date_str = date_str.replace('Z', '')

    if '.' in date_str:
        date_str = date_str.split('.')[0]

    formats = [
        "%Y-%m-%dT%H:%M:%S",
        "%Y-%m-%d %H:%M:%S",
        "%Y-%m-%d"
    ]

    for fmt in formats:
        try:
            return datetime.strptime(date_str, fmt)
        except ValueError:
            continue

    raise ValueError(
        "不支持的日期格式。支持的格式：YYYY-MM-DDTHH:MM:SS[.fff]Z, YYYY-MM-DD HH:MM:SS, YYYY-MM-DD"
    )


@alerts_center_bp.route('/sources', methods=['GET'])
def get_alert_sources():
    """获取告警来源统计"""
    try:
        start_time = request.args.get('start_time')
        end_time = request.args.get('end_time')
        status = request.args.get('status', '')
        current_app.logger.info(f"获取告警来源统计: {start_time} - {end_time}")

        if not start_time or not end_time:
            today = datetime.now().replace(hour=0, minute=0, second=0, microsecond=0)
            start_time = today.strftime("%Y-%m-%d %H:%M:%S")
            end_time = (today + timedelta(days=1, seconds=-1)
                        ).strftime("%Y-%m-%d %H:%M:%S")

        latest_alerts_subquery = db.session.query(
            MonitoringCenterAlert.fingerprint,
            func.max(MonitoringCenterAlert.starts_at).label('max_start_time')
        ).filter(
            MonitoringCenterAlert.starts_at >= start_time,
            MonitoringCenterAlert.starts_at <= end_time
        ).group_by(MonitoringCenterAlert.fingerprint).subquery()

        source_stats = db.session.query(
            MonitoringCenterAlert.source,
            func.count(MonitoringCenterAlert.id).label('count')
        ).join(
            latest_alerts_subquery,
            db.and_(
                MonitoringCenterAlert.fingerprint == latest_alerts_subquery.c.fingerprint,
                MonitoringCenterAlert.starts_at == latest_alerts_subquery.c.max_start_time
            )
        )

        if status == "true":
            source_stats = source_stats.filter(
                MonitoringCenterAlert.status != 'OK'
            ).group_by(
                MonitoringCenterAlert.source
            ).all()
        else:
            source_stats = source_stats.group_by(
                MonitoringCenterAlert.source
            ).all()

        sources = [
            {
                'source': source,
                'count': count
            }
            for source, count in source_stats
        ]

        return jsonify({
            'status': 'success',
            'data': {
                'sources': sources
            }
        })

    except Exception as e:
        current_app.logger.error(f"获取告警来源统计失败: {str(e)}")
        return jsonify({
            'status': 'error',
            'message': '服务器内部错误'
        }), 500


@alerts_center_bp.route('/active', methods=['GET'])
def list_active_alerts():
    """获取实时告警状态"""
    try:

        severity = request.args.get('severity')
        start_time = request.args.get('start_time')
        end_time = request.args.get('end_time')
        page = int(request.args.get('page', 1))
        source = request.args.get('source')
        status = request.args.get('status')
        per_page = int(request.args.get('per_page', 20))

        if not end_time:
            end_time = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        if not start_time:
            start_time = (datetime.strptime(end_time, "%Y-%m-%d %H:%M:%S") -
                          timedelta(hours=6)).strftime("%Y-%m-%d %H:%M:%S")

        base_query = MonitoringCenterAlert.query.filter(
            MonitoringCenterAlert.starts_at >= start_time,
            MonitoringCenterAlert.starts_at <= end_time,
            MonitoringCenterAlert.severity != 'resolved',
            MonitoringCenterAlert.status != 'OK',
            MonitoringCenterAlert.ends_at.is_(None)
        )

        if severity:
            base_query = base_query.filter(
                MonitoringCenterAlert.severity == severity)
        if status:
            base_query = base_query.filter(
                MonitoringCenterAlert.status == status)
        if source:
            base_query = base_query.filter(
                MonitoringCenterAlert.source == source)

        total_alerts = base_query.count()

        max_page = (total_alerts + per_page -
                    1) // per_page if total_alerts > 0 else 1

        if page > max_page:
            return jsonify({
                'status': 'error',
                'message': f'页码超出范围。最大页码为: {max_page}，当前请求页码为: {page}'
            }), 400

        alerts = base_query.order_by(MonitoringCenterAlert.updated_at.desc()) \
            .offset((page - 1) * per_page) \
            .limit(per_page) \
            .all()

        severity_stats = db.session.query(
            MonitoringCenterAlert.severity,
            func.count(MonitoringCenterAlert.id)
        ).filter(
            MonitoringCenterAlert.starts_at >= start_time,
            MonitoringCenterAlert.starts_at <= end_time,
            MonitoringCenterAlert.severity != 'resolved',
            MonitoringCenterAlert.status != 'OK',
            MonitoringCenterAlert.ends_at.is_(None)
        ).group_by(MonitoringCenterAlert.severity).all()

        results = []
        for alert in alerts:
            results.append({
                'id': alert.id,
                'source': alert.source,
                'fingerprint': alert.fingerprint,
                'external_id': alert.external_id,
                'status': alert.status,
                'severity': alert.severity,
                'summary': alert.summary,
                'description': alert.description,
                'labels': alert.labels,
                'annotations': alert.annotations,
                'starts_at': format_datetime(alert.starts_at),
                'ends_at': format_datetime(alert.ends_at),
                'created_at': format_datetime(alert.created_at),
                'updated_at': format_datetime(alert.updated_at),
                'acknowledged_by': alert.acknowledged_by,
                'acknowledged_at': format_datetime(alert.acknowledged_at),
                'alarm_status': alert.alarm_status,
                'account_id': alert.account_id
            })

        return jsonify({
            'status': 'success',
            'data': {
                'alerts': results,
                'total': total_alerts,
                'statistics': {
                    'severity_stats': {severity: count for severity, count in severity_stats}
                },
                'pagination': {
                    'current_page': page,
                    'per_page': per_page,
                    'total_pages': max_page,
                    'total_records': total_alerts
                }
            }
        })

    except Exception as e:
        current_app.logger.error(f"获取实时告警失败: {str(e)}")
        return jsonify({
            'status': 'error',
            'message': f"获取实时告警失败: {str(e)}"
        }), 500


@alerts_center_bp.route('/history', methods=['GET'])
def list_history_alerts():
    """获取历史告警数据"""
    try:

        status = request.args.get('status')
        severity = request.args.get('severity')
        source = request.args.get('source')
        start_time = request.args.get('start_time')
        end_time = request.args.get('end_time')
        page = int(request.args.get('page', 1))
        per_page = int(request.args.get('per_page', 20))

        if not start_time or not end_time:
            return jsonify({
                'status': 'error',
                'message': '开始时间和结束时间为必填参数'
            }), 400

        base_query = MonitoringCenterAlert.query.filter(
            MonitoringCenterAlert.starts_at >= start_time,
            MonitoringCenterAlert.starts_at <= end_time
        )

        total_all = base_query.count()

        status_stats = db.session.query(
            MonitoringCenterAlert.status,
            func.count(MonitoringCenterAlert.id).label('count')
        ).filter(
            MonitoringCenterAlert.starts_at >= start_time,
            MonitoringCenterAlert.starts_at <= end_time
        ).group_by(MonitoringCenterAlert.status).all()

        status_counts = {
            status: count
            for status, count in status_stats
        }

        severity_stats = db.session.query(
            MonitoringCenterAlert.severity,
            func.count(MonitoringCenterAlert.id).label('count')
        ).filter(
            MonitoringCenterAlert.starts_at >= start_time,
            MonitoringCenterAlert.starts_at <= end_time
        ).group_by(MonitoringCenterAlert.severity).all()

        severity_counts = {
            severity: count
            for severity, count in severity_stats if severity != "resolved"
        }

        source_stats = db.session.query(
            MonitoringCenterAlert.source,
            func.count(MonitoringCenterAlert.id).label('count')
        ).filter(
            MonitoringCenterAlert.starts_at >= start_time,
            MonitoringCenterAlert.starts_at <= end_time
        ).group_by(MonitoringCenterAlert.source).all()

        source_counts = {
            source: count
            for source, count in source_stats
        }

        if status:
            base_query = base_query.filter(
                MonitoringCenterAlert.status == status)
        if severity:
            base_query = base_query.filter(
                MonitoringCenterAlert.severity == severity)
        if source:
            base_query = base_query.filter(
                MonitoringCenterAlert.source == source)

        total_filter = base_query.count()

        max_page = (total_filter + per_page -
                    1) // per_page if total_filter > 0 else 1

        if page > max_page:
            return jsonify({
                'status': 'error',
                'message': f'页码超出范围。最大页码为: {max_page}，当前请求页码为: {page}'
            }), 400

        alerts = base_query.order_by(MonitoringCenterAlert.updated_at.desc()) \
            .offset((page - 1) * per_page) \
            .limit(per_page) \
            .all()

        results = []
        for alert in alerts:
            results.append({
                'id': alert.id,
                'source': alert.source,
                'fingerprint': alert.fingerprint,
                'external_id': alert.external_id,
                'status': alert.status,
                'severity': alert.severity,
                'summary': alert.summary,
                'description': alert.description,
                'labels': alert.labels,
                'annotations': alert.annotations,
                'starts_at': format_datetime(alert.starts_at),
                'ends_at': format_datetime(alert.ends_at),
                'created_at': format_datetime(alert.created_at),
                'updated_at': format_datetime(alert.updated_at),
                'acknowledged_by': alert.acknowledged_by,
                'acknowledged_at': format_datetime(alert.acknowledged_at),
                'alarm_status': alert.alarm_status,
                'account_id': alert.account_id
            })

        return jsonify({
            'status': 'success',
            'data': {
                'alerts': results,
                'total': total_filter,
                'statistics': {
                    'status_stats': status_counts,
                    'source_stats': source_counts,
                    'severity_stats': severity_counts
                },
                'pagination': {
                    'current_page': page,
                    'per_page': per_page,
                    'total_pages': max_page,
                    'total_records': total_filter
                }
            },
            'total_all': total_all
        })

    except ValueError as e:
        current_app.logger.error(f"参数错误: {str(e)}")
        return jsonify({
            'status': 'error',
            'message': f"参数错误: {str(e)}"
        }), 400
    except Exception as e:
        current_app.logger.error(f"获取历史告警失败: {str(e)}")
        return jsonify({
            'status': 'error',
            'message': f"获取历史告警失败: {str(e)}"
        }), 500


@alerts_center_bp.route('/trend', methods=['GET'])
def get_alert_trend():
    """获取告警趋势分析数据"""
    try:

        start_time = request.args.get('start_time')
        end_time = request.args.get('end_time')

        if not start_time or not end_time:
            return jsonify({
                'status': 'error',
                'message': '开始时间和结束时间为必填参数'
            }), 400

        try:
            start_time = parse_datetime(start_time)
            end_time = parse_datetime(end_time)
        except ValueError as e:
            return jsonify({
                'status': 'error',
                'message': f'时间格式错误: {str(e)}'
            }), 400

        time_diff = (end_time - start_time).total_seconds() / 86400

        if time_diff <= 3:

            interval = 'hour'
            time_format = '%Y-%m-%d %H:00:00'
            date_trunc = func.date_format(
                MonitoringCenterAlert.starts_at, time_format)

            time_points = []
            current = start_time.replace(minute=0, second=0, microsecond=0)
            while current <= end_time:
                time_points.append(current.strftime(time_format))
                current += timedelta(hours=1)
        else:

            interval = 'day'
            time_format = '%Y-%m-%d'
            date_trunc = func.date_format(
                MonitoringCenterAlert.starts_at, time_format)

            time_points = []
            current = start_time.replace(
                hour=0, minute=0, second=0, microsecond=0)
            while current <= end_time:
                time_points.append(current.strftime(time_format))
                current += timedelta(days=1)

        results = db.session.query(
            date_trunc.label('time_bucket'),
            func.count(MonitoringCenterAlert.id).label('total_count'),
            func.sum(case([(MonitoringCenterAlert.severity == 'critical', 1)], else_=0)).label(
                'critical_count'),
            func.sum(case([(MonitoringCenterAlert.severity == 'warning', 1)], else_=0)).label(
                'warning_count'),
            func.sum(case([(MonitoringCenterAlert.status == 'OK', 1)], else_=0)).label(
                'resolved_count')
        ).filter(
            MonitoringCenterAlert.starts_at >= start_time,
            MonitoringCenterAlert.starts_at <= end_time
        ).group_by('time_bucket').all()

        result_dict = {
            row.time_bucket: {
                'total': int(row.total_count or 0),
                'critical': int(row.critical_count or 0),
                'warning': int(row.warning_count or 0),
                'resolved': int(row.resolved_count or 0)
            } for row in results
        }

        trend_data = []
        for time_point in time_points:
            if time_point in result_dict:
                data = result_dict[time_point]
            else:
                data = {
                    'total': 0,
                    'critical': 0,
                    'warning': 0,
                    'resolved': 0
                }
            trend_data.append({
                'timestamp': time_point,
                **data
            })

        summary = {
            'total': sum(item['total'] for item in trend_data),
            'critical': sum(item['critical'] for item in trend_data),
            'warning': sum(item['warning'] for item in trend_data),
            'resolved': sum(item['resolved'] for item in trend_data)
        }

        return jsonify({
            'status': 'success',
            'data': {
                'interval': interval,
                'trend': trend_data,
                'summary': summary
            }
        })

    except Exception as e:
        current_app.logger.error(f"获取告警趋势数据失败: {str(e)}")
        return jsonify({
            'status': 'error',
            'message': f"获取告警趋势数据失败: {str(e)}"
        }), 500


@alerts_center_bp.route('/<fingerprint>/over', methods=['POST'])
def recover_alert(fingerprint: str = None):
    """设置告警结束"""
    try:
        if not fingerprint:
            return jsonify({
                'status': 'error',
                'message': '缺少必要的fingerprint参数'
            }), 400

        current_time = datetime.now().strftime("%Y-%m-%d %H:%M:%S")

        alert = MonitoringCenterAlert.query.filter_by(
            fingerprint=fingerprint,
            status='ALARM'
        ).first()

        if not alert:
            return jsonify({
                'status': 'error',
                'message': f'未找到未恢复的告警记录: {fingerprint}'
            }), 404

        alert.status = 'OK'
        alert.ends_at = current_time
        alert.updated_at = current_time

        try:
            db.session.commit()
            return jsonify({
                'status': 'success',
                'data': {
                    'id': alert.id,
                    'fingerprint': alert.fingerprint,
                    'status': alert.status,
                    'ends_at': alert.ends_at,
                    'updated_at': alert.updated_at
                }
            })
        except Exception as e:
            db.session.rollback()
            current_app.logger.error(f"更新告警状态失败: {str(e)}")
            raise

    except Exception as e:
        current_app.logger.error(f"恢复告警失败: {str(e)}")
        return jsonify({
            'status': 'error',
            'message': f"恢复告警失败: {str(e)}"
        }), 500
    finally:
        db.session.close()


def aggregate_items(items):
    """聚合相同name的告警项"""
    aggregated = {}
    for item in items:
        name = item['name']
        if name not in aggregated:
            aggregated[name] = {
                'name': name,
                'severities': [],
                'critical_count': 0,
                'warning_count': 0,
                'info_count': 0,
                'account_id': item['account_id'],
            }

        for severity in item['severities']:
            if severity not in aggregated[name]['severities']:
                aggregated[name]['severities'].append(severity)

        aggregated[name]['critical_count'] += item['critical_count']
        aggregated[name]['warning_count'] += item['warning_count']
        aggregated[name]['info_count'] += item['info_count']

    return list(aggregated.values())


# @alerts_center_bp.route('/healthboard', methods=['GET'])
# def get_healthboard_data():
#     try:

#         start_time = request.args.get('start_time')
#         end_time = request.args.get('end_time')

#         base_query = MonitoringCenterAlert.query.filter(
#             MonitoringCenterAlert.starts_at >= start_time,
#             MonitoringCenterAlert.starts_at <= end_time
#         )
#         statistics_query = base_query.with_entities(
#             MonitoringCenterAlert.status,
#             func.count(MonitoringCenterAlert.id).label('count')
#         ).group_by(MonitoringCenterAlert.status)

#         statistics = {status: count for status,
#                       count in statistics_query.all()}

#         alerts = base_query.filter(
#             MonitoringCenterAlert.status == 'ALARM').all()

#         items = {}

#         for alert in alerts:

#             if alert.source in ['pinpoint', 'rocketmq']:
#                 name = alert.labels.get('alertname') if alert.labels else None
#             elif alert.source == 'custom':
#                 if alert.labels:
#                     if 'alert_title' in alert.labels:
#                         name = alert.labels['alert_service'] if 'alert_service' in alert.labels and alert.labels[
#                             'alert_service'] != '' else alert.labels['alert_title']
#                     elif 'alert_object' in alert.labels:
#                         name = alert.labels['alert_object']
#                     else:
#                         name = alert.summary
#                 else:
#                     name = alert.summary
#             elif alert.source == 'tencent_cloud':
#                 name = alert.labels.get(
#                     'policy_name') if alert.labels else None
#             else:
#                 name = None

#             if name:
#                 if alert.source not in items:
#                     items[alert.source] = {
#                         'source': alert.source,
#                         'severities': [],
#                         'critical_count': 0,
#                         'warning_count': 0,
#                         'info_count': 0,
#                         'source_count': 0,
#                         'items': []
#                     }

#                 if alert.severity == 'critical':
#                     items[alert.source]['critical_count'] += 1
#                 elif alert.severity == 'warning':
#                     items[alert.source]['warning_count'] += 1
#                 elif alert.severity == 'info':
#                     items[alert.source]['info_count'] += 1

#                 items[alert.source]['source_count'] = (
#                     items[alert.source]['critical_count'] +
#                     items[alert.source]['warning_count'] +
#                     items[alert.source]['info_count']
#                 )

#                 if alert.severity not in items[alert.source]['severities']:
#                     items[alert.source]['severities'].append(alert.severity)

#                 items[alert.source]['items'].append({
#                     'name': name,
#                     'severities': [alert.severity],
#                     'critical_count': 1 if alert.severity == 'critical' else 0,
#                     'warning_count': 1 if alert.severity == 'warning' else 0,
#                     'info_count': 1 if alert.severity == 'info' else 0,
#                     'account_id': alert.account_id.split('|')[0] if alert.source == 'tencent_cloud' else None
#                 })

#         for source_data in items.values():
#             source_data['items'] = aggregate_items(source_data['items'])

#             source_data['source_count'] = source_data['source_count']

#         result = {
#             'items': list(items.values()),
#             'statistics': statistics
#         }

#         return jsonify({
#             'status': 'success',
#             'data': result
#         })
#     except Exception as e:
#         current_app.logger.error(f'获取健康看板数据失败: {str(e)}')
#         return jsonify({
#             'status': 'error',
#             'message': '服务器内部错误'
#         }), 500



@alerts_center_bp.route('/namespace/stats', methods=['GET'])
def get_namespace_stats():
    """根据namespace或product_show_name获取告警聚合统计"""
    try:
        start_time = request.args.get('start_time')
        end_time = request.args.get('end_time')

        if not start_time or not end_time:
            return jsonify({
                'status': 'error',
                'message': '开始时间和结束时间为必填参数'
            }), 400


        alerts = MonitoringCenterAlert.query.filter(
            MonitoringCenterAlert.starts_at >= start_time,
            MonitoringCenterAlert.starts_at <= end_time
        ).all()


        namespace_product = TencentProductList.query.all()
        namespace_product_dict = {product.namespace: product.product_name for product in namespace_product}


        default_severities = ['critical', 'warning', 'info']

        all_severities = set(default_severities)
        all_severities.update(alert.severity for alert in alerts if alert.severity)
        all_severities = list(all_severities)

        stats = {}

        def init_stat(group_key, group_id, alert):
            return {
                'name': group_key,
                'nameId': group_id if group_id else '',
                'total': 0,
                'source': alert.source if "tencent" in alert.source else '自建监控',
                'sources_items': {},
                'severity_items': {severity: 0 for severity in all_severities}
            }

        for alert in alerts:
            group_key = None
            group_id = None
            if alert.labels:
                if alert.labels.get('namespace'):
                    group_key = namespace_product_dict.get(alert.labels['namespace'], alert.labels['namespace'])
                    group_id = alert.labels['namespace']
                elif alert.labels.get('product_show_name'):
                    group_key = alert.labels['product_show_name']
                    group_id = group_key
            if not group_key:
                group_key = '自建监控'
                group_id = 'custom'

            if group_key not in stats:
                stats[group_key] = init_stat(group_key, group_id, alert)

            stats[group_key]['total'] += 1
            if alert.severity:
                stats[group_key]['severity_items'][alert.severity] = stats[group_key]['severity_items'].get(alert.severity, 0) + 1

            if alert.source == 'tencent_cloud' and alert.account_id:
                source_key = alert.account_id
            else:
                source_key = alert.source if alert.source else 'unknown'
            stats[group_key]['sources_items'][source_key] = stats[group_key]['sources_items'].get(source_key, 0) + 1

        result = [
            {
                'name': data['name'],
                'nameId': data['nameId'],
                'total': data['total'],
                'source': data['source'],
                'sources_items': data['sources_items'],
                'severity_items': data['severity_items'],
                'source_count': len(data['sources_items'])
            }
            for data in stats.values()
        ]

        return jsonify({
            'status': 'success',
            'data': {
                'stats': result,
                'total': len(alerts)
            }
        })

    except Exception as e:
        import traceback
        current_app.logger.error(f"获取namespace统计失败: {str(e)}\n{traceback.format_exc()}")
        return jsonify({
            'status': 'error',
            'message': f"获取namespace统计失败: {str(e)}"
        }), 500


@alerts_center_bp.route('/namespace/stats', methods=['POST'])
def get_namespace_stats_post():
    """根据namespaceId获取告警聚合统计（POST）"""
    try:
        data = request.get_json(force=True)
        start_time = data.get('start_time')
        end_time = data.get('end_time')
        namespace_id = data.get('namespaceID')

        if not start_time or not end_time or not namespace_id:
            return jsonify({
                'status': 'error',
                'message': 'start_time、end_time、namespaceID为必填参数'
            }), 400

        alerts = []
        if namespace_id == 'custom':
            alerts = MonitoringCenterAlert.query.filter(
                MonitoringCenterAlert.starts_at >= start_time,
                MonitoringCenterAlert.starts_at <= end_time,
                MonitoringCenterAlert.source.in_(['custom', 'pinpoint', 'rocketmq'])
            ).all()
        else:
            try:
                alerts = MonitoringCenterAlert.query.filter(
                    MonitoringCenterAlert.starts_at >= start_time,
                    MonitoringCenterAlert.starts_at <= end_time,
                    func.json_extract(MonitoringCenterAlert.labels, '$.namespace') == namespace_id
                ).all()
            except Exception as e:
                current_app.logger.warning(f"json_extract 查询失败，降级为 like 查询: {str(e)}")
                like_pattern = f'\"namespace\":\"{namespace_id}\"'
                alerts = MonitoringCenterAlert.query.filter(
                    MonitoringCenterAlert.starts_at >= start_time,
                    MonitoringCenterAlert.starts_at <= end_time,
                    MonitoringCenterAlert.labels.like(f'%{like_pattern}%')
                ).all()

        default_severities = ['critical', 'warning', 'info']
        all_severities = set(default_severities)
        all_severities.update(alert.severity for alert in alerts if alert.severity)
        all_severities = list(all_severities)

        stats = {
            'name': namespace_id,
            'nameId': namespace_id,
            'total': len(alerts),
            'source': 'tencent_cloud' if alerts and 'tencent' in alerts[0].source else '自建监控',
            'sources_items': {},
            'severity_items': {severity: 0 for severity in all_severities},
            'source_count': 0
        }

        for alert in alerts:
            if alert.severity:
                stats['severity_items'][alert.severity] = stats['severity_items'].get(alert.severity, 0) + 1
            if alert.source == 'tencent_cloud' and alert.account_id:
                source_key = alert.account_id
            else:
                source_key = alert.source if alert.source else 'unknown'
            stats['sources_items'][source_key] = stats['sources_items'].get(source_key, 0) + 1

        stats['source_count'] = len(stats['sources_items'])

        return jsonify({
            'status': 'success',
            'data': {
                'stats': [stats],
                'total': len(alerts)
            }
        })
    except Exception as e:
        import traceback
        current_app.logger.error(f"POST获取namespace统计失败: {str(e)}\n{traceback.format_exc()}")
        return jsonify({
            'status': 'error',
            'message': f"POST获取namespace统计失败: {str(e)}"
        }), 500


@alerts_center_bp.route('/panel', methods=['GET'])
def get_alert_panel():
    """获取健康面板数据 - v4.0.0 支持数据源分离（优化版本）

    性能优化：
    - 使用缓存的产品分类映射
    - 优化数据库查询策略
    - 减少重复计算和内存使用
    - 函数拆分提高可维护性
    """
    start_time_ms = time.time()

    try:
        # 获取请求参数
        start_time = request.args.get('start_time')
        end_time = request.args.get('end_time')
        date = request.args.get('date')

        current_app.logger.info(f"获取健康面板数据: {start_time} - {end_time}, date: {date}")

        # 参数验证
        is_valid, error_msg = validate_panel_request_params(start_time, end_time, date)
        if not is_valid:
            return jsonify({
                'status': 'error',
                'message': error_msg
            }), 400

        # 获取当前日期作为实时数据基准
        today = datetime.now().strftime("%Y-%m-%d")
        today_start = f"{today} 00:00:00"
        today_end = f"{today} 23:59:59"

        # 获取产品分类映射（使用缓存）
        product_category_map = get_product_category_mapping()
        if not product_category_map:
            current_app.logger.warning("产品分类映射为空，可能影响数据准确性")

        # 分离查询历史数据和今日数据
        # 历史数据：基于前端传递的时间范围，但排除今日数据
        history_alerts = fetch_history_alerts(start_time, end_time, today_start)

        # 今日数据：独立查询，不受前端时间参数影响，始终返回当天实时状态
        today_alerts = fetch_today_alerts(today_start, today_end)

        # 生成日期范围
        date_range = generate_date_range(date, end_time)

        # 构建历史数据
        category_stats, product_stats = build_history_data(history_alerts, product_category_map)

        # 构建今日状态数据
        today_status = build_today_status_data(today_alerts, product_category_map)

        # 构建响应items
        items = build_response_items(category_stats, product_stats, date_range)

        # 记录性能指标
        processing_time = (time.time() - start_time_ms) * 1000
        current_app.logger.info(f"健康面板数据处理完成: 耗时{processing_time:.2f}ms, 历史数据{len(history_alerts)}条, 今日数据{len(today_alerts)}条")

        return jsonify({
            'status': 'success',
            'message': '获取数据成功',
            'data': {
                'items': items,
                'today_status': today_status,
                'dateRange': date_range
            }
        })

    except ValueError as e:
        current_app.logger.error(f"参数错误: {e}")
        return jsonify({
            'status': 'error',
            'message': f"参数错误: {e}"
        }), 400
    except Exception as e:
        current_app.logger.error(f"获取健康面板数据失败: {e}")
        import traceback
        current_app.logger.error(f"详细错误信息: {traceback.format_exc()}")
        return jsonify({
            'status': 'error',
            'message': f"获取健康面板数据失败: {e}"
        }), 500


@alerts_center_bp.route('/events', methods=['GET'])
def get_event_statistics():
    """获取事件统计数据 - 查询当天NO_DATA状态的告警汇总"""
    try:
        # 获取当前日期，构造今日的开始和结束时间
        today = datetime.now().replace(hour=0, minute=0, second=0, microsecond=0)
        current_app.logger.info(f"today: {today}")
        today_start = today.strftime("%Y-%m-%d %H:%M:%S")
        today_end = (today + timedelta(days=1, seconds=-1)).strftime("%Y-%m-%d %H:%M:%S")

        current_app.logger.info(f"获取事件统计数据: {today_start} - {today_end}")

        # 查询当天状态为NO_DATA的告警
        events_query = MonitoringCenterAlert.query.filter(
            MonitoringCenterAlert.starts_at >= today_start,
            MonitoringCenterAlert.starts_at <= today_end,
            MonitoringCenterAlert.status == 'NO_DATA'
        )

        # 获取总数
        total_events = events_query.count()

        # 按来源统计
        source_stats = db.session.query(
            MonitoringCenterAlert.source,
            func.count(MonitoringCenterAlert.id).label('count')
        ).filter(
            MonitoringCenterAlert.starts_at >= today_start,
            MonitoringCenterAlert.starts_at <= today_end,
            MonitoringCenterAlert.status == 'NO_DATA'
        ).group_by(MonitoringCenterAlert.source).all()

        # 按严重程度统计
        severity_stats = db.session.query(
            MonitoringCenterAlert.severity,
            func.count(MonitoringCenterAlert.id).label('count')
        ).filter(
            MonitoringCenterAlert.starts_at >= today_start,
            MonitoringCenterAlert.starts_at <= today_end,
            MonitoringCenterAlert.status == 'NO_DATA'
        ).group_by(MonitoringCenterAlert.severity).all()

        # 构造响应数据
        source_counts = {source: count for source, count in source_stats}
        severity_counts = {severity: count for severity, count in severity_stats if severity}

        return jsonify({
            'status': 'success',
            'data': {
                'total_events': total_events,
                'date_range': {
                    'start': today_start,
                    'end': today_end
                },
                'statistics': {
                    'source_stats': source_counts,
                    'severity_stats': severity_counts
                }
            },
            'message': '获取事件统计数据成功'
        })

    except Exception as e:
        current_app.logger.error(f"获取事件统计数据失败: {str(e)}")
        return jsonify({
            'status': 'error',
            'message': f"获取事件统计数据失败: {str(e)}"
        }), 500