#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
@Title:     kubectl.py
@Date:      2022/10/18 19:49
@Author:    WangL<PERSON>
@Desc：     kubectl api 接口操作，注意：迁移到其他服务器存在风险（调用系统命令兼容问题），谨慎操作
"""
import json
import os
from datetime import datetime, timedelta
from flask import Blueprint, current_app, jsonify, request
from kubernetes import client, config
from kubernetes.client import ApiException
from sqlalchemy import or_
from subprocess import *
from tencentcloud.common import credential
from tencentcloud.common.exception.tencent_cloud_sdk_exception import TencentCloudSDKException
from tencentcloud.common.profile.client_profile import ClientProfile
from tencentcloud.common.profile.http_profile import HttpProfile
from tencentcloud.tcr.v20190924 import models, tcr_client

from model import CiProjects
from utils import execute_job_1000182, get_response, get_time_stamp, user_in_operations
from utils.mykubernetes import KubernetesBase
from utils.variable import devops_group, kube_config_dict, kube_config_file, tencent_secret

kubectl_bp = Blueprint("kubectl_blue", __name__, url_prefix="/kubectl/api/v1")

command_switch = "/usr/local/bin/kubecm switch %(cluster_env)s"
cluster_name = {"prod-b": "prod-b", "prod-a": "prod-a", "devops": "devops", 'devtest': "devtest"}  # 用于kubecm的变量


def command_run(command):
    """
    执行命令
    :param command:
    :return:
    """
    result = Popen(command, stdout=PIPE, stderr=PIPE, encoding='utf-8', shell=True)
    stdout, stderr = result.communicate()
    if result.poll() == 0:
        current_app.logger.info(f"执行命令：{command}")
        return stdout, stderr
    return "", "Error"


@kubectl_bp.route('/deployment/info', methods=['POST'])
def deployment_info():
    """
    获取deployment和namespace信息
    :param cluster_env: "B|A|devops|devtest"
    :return:
    """
    namespace = {}
    data_raw = get_response(request)
    cluster_env = data_raw.get('cluster_env', 'devtest')
    command_s = command_switch % {"cluster_env": cluster_name[cluster_env]}
    command_deployment = "/usr/local/bin/kubectl get deployment -A | grep -v 'kube-system'"
    command_namespace = "/usr/local/bin/kubectl get ns"
    # 切换集群
    command_run(command_s)
    # 获取集群namespace列表
    stdout, stderr = command_run(command_namespace)
    if stdout:
        lines = stdout.split('\n')
        lines = [i for i in lines if i != '']
        for line in lines:
            ns = line.split()[0]
            if ns == "NAME" or "kube" in ns:
                continue
            namespace[ns] = {"pod_total": 0}
    else:
        return stderr

    stdout, stderr = command_run(command_deployment)
    info_text = stdout.split('\n')
    info_text = [i for i in info_text if i != '']
    if stdout:
        for info in info_text:
            line_list = info.split()
            ns = line_list[0]
            podname = line_list[1]
            num = line_list[4]
            if ns == "NAMESPACE" or "kube" in ns:
                continue
            namespace[ns]["pod_total"] = namespace[ns]["pod_total"] + 1
            namespace[ns][podname] = int(num)
        result = [{k: v} for k, v in namespace.items()]
        return jsonify({"code": 200, "data": result})
    return stderr


@kubectl_bp.route('/podsinfo', methods=['POST'])
def get_pods_infos():
    """
    获取namespace下的pod信息列表
    :param namespace:  命名空间
    :param cluster_env: 集群ID
    :return:
    """
    data_raw = get_response(request)
    if data_raw.get('namespace') and data_raw.get('cluster_env'):
        pods_info = []
        columns_name = []
        command_s = command_switch % {"cluster_env": data_raw['cluster_env']}
        command_pod = "/usr/local/bin/kubectl get pods -n %(namespace)s -o wide --sort-by=.metadata.name  | grep -v 'kube-system'" % {"namespace": data_raw['namespace']}
        command_run(command_s)
        stdout, stderr = command_run(command_pod)
        if stdout:
            lines = stdout.split('\n')
            lines = [i for i in lines if i != '']
            for line in lines:
                pod_name = line.split()[0]
                if pod_name == "NAME":
                    columns_name.extend([i.lower() for i in line.split()[:-2]])
                    continue
                line_dict = dict(zip(columns_name, line.split()[:-2]))
                pods_info.append(line_dict)
            return jsonify({"code": 200, "data": pods_info})
        else:
            return jsonify({'code': 404, "msg": stderr})
    return jsonify({'code': 404, "msg": "命名空间和集群ID没有传递"})


@kubectl_bp.route('/replace/force', methods=['POST'])
def replace_force():
    """
    销毁新建pod，存在权限限制操作：prod-b和prod-a
    :param namespace: 命名名称，如：test-3
    :param pod_name: 单个pod名称，如：acs-5884dc7ffd-ft9h2
    :param cluster_env: 集群ID，如：B
    :return:
    """

    def running():
        command_s = command_switch % {"cluster_env": data_raw.get('cluster_env')}
        command_shell = "/usr/local/bin/kubectl get pod %(pod)s -n %(ns)s -o yaml | kubectl replace --force -f - " % {
            "pod": data_raw.get('pod_name'), "ns": data_raw.get('namespace')}
        command_run(command_s)
        out, err = command_run(command_shell)
        return out, err

    data_raw = get_response(request)
    username = request.headers.get('X-User')
    result = user_in_operations(username, deptname="运维")
    if data_raw.get('namespace') and data_raw.get('pod_name') and data_raw.get('cluster_env'):  # 参数传递是否正确
        if data_raw.get('cluster_env') in ["A", "B"] and result != "success":  # 权限操作，是否属于运维，操作A B集群
            return jsonify({'code': 403, "msg": "当前用户权限不够"})
        stdout, stderr = running()
        if stdout:
            return jsonify({'code': 200, "msg": f"{data_raw.get('pod_name')}销毁重建....."})
        else:
            return jsonify({"code": 500, 'msg': stderr})
    return jsonify({'code': 404, "msg": "命名空间或集群ID或pod_name名称没有传递"})


@kubectl_bp.route('/rallout/deployment', methods=['POST'])
def rallout_deployment():
    """
    重新部署deployment，存在权限限制操作：prod-b和prod-a
    :param namespace: 命名名称，如：test-3
    :param deployment: deployment名称，如：acs
    :param cluster_env: 集群ID，如：B
    :return:
    """

    def running():
        command_s = command_switch % {"cluster_env": data_raw.get('cluster_env')}
        command_shell = "/usr/local/bin/kubectl rollout restart -n %(ns)s deployment/%(deployment)s " % {
            "deployment": data_raw.get('deployment'), "ns": data_raw.get('namespace')}
        command_run(command_s)
        out, err = command_run(command_shell)
        return out, err

    data_raw = get_response(request)
    username = request.headers.get('X-User')
    result = user_in_operations(username, deptname="运维")
    if data_raw.get('namespace') and data_raw.get('deployment') and data_raw.get('cluster_env'):
        if data_raw.get('cluster_env') in ["A", "B"] and result != "success":  # 权限操作，是否属于运维，操作A B集群
            return jsonify({'code': 403, "msg": "当前用户权限不够"})
        stdout, stderr = running()
        if stdout:
            return jsonify({'code': 200, "msg": f"{data_raw.get('deployment')}重新部署....."})
        else:
            return jsonify({"code": 500, 'msg': stderr})
    return jsonify({'code': 404, "msg": "命名空间或集群ID或pod_name名称没有传递"})


@kubectl_bp.route('/deployment/image/list')
def deployment_image_list():
    """
    获取所选服务的镜像，均为prod镜像
    :params appname: 服务名
    :return:
    """
    kwargs = get_response(request)
    task_filter = {
        or_(
            CiProjects.appname == kwargs['deployment'],
            CiProjects.alianame == kwargs['deployment'],
            CiProjects.project == kwargs['deployment'],
        )
    }

    query = CiProjects.query.filter(*task_filter).first()
    repo_name = "/".join(query.repo_url.split('/')[1:])
    try:
        cred = credential.Credential(tencent_secret['secretId'], tencent_secret['secretKey'])
        httpProfile = HttpProfile()
        httpProfile.endpoint = "tcr.tencentcloudapi.com"
        clientProfile = ClientProfile()
        clientProfile.httpProfile = httpProfile
        client = tcr_client.TcrClient(cred, "ap-guangzhou", clientProfile)
        req = models.DescribeImagePersonalRequest()
        params = {
            "RepoName": repo_name,
            "offset": 0,
            "Limit": 100
        }
        req.from_json_string(json.dumps(params))
        resp = client.DescribeImagePersonal(req)

        tag_info = json.loads(resp.to_json_string())["Data"]["TagInfo"]

        # 根据时间排序当前仓库获取到的tag列表，获取最近时间的tag, 并对tag标签进行环境确认
        # tag_prefix = 'test_3c.'
        # tag_info = sorted([tag for tag in tag_info if tag["TagName"].startswith(tag_prefix)], key=lambda tag: tag['UpdateTime'], reverse=True)
        tag_info = sorted([{"TagName": '{}:{}'.format(query.repo_url, tag['TagName']), "UpdateTime": get_time_stamp(tag['UpdateTime'])} for tag in tag_info], key=lambda tag: tag['UpdateTime'], reverse=True)
        return jsonify({'code': 200, 'data': tag_info})
    except TencentCloudSDKException as err:
        current_app.logger.error(err)
        return jsonify({'code': 500, 'msg': err})


@kubectl_bp.route('/deployment/image/history', methods=['POST'])
def deployment_image_history():
    """
    获取指定namespace下所有的deployment的replca(修订历史记录),获取deployment的pod-template-hash
    :param namespace: 命名空间
    :param cluster_name: 集群名称（test, dev, prod-a, prod-b）
    :param deployment: deployment名称
    :return namespace: 命名空间
    :return name: pod名称
    :return version: 版本号
    :return update_time: 更新时间
    :return img_url: 镜像地址和版本号
    :return app: deployment名称
    """
    global img_url
    kwargs = get_response(request)
    namespace = kwargs.get('namespace', 'default')
    deployment = kwargs.get('deployment', 'acs')
    try:
        config.load_kube_config(kube_config_file % {"cluster_id": kube_config_dict[kwargs.get('cluster_name', 'prod-b')]})
        api_instance = client.AppsV1Api()
        api_response = api_instance.list_namespaced_replica_set(kwargs.get('namespace', 'default'), pretty="true")
        rs_dict = {"namespace": namespace, "deployment": deployment, "cluster_name": kwargs.get('cluster_name', 'prod-b')}
        history = []
        for line in api_response.items:
            if deployment != line.metadata.labels["k8s-app"]:
                continue
            for container in line.spec.template.spec.containers:
                if container.name == line.metadata.labels["k8s-app"]:
                    img_url = container.image
            Update_local_time = get_time_stamp(
                line.metadata.creation_timestamp + timedelta(hours=8))  # 获取到的不是北京时间，而是格林尼治时间，所以需要+8小时
            history.append(
                {"name": line.metadata.name, "version": line.metadata.annotations['deployment.kubernetes.io/revision'],
                 "TagName": img_url, "UpdateTime": Update_local_time, "app": line.metadata.labels["k8s-app"]})
        history = sorted(history, key=lambda label: (label['app'], label['UpdateTime']), reverse=True)
        rs_dict['history'] = history
        return jsonify({"code": 200, "data": rs_dict})
    except ApiException as e:
        current_app.logger.error("Exception when calling AppsV1Api->read_namespaced_replica_set: %s\n" % e)
        return jsonify({"code": 500, "msg": "Exception when calling AppsV1Api->read_namespaced_replica_set: %s\n" % e})


@kubectl_bp.route('/deployment/patch', methods=['POST'])
def deployment_patch():
    """
    TODO 暂时设置运维发布权限
    发版更新应用镜像
    调用蓝鲸平台发布
    :param cluster_name:
    :param namespace:
    :param deployment:
    :param TagName:
    :return:
    """
    user = request.headers.get("X-User")
    if user not in devops_group:
        return jsonify({"code": 403, "msg": "无发布权限"})
    repo_domain = "ccr.ccs.tencentyun.com/"
    kwargs = get_response(request)
    cluster_name = kwargs.get('cluster_name', "")
    namespace = kwargs.get('namespace', "")
    deployment = kwargs.get('deployment', "")
    img_url = kwargs.get('TagName').replace(repo_domain, "")
    params = {"cluster_name": cluster_name, "namespace": namespace, "deployment": deployment, "img_url": img_url}
    if cluster_name and namespace and deployment and img_url:
        # 调用蓝鲸作业平台
        # 返回数据，调用接口
        result = execute_job_1000182(**params)
        if result["result"] is True:
            current_app.logger.info(f"{cluster_name}-{namespace}-{deployment}-{img_url}服务：正在发布中.....")
            return jsonify({"code": 200, "msg": f"{cluster_name}-{namespace}-{deployment}服务：正在发布中.....", "data": result})
        return jsonify({"code": 503, "msg": result['message']})
    current_app.logger.error("版本发布调用失败，失败原因：参数传递不足或参数存在为空")
    return jsonify({"code": 500, "msg": "参数传递不足或存在参数为空"})


@kubectl_bp.route('/deployment/restart', methods=['POST'])
def deployment_restart():
    """
    重新部署应用镜像，只限于重启
    :param cluster_name: 集群ID
    :param deployment: deployment名称
    :param namespace: 命令空间
    :return:
    """
    kwargs = get_response(request)
    cluster_id = kube_config_dict[kwargs.get('cluster_name')] if kube_config_dict.get(kwargs.get('cluster_name')) else kwargs.get('cluster_name')
    kube_file = kube_config_file % {"cluster_id": cluster_id}
    if os.path.exists(kube_file) is False:
        current_app.logger.warning("集群：{}，未找到配置文件".format(cluster_id))
        return {"code": 404, "msg": "集群：{}，未找到配置文件".format(cluster_id)}
    k8s_client = KubernetesBase(kube_file)
    api_response = k8s_client.apps_v1_instance.list_namespaced_deployment(namespace=kwargs.get('namespace'), pretty='true')
    namespace_deployments = [deployment_name.metadata.name for deployment_name in api_response.items]
    if kwargs.get('deployment') in namespace_deployments:
        old_body = k8s_client.apps_v1_instance.read_namespaced_deployment(name=kwargs.get('deployment'), namespace=kwargs.get('namespace'), pretty='true')
        old_body.spec.template.metadata.annotations = {"date": str(int(datetime.timestamp(datetime.now())))}  # 修改时间，打包文件修改的目的，已实现重启操作
        k8s_client.apps_v1_instance.patch_namespaced_deployment(name=kwargs.get('deployment'), namespace=kwargs.get('namespace'), body=old_body, pretty='true')
        current_app.logger.info("集群：{cluster_name}，命名空间：{namespace}，deployment：{deployment} 重启调用成功".format(**kwargs))
        return jsonify({"code": 200, "msg": "集群：{cluster_name}，命名空间：{namespace}，deployment：{deployment} 重启调用成功".format(**kwargs)})
    current_app.logger.warning("{deployment}不在当前{cluster_name}中，停止调用".format(**kwargs))
    return jsonify({"code": 403, "msg": "{deployment}不在当前{cluster_name}中，停止调用".format(**kwargs)})
