#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
@Title:     public.py
@Date:      2024/3/19 14:41
@Author:    wanglh
@Desc:      公共接口蓝图
"""

from flask import Blueprint, request
from sqlalchemy import insert
from sqlalchemy.exc import SQLAlchemyError

from blueprint.presm import DbOperationUtil, LogOperationError, LogOperationUtil
from utils import *

public_bp = Blueprint("public_api", __name__, url_prefix="/public/api/v1")


@public_bp.route("/envs/grayrule/update", methods=['POST'])
def update_gray_rule():
    """
    批量更新环境规则信息的公共接口
    通过 albumid 列表进行匹配并修改信息
    """
    data_list = request.get_json()
    if not isinstance(data_list, list):
        data_list = [data_list]
        
    applicant = request.headers.get('X-User')
    if not applicant:
        return jsonify({"msg": "X-User 头部信息不能为空"}), 400

    existing_applicant = SsousersModel.query.filter(
        or_(SsousersModel.username == applicant,
            SsousersModel.displayname == applicant)
    ).first()
    
    if not existing_applicant:
        return jsonify({"msg": "applicant不存在于SsoUsers数据表中"}), 400

    all_pre_uuids = [data.get("pre_uuid") for data in data_list if data.get("pre_uuid")]
    if not all_pre_uuids:
        return jsonify({"msg": "pre_uuid 参数不能为空"}), 400
        
    valid_pre_uuids = CiPreEnvInfo.query.filter(
        CiPreEnvInfo.pre_uuid.in_(all_pre_uuids),
        CiPreEnvInfo.status < 3
    ).with_entities(CiPreEnvInfo.pre_uuid).all()
    valid_pre_uuids = [uuid[0] for uuid in valid_pre_uuids]
    
    invalid_pre_uuids = set(all_pre_uuids) - set(valid_pre_uuids)
    if invalid_pre_uuids:
        return jsonify({
            "msg": "存在无效的预发布环境ID",
            "invalid_pre_uuids": list(invalid_pre_uuids)
        }), 400

    all_albumids = []
    for data in data_list:
        rules = data.get("rules", [])
        all_albumids.extend(rule.get("albumid") for rule in rules if rule.get("albumid"))
    
    duplicate_albumids = [aid for aid in set(all_albumids) if all_albumids.count(aid) > 1]
    if duplicate_albumids:
        return jsonify({
            "msg": "参数中存在重复的 albumid",
            "duplicate_albumids": duplicate_albumids
        }), 400

    response = {
        "success": [],
        "failed": [],
        "log_status": "Success"
    }

    try:
        for data in data_list:
            pre_uuid = data.get("pre_uuid")
            rules = data.get("rules", [])
            
            if not rules:
                response["failed"].append({
                    "pre_uuid": pre_uuid,
                    "msg": "rules 参数不能为空"
                })
                continue

            invalid_rules = [rule for rule in rules if not rule.get("albumid")]
            if invalid_rules:
                response["failed"].append({
                    "pre_uuid": pre_uuid,
                    "msg": "存在没有 albumid 的规则",
                    "invalid_rules": invalid_rules
                })
                continue

            pre_uuid_success = []
            pre_uuid_failed = []
            
            for rule in rules:
                albumid = rule["albumid"]
                rule["update_user"] = applicant
                rule["pre_uuid"] = pre_uuid
                current_app.logger.info(f"update env grayrule: {rule}")

                try:
                    current_rule = CiPreGrayRule.query.filter_by(albumid=albumid).first()

                    stmt = insert(CiPreGrayRule).values(**rule)
                    stmt = stmt.on_duplicate_key_update(**rule)
                    db.session.execute(stmt)
                    db.session.commit()

                    operation_type = "新增" if not current_rule else "更新"
                    log_message = (
                        f"相册ID：{albumid}，{operation_type}环境规则到{pre_uuid}"
                        if not current_rule else
                        f"相册ID：{albumid}，从环境{current_rule.pre_uuid}变更到{pre_uuid}"
                    )

                    LogOperationUtil.log_operation(
                        5,
                        pre_uuid,
                        f"{operation_type}环境规则",
                        log_message,
                        0,
                        applicant
                    )

                    pre_uuid_success.append({
                        "albumid": albumid,
                        "msg": f"{operation_type}成功"
                    })

                except (LogOperationError, SQLAlchemyError) as e:
                    db.session.rollback()
                    pre_uuid_failed.append({
                        "albumid": albumid,
                        "msg": f"操作失败: {str(e)}"
                    })
                    current_app.logger.error(f"规则操作失败 albumid={albumid}: {e}")

            if pre_uuid_success:
                response["success"].append({
                    "pre_uuid": pre_uuid,
                    "rules": pre_uuid_success
                })
            if pre_uuid_failed:
                response["failed"].append({
                    "pre_uuid": pre_uuid,
                    "rules": pre_uuid_failed
                })

        if response["success"]:
            try:
                empty_rules_query = CiPreGrayRule.query.filter(
                    or_(
                        CiPreGrayRule.albumid == "",
                        CiPreGrayRule.albumid.is_(None)
                    )
                )
                
                count = empty_rules_query.count()
                
                if count > 0:
                    DbOperationUtil.delete_from_db(empty_rules_query)
                    current_app.logger.info(f"成功删除 {count} 条空规则记录")
                else:
                    current_app.logger.info("没有找到需要删除的空规则记录")
                    
            except Exception as e:
                current_app.logger.error(f"删除空规则失败: {e}")

    except Exception as e:
        current_app.logger.error(f"更新规则过程中发生错误: {e}")
        response["log_status"] = "Failed"
        return jsonify(response), 500

    if not response["success"] and response["failed"]:
        response["log_status"] = "Failed"
        return jsonify(response), 500

    return jsonify(response), 200
