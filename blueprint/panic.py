#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
@Title:     panic.py
@Date:      2023/3/30 9:51
@Author:    wanglh
@Desc：     panic 限流熔断相关接口
"""

from functools import wraps

from flask import Blueprint, request, views
from sqlalchemy import and_

from blueprint import ViewMethodBase
from utils import *

panic_bp = Blueprint("panic_blue", __name__, url_prefix='/panic/api/v1')


def user_judgment(func):
    @wraps(func)
    def wrapper(*args, **kwargs):
        owner = request.headers.get('X-User')
        if owner not in ['dongxin', 'lijiana', 'wanglinhao', 'wuxuan']:
            return jsonify({"code": 403, "msg": "权限不够，请联系管理员"})
        return func(*args, **kwargs)

    return wrapper


class CiPanicUriLevelAPI(views.MethodView, ViewMethodBase):
    pass


class CiPanicUriListAPI(views.MethodView, ViewMethodBase):
    def __init__(self):
        super().__init__(CiPanicUriList, request)  # 调用父类复制方法，
        # 时间兼容处理
        for line in self.params:
            if "time" in line:
                self.params[line] = get_time_stamp(self.params[line])

    def get(self):
        """
        panic：urilist-查询
        ---
        tags:
          - panic：限流
        parameters:
          - name:  keyword
            in: query
            type: string
            required: false
            description: 关键字
        responses:
          200:
            description: A successful response
            example: {"data": [], "total": total}
        """
        # 关键字处理
        current_app.logger.info("表：{}，查询关键字：{}".format(CiPanicUriList.__tablename__, self.keyword))
        task_filter = {
            and_(
                CiPanicUriList.uri.like('%{}%'.format(self.keyword)),
                CiPanicUriList.status == 1,
            )
        }
        # 获取关键字查询的所有结果
        total = CiPanicUriList.query.filter(*task_filter).count()
        res = self.view_methods_get_for_table(task_filter)
        for line in res:
            for key in line:
                if "time" in key:
                    line[key] = get_time_stamp(line.get(key))
        res = sorted(res, key=lambda x: x["update_time"], reverse=False)
        return jsonify({"code": 200, "data": res, "total": total})

    def post(self):
        """
        panic：urilist-新增与更新
        ---
        tags:
          - panic：限流
        parameters:
          - name:  X-User
            in: header
            type: string
            required: true
            description: owner id
          - name: body
            in: body
            required: true
            schema:
              type: object
              properties:
                id:
                  type: integer
                  required: false
                status:
                  type: integer
                  required: true
                uri:
                  type: string
                  required: true
                  description: uri地址
                create_time:
                  type: string
                applicant:
                  type: string
                update_time:
                  type: string
        responses:
          200:
            description: A successful response
            example: {"data": [], "total": total}
        """
        self.params['applicant'] = self.owner  # 添加创建人
        res = self.view_methods_post_for_table()
        return jsonify(res)

    @user_judgment  # 装饰器，判断用户
    def delete(self):
        """
        panic：urllist-单个删除
        ---
        tags:
          - panic：限流
        parameters:
          - name: X-User
            in: header
            type: string
            required: true
            description: user ID
          - name: body
            in: body
            required: true
            schema:
              type: object
              properties:
                id:
                  type: integer
                  required: false
                status:
                  type: integer
                  required: true
                uri:
                  type: string
                  required: true
                  description: uri地址
                create_time:
                  type: string
                applicant:
                  type: string
                update_time:
                  type: string
        responses:
          200:
            description: 请求成功
            example: {"data": [{}],"total": 1}
          403:
            description: 权限限制
            example: {"msg": ""}
          404:
            description: 参数缺失
            example: {"msg": ""}
        """
        column_id = self.params.get('id')

        if column_id:
            CiPanicUriList.query.filter_by(id=column_id).update({"status": 2})
            db.session.commit()
            print("完成")
        LOCAL_HOST = current_app.config.get('LOCAL_HOST')
        LOCAL_PORT = current_app.config.get('LOCAL_PORT')
        res = requests.get(f"http://{LOCAL_HOST}:{LOCAL_PORT}/panic/api/v1/urilist")
        data = res.json()
        data['msg'] = f'id={column_id}, 删除完成'
        return jsonify(data)


class KafkaNginxErrorAPI(views.MethodView, ViewMethodBase):

    def __init__(self):
        super().__init__(KafkaNginxError, request)  # 调用父类复制方法，

    def get(self):
        from utils.mysql import MysqlAcs
        sql_conn = MysqlAcs()
        """
        kafka_error_nginx-query
        ---
        tags:  # 表示组名称
          - panic：限流
        parameters:
          - name:  time
            in: params
            type: string
            required: false
            description: 时间
        responses:
          200:
            description: A successful response
            example: {"data": [], "total": total}
        """
        # 关键字处理
        current_app.logger.info("表：{}，查询关键字：{}".format(KafkaNginxError.__tablename__, self.keyword))
        q_time = int(self.params.get('time')) if self.params.get('time') else 300

        select_sql = """
        select
            replace(url, '//', '/') as 'url',
            if(l.`level` is null, 0, l.`level`) as 'level',
            sum(count) as 'count'
        from
            wego_ops.`kafka_nginx_error` e
            left join wego_ops.`ci_panic_uri_level` l on l.`uri` = replace(e.url, '//', '/')
        where
            ts > TIMESTAMPADD(SECOND, -{q_time}, now())
        group by
            url
        order by
            3 desc
            LIMIT {pageNo},{pageSize};
        """.format(q_time=q_time, pageNo=int(self.pageSize) * (int(self.pageIndex) - 1), pageSize=int(self.pageSize))

        print(select_sql)
        res = sql_conn.select(select_sql, type_="fetchall")
        total_sql = """
        select
            replace(url, '//', '/') as 'url',
            if(l.`level` is null, 0, l.`level`) as 'level',
            sum(count) as 'count'
        from
            wego_ops.`kafka_nginx_error` e
            left join wego_ops.`ci_panic_uri_level` l on l.`uri` = replace(e.url, '//', '/')
        where
            ts > TIMESTAMPADD(SECOND, -{q_time}, now())
        group by
            url
        order by
            3 desc;
        """.format(q_time=q_time)
        query = sql_conn.select(total_sql, type_="fetchall")
        total = len(query)
        sql_conn.__disconnect__()
        return jsonify({"code": 200, "data": res, "total": total})


@panic_bp.route('/urilist/clean/all', methods=['DELETE'])
@user_judgment
def urilist_clean_all():
    """
    panic：降级所有接口
    ---
    tags:  # 表示组名称
      - panic：限流
    parameters:
      - name: X-User
        in: header
        type: string
        required: true
        description: user ID
    responses:
      200:
        description: 请求成功
        example: {"msg": "删除完毕","status=1": total}
      403:
        description: 权限限制
        example: {"msg": ""}
      404:
        description: 参数缺失
        example: {"msg": ""}
    """
    CiPanicUriList.query.filter_by(status=1).update({"status": 2})
    db.session.commit()
    LOCAL_HOST = current_app.config.get('LOCAL_HOST')
    LOCAL_PORT = current_app.config.get('LOCAL_PORT')
    res = requests.get(f'http://{LOCAL_HOST}:{LOCAL_PORT}/panic/api/v1/urilist')
    res = res.json()
    total = res.get('total')
    return jsonify({"code": 200, "msg": "删除完毕", "status=1": total})


@panic_bp.route('/hiesecurity', methods=['POST'])
@user_judgment
def hierarchical_security():
    """
    panic：分级保障
    ---
    tags:  # 表示组名称
      - panic：限流
    parameters:
      - name: body
        in: body
        required: true
        schema:
          type: object
          properties:
            level:
              type: string
              description: 级别 1/2/3
      - name: X-User
        in: header
        type: string
        required: true
        description: user ID
    responses:
      200:
        description: 请求成功
        example: {"data": [{}],"total": 1}
      403:
        description: 权限限制
        example: {"msg": ""}
      404:
        description: 参数缺失
        example: {"msg": ""}
    """

    params = get_response(request)
    if params.get('level'):
        insert_sql = """
        insert into ci_panic_urilist (uri) 
        select u.uri from ci_nginx_uriwhitelist u left join ci_panic_uri_level l  on l.`uri`=u.`uri`
        WHERE l.id IS NULL OR l.level > {level};
        """.format(level=int(params.get('level')))
        db.session.execute(insert_sql)
        db.session.commit()
        model_get = CiPanicUriListAPI()
        return model_get.get()
    return jsonify({'code': 404, 'msg': '缺少主要的参数'})


panic_bp.add_url_rule(rule='/urilevel', view_func=CiPanicUriLevelAPI.as_view('ci_panic_uri_level_api'))
panic_bp.add_url_rule(rule='/urilist', view_func=CiPanicUriListAPI.as_view('ci_panic_urilist_api'))
panic_bp.add_url_rule(rule='/kfkerror', view_func=KafkaNginxErrorAPI.as_view('kafka_nginx_error_api'))
