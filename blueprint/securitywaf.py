#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
@Title:     securitywaf_Old.py
@Date:      2024/5/15 14:44
@Author:    wanglh
@Desc:      安全中心接口
"""
from configparser import ConfigParser
from datetime import timed<PERSON>ta

from flask import Blueprint, request, views
from sqlalchemy import asc, desc, inspect
from sqlalchemy.dialects.mysql import VARCHAR
from sqlalchemy.exc import SQLAlchemyError

from blueprint.cicd import get_page_keyword
from blueprint.useritsm import get_name_dictionary
from utils import *

waf_bp = Blueprint('securitywaf', __name__, url_prefix='/security/api/v1')

current_path = os.path.abspath(__file__)
current_dir = os.path.dirname(current_path)
config_path = os.path.join(os.path.dirname(current_dir), 'config.ini')
config_read = ConfigParser()
config_read.read(config_path)
# ic(config_path)


def query_table_info():
    # 使用 SQLAlchemy 的 inspect 功能来获取表的信息
    inspector = inspect(engine)
    columns = inspector.get_columns(WafAttackDetails.__tablename__)
    all_columns_info = []
    varchar_columns_info = []
    for col in columns:
        column_info = {
            'name': col['name'],
            'type': str(col['type']),
            'nullable': col['nullable'],
            'default': col.get('default', None),  # 使用 .get() 来避免 KeyError
            'autoincrement': col.get('autoincrement', False)
        }
        all_columns_info.append(column_info)

        # 检查列的类型是否为 varchar，如果是，则添加到 varchar_columns_info 列表
        if isinstance(col['type'], VARCHAR):
            varchar_columns_info.append(column_info)

    return jsonify({
        "all_column": all_columns_info,
        "varchar_column": varchar_columns_info
    })


class ViewItem:
    @classmethod
    def get_view_func(cls):
        # 定义 name 和 url
        cls.name = f"{cls.model.__table__.name}_api"
        cls.url = f"/{'/'.join(cls.model.__table__.name.split('_'))}"
        cls.domain = f"{waf_bp.url_prefix}{cls.url}"
        return cls.name, cls.url


class BaseSecurityView(views.MethodView):
    model = None  # 子类应覆盖此属性以指定模型

    def __init__(self):
        if not self.model:
            raise ValueError("Model not defined")
        self.time_fields = self.get_time_fields()
        self.keyword, self.pageIndex, self.pageSize = self.get_params()
        self.data_raw = get_response(request)

    @staticmethod
    def get_params():
        """
        获取分页和关键词参数（假设这些函数已定义）
        """
        kw, pageIndex, pageSize = get_page_keyword(get_response(request))
        keyword = get_name_dictionary(kw) or kw
        return keyword, pageIndex, pageSize

    @staticmethod
    def process_query_result(data):
        """
        钩子方法：允许子类自定义处理查询结果数据。默认实现直接返回数据
        """
        return data

    @staticmethod
    def prepare_params(params):
        """
        清理参数，移除None值，保留0值
        """
        return {k: v for k, v in params.items() if v is not None or v == 0}

    @staticmethod
    def convert_to_dict(row):
        """
        将查询结果转换为字典，并确保 sum_attack_count 为数值类型
        """
        row_dict = dict(row)
        if 'sum_attack_count' in row_dict:
            row_dict['sum_attack_count'] = int(row_dict['sum_attack_count'])
        return row_dict

    def get_time_fields(self):
        """ 获取时间类型字段 """
        time_fields = [
            field.name for field in self.model.__table__.columns
            if isinstance(field.type, (db.DateTime, db.Date, db.Time))
        ]
        return time_fields

    def get_query_filters(self):
        """ 查询过滤条件，默认：将时间范围添加到 filters 中，没有则默认选取 7 天"""

        start_time = self.data_raw.get("start_time", (datetime.now() - timedelta(days=7)).strftime("%Y-%m-%d %H:%M:%S"))
        end_time = self.data_raw.get("end_time", datetime.now().strftime("%Y-%m-%d %H:%M:%S"))

        filters = [and_(*[getattr(self.model, field) >= start_time for field in self.time_fields]),
                   and_(*[getattr(self.model, field) <= end_time for field in self.time_fields])]
        if self.keyword:
            keyword_filters = or_(
                self.model.uri.like(f"%{self.keyword}%"),
                self.model.params.like(f"%{self.keyword}%"),
                self.model.headers.like(f"%{self.keyword}%")
            )
            filters.append(keyword_filters)
        return self.model.query.filter(*filters)

    def get(self, orderColumn="", orderDir="desc"):
        """
        默认：获取过滤条件后的所有数据
        """
        try:
            data_query = self.get_query_filters()
            total_count = data_query.count()
            # 版本 1
            # if orderColumn != "":
            #     order_columns = [getattr(self.model, col) for col in orderColumn.split(',')]
            #     # data_query = data_query.order_by(*[desc(getattr(self.model, col)) for col in orderColumn.split(',')])
            # else:
            #     order_columns = [getattr(self.model, col.strip()) for col in self.time_fields]
            #     # data_query = data_query.order_by(*[desc(getattr(self.model, col.strip())) for col in self.time_fields])
            #
            # if orderDir == "asc":
            #     order_columns = [asc(col) for col in order_columns]
            # else:
            #     order_columns = [desc(col) for col in order_columns]
            #
            # data_query = data_query.order_by(*order_columns)

            ## 版本 2：
            # 参数传递：通过这种方式，你可以传递如下参数进行排序：
            # orderColumn="id,time"
            # orderDir="asc,desc"
            order_columns = []
            if orderColumn:
                columns = orderColumn.split(',')
                directions = orderDir.split(',') if orderDir else ['desc'] * len(columns)
                for col, dir in zip(columns, directions):
                    col_attr = getattr(self.model, col.strip())
                    if dir.strip().lower() == 'asc':
                        order_columns.append(asc(col_attr))
                    else:
                        order_columns.append(desc(col_attr))
            else:
                order_columns = [desc(getattr(self.model, col.strip())) for col in self.time_fields]
            data_query = data_query.order_by(*order_columns)

            data_query = data_query.limit(self.pageSize).offset((self.pageIndex - 1) * self.pageSize).all()
            data = self.model.to_all_json(data_query)
            data = self.process_query_result(data)
            data = self.change_time_stamp(data)
            return jsonify({
                "code": 200,
                "data": data,
                "totalCount": total_count,
            }), 200
        except Exception as e:
            current_app.logger.error(f"Error in get method: {e}")
            return jsonify({"code": 500, "msg": str(e)}), 500

    def change_time_stamp(self, data):
        for item in data:
            for key, value in item.items():
                if key in self.time_fields:
                    item[key] = get_time_stamp(value)
        return data

    def update_record(self, update_conditions, update_values):
        """更新记录，子类可重写提供具体逻辑"""
        try:
            if update_conditions:
                record = self.model.query.filter_by(**update_conditions).first()
                if record:
                    for key, value in update_values.items():
                        setattr(record, key, value)
                else:
                    return jsonify({"code": 404, "msg": "记录未找到"}), 404
            else:
                # new_record_data = update_values
                record = self.model(**update_values)
                db.session.add(record)
            db.session.commit()
            return jsonify({"code": 200, "msg": "更新成功"}), 200
        except SQLAlchemyError as e:
            db.session.rollback()
            current_app.logger.error(f"Database error in update_record: {e}, Conditions: {update_conditions}, Values: {update_values}")
            return jsonify({"code": 500, "msg": "数据库错误，请稍后再试"}), 500
        except Exception as e:
            db.session.rollback()
            current_app.logger.error(f"Error in update_record: {e}, Conditions: {update_conditions}, Values: {update_values}")
            return jsonify({"code": 500, "msg": str(e)}), 500

        # try:
        #     if update_conditions:
        #         record = self.model.query.filter_by(**update_conditions).first()
        #         if record:
        #             for key, value in update_values.items():
        #                 setattr(record, key, value)
        #     else:
        #         new_record_data = {**update_conditions, **update_values}
        #         record = self.model(**new_record_data)
        #         db.session.add(record)
        #     db.session.commit()
        #     return jsonify({"code": 200, "msg": "更新成功"}), 200
        # except Exception as e:
        #     db.session.rollback()
        #     current_app.logger.error(f"Error in update_record: {e}")
        #     return jsonify({"code": 500, "msg": str(e)}), 500

    def post(self):
        try:
            params = self.prepare_params(request.json)
            update_conditions = self.get_update_conditions(params)
            return self.update_record(update_conditions, params)
        except Exception as e:
            current_app.logger.error(f"Error in post method: {e}, Params: {request.json}")
            return jsonify({"code": 500, "msg": str(e)}), 500

    def get_update_conditions(self, params):
        """生成更新条件，子类实现具体逻辑"""
        raise NotImplementedError("Subclasses should implement this method")

    def get_aggregated_data(self, group_by_column):
        try:
            data_query = self.get_query_filters()
            group_query = data_query.group_by(group_by_column)
            total_count = group_query.count()
            data_query = group_query.with_entities(
                group_by_column,
                func.sum(self.model.attack_count).label('sum_attack_count')
            ).order_by(desc("sum_attack_count"))
            data_query = data_query.limit(self.pageSize).offset((self.pageIndex - 1) * self.pageSize).all()
            data = [self.convert_to_dict(row) for row in data_query]  # 该方法将查询结果转换为字典，并确保 sum_attack_count 为数值类型（使用 float 类型转换）。
            data = self.process_query_result(data)
            data = self.change_time_stamp(data)
            return jsonify({
                "code": 200,
                "data": data,
                "totalCount": total_count,
            }), 200
        except Exception as e:
            current_app.logger.error(f"Error in get_aggregated_data method: {e}")
            return jsonify({"code": 500, "msg": str(e)}), 500


class WafAttackDetailsView(BaseSecurityView):
    model = WafAttackDetails

    def get_query_filters(self):
        group = request.args.get('group', '')
        start_time = self.data_raw.get("start_time", (datetime.now() - timedelta(days=7)).strftime("%Y-%m-%d %H:%M:%S"))
        end_time = self.data_raw.get("end_time", datetime.now().strftime("%Y-%m-%d %H:%M:%S"))
        filters = [and_(self.model.timestamp >= start_time, self.model.timestamp <= end_time)]

        if self.keyword:
            keyword_filters = or_(
                self.model.uri.like(f"%{self.keyword}%"),
                self.model.params.like(f"%{self.keyword}%"),
                self.model.headers.like(f"%{self.keyword}%")
            )
            filters.append(keyword_filters)

        try:
            if group:
                group_columns = group.split(',')
                group_column_objects = [getattr(self.model, col.strip()) for col in group_columns if hasattr(self.model, col.strip())]
                # if not group_column_objects:
                #     return {'error': 'Invalid group fields'}, 400
                return self.model.query.filter(*filters).with_entities(*group_column_objects, func.count(self.model.id).label('count')).group_by(*group_column_objects), True
            else:
                return self.model.query.filter(*filters), False
        except Exception as e:
            current_app.logger.error(f"Error in get_query_filters: {e}")
            return self.model.query.filter(*filters), False

    def get(self):
        try:
            data_query, is_grouped = self.get_query_filters()
            total_count = self.model.query.count()
            if is_grouped:
                # 计数
                data_item_count = data_query.count()
                query = data_query.order_by(desc('count'))
            else:
                query = data_query.order_by(desc(self.model.timestamp))
                data_item_count = total_count

            # 分页
            data = query.limit(self.pageSize).offset((self.pageIndex - 1) * self.pageSize).all()
            data = self.model.to_all_json(data) if not is_grouped else [dict(row) for row in data]
            sum_count = sum(row['count'] for row in data) if is_grouped else data_item_count

            data = self.process_query_result(data)
            data = self.change_time_stamp(data)
            return jsonify({
                "code": 200,
                "data": data,
                "totalCount": total_count,
                "dataItemCount": data_item_count,
                "sumCount": sum_count
            }), 200
        except Exception as e:
            current_app.logger.error(f"Error in get method: {e}")
            return jsonify({"code": 500, "msg": str(e)}), 500

    def get_update_conditions(self, params):
        """生成更新条件"""
        return {"id": params.get("id")}  # 假设通过id进行更新


class WafAttackAggregatedByTypeView(BaseSecurityView, ViewItem):
    model = WafAttackAggregatedByType

    def get(self):
        return self.get_aggregated_data(self.model.attack_type)


class WafAttackAggregatedByIpView(BaseSecurityView, ViewItem):
    model = WafAttackAggregatedByIp

    def get(self):
        return self.get_aggregated_data(self.model.ip_address)


class WafBotIpWhitelistView(BaseSecurityView, ViewItem):
    model = WafBotIpWhitelist

    def get_query_filters(self):
        """ 查询过滤条件"""
        return self.model.query.filter(*[
            or_(
                self.model.ip_address.like(f"%{self.keyword}%"),
                self.model.description.like(f"%{self.keyword}%"),
            )
        ]) if self.keyword else self.model.query.filter()

    def get(self):
        return super().get(orderColumn="active,date_added", orderDir="asc,desc")

    def get_update_conditions(self, params):
        """生成更新条件"""
        if "ip_id" in params and params["ip_id"] is not None:
            return {"ip_id": params["ip_id"]}
        else:
            return None  # 或者返回一个空字典 {}, 取决于 update_record 方法的实现


class WafBotRestrictUrisView(BaseSecurityView, ViewItem):
    model = WafBotRestrictedUris

    def get_query_filters(self):
        """ 查询过滤条件"""
        return self.model.query.filter(*[
            or_(
                self.model.uri.like(f"%{self.keyword}%"),
                self.model.description.like(f"%{self.keyword}%"),
            )
        ]) if self.keyword else self.model.query.filter()

    def get(self):
        return super().get(orderColumn="active,date_added", orderDir="asc,desc")

    def get_update_conditions(self, params):
        """生成更新条件"""
        if "uri_id" in params and params["uri_id"] is not None:
            return {"uri_id": params["uri_id"]}
        else:
            return None  # 或者返回一个空字典 {}, 取决于 update_record 方法的实现


class WafBotMonitoredTokensView(BaseSecurityView, ViewItem):
    model = WafBotMonitoredTokens

    def get_query_filters(self):
        """ 查询过滤条件"""
        return self.model.query.filter()

    def get_update_conditions(self, params):
        """生成更新条件"""
        if "token_id" in params and params["token_id"] is not None:
            return {"token_id": params["token_id"]}
        else:
            return None  # 或者返回一个空字典 {}, 取决于 update_record 方法的实现


class WafFlowctrlConfigView(BaseSecurityView, ViewItem):
    model = WafFlowctrlConfig

    def get_query_filters(self):
        """ 查询过滤条件"""
        return self.model.query.filter(*[
            or_(
                self.model.rule_name.like(f"%{self.keyword}%"),
                self.model.request_limit.like(f"%{self.keyword}%"),
                self.model.flow_key.like(f"%{self.keyword}%"),
                self.model.description.like(f"%{self.keyword}%"),
            )
        ]) if self.keyword else self.model.query.filter()

    def get(self):
        response, status = super().get()
        data = response.get_json()
        data.update({"CHOICE_ACTIVE": self.model.CHOICE_ACTIVE})
        return jsonify(data), status

    def get_update_conditions(self, params):
        """生成更新条件"""
        if "id" in params and params["id"] is not None:
            return {"id": params["id"]}
        else:
            return None  # 或者返回一个空字典 {}, 取决于 update_record 方法的实现


class WafFlowctrlRuleView(BaseSecurityView, ViewItem):
    model = WafFlowctrlRule

    def get_query_filters(self):
        """ 查询过滤条件"""
        return self.model.query.filter()

    def get_update_conditions(self, params):
        """生成更新条件"""
        if "condition_id" in params and params["condition_id"] is not None:
            return {"condition_id": params["condition_id"]}
        else:
            return None  # 或者返回一个空字典 {}, 取决于 update_record 方法的实现


class WafPoliciesView(BaseSecurityView, ViewItem):
    model = WafPolicies

    def get_query_filters(self):
        """ 查询过滤条件"""
        return self.model.query.filter()

    def get_update_conditions(self, params):
        """生成更新条件"""
        if "id" in params and params["id"] is not None:
            return {"id": params["id"]}
        else:
            return None  # 或者返回一个空字典 {}, 取决于 update_record 方法的实现


class WafPolicyRulesView(BaseSecurityView, ViewItem):
    model = WafPolicyRules

    def get_query_filters(self):
        """ 查询过滤条件"""
        return self.model.query.filter()

    def get_update_conditions(self, params):
        """生成更新条件"""
        if "id" in params and params["id"] is not None:
            return {"id": params["id"]}
        else:
            return None  # 或者返回一个空字典 {}, 取决于 update_record 方法的实现


class WafSecurityPoliciesView(BaseSecurityView, ViewItem):
    model = WafSecurityPolicies

    def get_query_filters(self):
        """ 查询过滤条件"""
        return self.model.query.filter()

    def get_update_conditions(self, params):
        """生成更新条件"""
        if "id" in params and params["id"] is not None:
            return {"id": params["id"]}
        else:
            return None  # 或者返回一个空字典 {}, 取决于 update_record 方法的实现


class WafSourceDetailsView(BaseSecurityView, ViewItem):
    model = WafSourceDetails

    def get_query_filters(self):
        """ 查询过滤条件"""
        return self.model.query.filter()

    def get_update_conditions(self, params):
        """生成更新条件"""
        if "id" in params and params["id"] is not None:
            return {"id": params["id"]}
        else:
            return None  # 或者返回一个空字典 {}, 取决于 update_record 方法的实现


class WafBaseConfigView(BaseSecurityView, ViewItem):
    model = WafBaseConfig

    def get_query_filters(self):
        """ 查询过滤条件"""
        return self.model.query.filter(*[
            or_(
                self.model.config_name.like(f"%{self.keyword}%"),
                self.model.config_title.like(f"%{self.keyword}%"),
            )
        ]) if self.keyword else self.model.query.filter()

    def get(self):
        response, status = super().get()
        data = response.get_json()
        data.update({"CHOICE_LEVEL_ANTI": self.model.CHOICE_LEVEL_ANTI,
                     "CHOICE_LEVEL_FLOW": self.model.CHOICE_LEVEL_FLOW,
                     "CHOICE_LEVEL_BLACK": self.model.CHOICE_LEVEL_BLACK})

        return jsonify(data), status

    def get_update_conditions(self, params):
        """生成更新条件"""
        if "id" in params and params["id"] is not None:
            return {"id": params["id"]}
        else:
            return None  # 或者返回一个空字典 {}, 取决于 update_record 方法的实现


def get_redis_connection():
    redis_config = config_read['redis-loonflow']
    redis_host = redis_config.get('host')
    redis_port = redis_config.get('port')
    redis_password = redis_config.get('password')
    r = redis.StrictRedis(
        host=redis_host,
        port=redis_port,
        password=redis_password,
        db=2
    )
    return r


def waf_suspip_captchapass_scan():
    r = get_redis_connection()
    cursor = 0
    susp_keys = []
    captcha_keys = []

    while True:
        try:
            cursor, result = r.scan(cursor, match='SuspIP*', count=500)
            susp_keys.extend(result)
            if cursor == 0:
                break
        except redis.RedisError as e:
            return jsonify({"error": str(e)}), 500

    while True:
        try:
            cursor, result = r.scan(cursor, match='CAPTCHA_PASS*', count=500)
            captcha_keys.extend(result)
            if cursor == 0:
                break
        except redis.RedisError as e:
            return jsonify({"error": str(e)}), 500

    susp_keys_list = [{"key": key.decode('utf-8').split(':')[1]} for key in susp_keys]
    captcha_keys_list = [{"key": key.decode('utf-8').split(':')[1]} for key in captcha_keys]
    return jsonify(
        {"data": {"susp_keys": susp_keys_list, "captcha_keys": captcha_keys_list},
         "susp_count": len(susp_keys),
         "captcha_count": len(captcha_keys)}), 200


def waf_captcha_pass():
    r = get_redis_connection()
    key = request.args.get('key')

    if not key:
        return jsonify({"error": "Key is required"}), 400
    keyStr = "CAPTCHA_PASS:" + key
    try:
        value = r.get(keyStr)
        value = value.decode('utf-8') if value else value
        return jsonify({"data": {keyStr: value}, "message": f"keyStr: '{keyStr}', value: {value}"}), 200
    except redis.RedisError as e:
        return jsonify({"error": str(e)}), 500


class WafSuspView(views.MethodView):
    name = "waf_susp_ip"
    url = "/waf/susp/ip"

    def get(self):
        r = get_redis_connection()
        key = request.args.get('key')
        keyStr = "SuspIP:" + key
        if not key:
            return jsonify({"error": "Key is required"}), 400
        try:
            value = r.get(keyStr)
            value = value.decode('utf-8') if value else value
            return jsonify({"data": {keyStr: value}, "message": f"keyStr: '{keyStr}', value: {value}"}), 200
        except redis.RedisError as e:
            return jsonify({"error": str(e)}), 500

    def post(self):
        r = get_redis_connection()
        data_raw = get_response(request)
        key = data_raw.get('ip_address')
        value = data_raw.get('active')
        keyStr = "SuspIP:" + key
        ttl = int(data_raw.get('ttl', 3600))  # 获取ttl参数，默认为3600秒
        if not key:
            return jsonify({"error": "Key is required"}), 400
        try:
            result = r.setnx(keyStr, value)
            if result:
                # 如果键不存在，设置过期时间
                if ttl != int("-1"):
                    r.expire(keyStr, ttl)
                return jsonify({"message": f"Key: {keyStr}, value: {value} , ttl: {ttl} added successfully"}), 200
            return jsonify({"message": f"Key: {keyStr} already exists"}), 200
        except redis.RedisError as e:
            return jsonify({"error": str(e)}), 500

    def delete(self):
        r = get_redis_connection()
        data_raw = get_response(request)
        key = data_raw.get('key')
        keyStr = "SuspIP:" + key
        if not key:
            return jsonify({"error": "Key is required"}), 400

        try:
            value = r.get(keyStr)
            if value:
                r.delete(keyStr)
                return jsonify({"message": f"Key {keyStr} deleted successfully"}), 200
            else:
                return jsonify({"error": "Key not found"}), 404
        except redis.RedisError as e:
            return jsonify({"error": str(e)}), 500


ModelViewList = [
    WafAttackAggregatedByTypeView, WafAttackAggregatedByIpView,
    WafBotIpWhitelistView, WafBotRestrictUrisView, WafBotMonitoredTokensView,
    WafFlowctrlConfigView, WafFlowctrlRuleView,
    WafPoliciesView, WafPolicyRulesView,
    WafSecurityPoliciesView, WafSourceDetailsView,
    WafBaseConfigView
]

ViewList = [
    WafSuspView
]

for view_class in ModelViewList:
    name, url = view_class.get_view_func()
    view_func = view_class.as_view(name)
    waf_bp.add_url_rule(url, view_func=view_func)

for view_class in ViewList:
    # ic(view_class.name, view_class.url)
    view_func = view_class.as_view(view_class.name)
    waf_bp.add_url_rule(view_class.url, view_func=view_func)

waf_bp.add_url_rule(rule='/waf/query/table/column', view_func=query_table_info, methods=['GET'])
waf_bp.add_url_rule(rule='/waf/susp/captcha/scan', view_func=waf_suspip_captchapass_scan, methods=['GET'])
waf_bp.add_url_rule(rule='/waf/captcha/pass', view_func=waf_captcha_pass, methods=['GET'])
