#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
@Title:     rview.py
@Date:      2023/6/15 15:57
@Author:    wanglh
@Desc：     版本发布接口
"""

from flask import Blueprint, jsonify, request

from extension import db
from utils import get_response
from utils.release import PlayProd

rview_bp = Blueprint('release_view', __name__, url_prefix='/release/api/v1')


@rview_bp.route("/update/service", methods=['POST'])
def update_one():
    """
    版本更新
    Args:
        **kwargs: 关键字参数，包含以下参数：
            - upgrade_id (str): 数据表变更ID。
            - d_env (str): 变更环境，可以是 A 或 B。
            - services (str): 逗号分隔的服务名，用于指定要发布的服务。

    Returns:
        None: 该函数没有返回值。
    """

    #
    try:
        # 开启 维护状态
        db.session.execute('INSERT INTO system_config (scenario,cre_time) values (2,now());')
        db.session.commit()
        db.session.close()

        # 发布
        kwargs = get_response(request)
        message = PlayProd.main(**kwargs)
        # 关闭 维护状态
        db.session.execute('INSERT INTO system_config (scenario,cre_time) values (1,now());')
        db.session.commit()

    except Exception as e:
        message = f"后端异常,{e}"
        return jsonify({"code": 1, "msg": message}), 500
    finally:
        db.session.close()
    return jsonify({"code": 0, "msg": message}), 200
