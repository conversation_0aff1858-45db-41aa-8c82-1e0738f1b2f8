#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
@Title:     elk_monitor.py
@Date:      2025/3/4
@Author:    dongxin
@Desc:      ELK告警规则配置API
"""

from flask import Blueprint, abort, request, jsonify, current_app
from flask.views import MethodView
from sqlalchemy import insert, text, update, or_, and_
from sqlalchemy.exc import SQLAlchemyError
import json
import traceback
from datetime import datetime, timedelta

from utils import *

elk_bp = Blueprint("monitoring_elk", __name__, url_prefix="/monitoring/api/v1/elk")

HTTP_NOT_FOUND = 404
HTTP_SERVER_ERROR = 500
HTTP_SUCCESS = 200
HTTP_BAD_REQUEST = 400


class BaseApi(MethodView):
    def __init__(self):
        super().__init__()
        self.applicant = request.headers.get('X-User', 'unknown')


class ElkRuleAPI(BaseApi):
    """ELK告警规则API，处理规则的CRUD操作"""

    def __init__(self):
        super().__init__()
        self.model = ElkAlertRules

    def get(self, rule_id=None):
        """获取告警规则列表或单个规则"""
        data = get_response(request)
        page_no = int(data.get("pageNo", 1))
        page_size = int(data.get("pageSize", 10))
        keyword = data.get("keyword", "")

        try:
            if rule_id:
                # 获取特定ID的规则及其条件
                rule = self.model.query.filter_by(id=rule_id).first()
                if not rule:
                    return jsonify({"error": "规则不存在"}), HTTP_NOT_FOUND

                rule_dict = rule.to_dict()
                # 查询关联的条件
                conditions = ElkRuleConditions.query.filter_by(rule_id=rule_id).all()
                rule_dict['conditions'] = [condition.to_dict() for condition in conditions]

                return jsonify({"data": rule_dict}), HTTP_SUCCESS
            else:
                # 构建查询过滤条件
                query = self.model.query

                if keyword:
                    query = query.filter(
                        or_(
                            self.model.rule_name.like(f"%{keyword}%"),
                            self.model.target_value.like(f"%{keyword}%")
                        )
                    )

                # 获取总记录数
                total = query.count()

                # 分页查询规则
                rules = query.order_by(self.model.priority.asc(), self.model.id.desc()) \
                    .limit(page_size).offset((page_no - 1) * page_size).all()

                # 查询每个规则的条件
                result = []
                for rule in rules:
                    rule_dict = rule.to_dict()
                    conditions = ElkRuleConditions.query.filter_by(rule_id=rule.id).all()
                    rule_dict['conditions'] = [condition.to_dict() for condition in conditions]
                    result.append(rule_dict)

                return jsonify({"data": result, "total": total}), HTTP_SUCCESS

        except Exception as e:
            current_app.logger.error(f"获取告警规则异常: {str(e)}")
            current_app.logger.error(traceback.format_exc())
            return jsonify({"error": f"获取告警规则失败: {str(e)}"}), HTTP_SERVER_ERROR

    def post(self):
        """创建新的告警规则"""
        data = request.get_json()

        # 验证必要字段
        required_fields = ['rule_name', 'target_value', 'frequency_interval', 'severity']
        for field in required_fields:
            if field not in data:
                return jsonify({"error": f"缺少必要字段: {field}"}), HTTP_BAD_REQUEST

        # 验证条件
        if 'conditions' not in data or not data['conditions']:
            return jsonify({"error": "至少需要一个告警条件"}), HTTP_BAD_REQUEST

        try:
            # 准备规则数据
            rule_data = {
                'rule_name': data['rule_name'],
                'target_value': data['target_value'],
                'priority': data.get('priority', 10),
                'frequency_interval': data['frequency_interval'],
                'severity': data['severity'],
                'notification_channels': data.get('notification_channels', ''),
                'created_at': datetime.now(),
                'updated_at': datetime.now()
            }

            # 创建规则
            new_rule = self.model(**rule_data)
            db.session.add(new_rule)
            db.session.flush()  # 获取自增ID

            # 创建条件
            for condition in data['conditions']:
                new_condition = ElkRuleConditions(
                    rule_id=new_rule.id,
                    metric_type=condition['metric_type'],
                    threshold_value=condition['threshold_value'],
                    operator=condition.get('operator', '>='),
                    created_at=datetime.now(),
                    updated_at=datetime.now()
                )
                db.session.add(new_condition)

            db.session.commit()

            # 记录操作日志
            log_message = f"创建告警规则: {data['rule_name']}, 目标: {data['target_value']}"
            current_app.logger.info(log_message)

            return jsonify({
                "message": "创建告警规则成功",
                "rule_id": new_rule.id
            }), HTTP_SUCCESS

        except Exception as e:
            db.session.rollback()
            current_app.logger.error(f"创建告警规则异常: {str(e)}")
            current_app.logger.error(traceback.format_exc())
            return jsonify({"error": f"创建告警规则失败: {str(e)}"}), HTTP_SERVER_ERROR

    def put(self, rule_id):
        """更新告警规则"""
        if not rule_id:
            return jsonify({"error": "规则ID不能为空"}), HTTP_BAD_REQUEST

        data = request.get_json()

        # 检查规则是否存在
        rule = self.model.query.filter_by(id=rule_id).first()
        if not rule:
            return jsonify({"error": "规则不存在"}), HTTP_NOT_FOUND

        try:
            # 更新规则
            rule.rule_name = data.get('rule_name', rule.rule_name)
            rule.target_value = data.get('target_value', rule.target_value)
            rule.priority = data.get('priority', rule.priority)
            rule.frequency_interval = data.get('frequency_interval', rule.frequency_interval)
            rule.severity = data.get('severity', rule.severity)
            rule.notification_channels = data.get('notification_channels', rule.notification_channels)
            rule.updated_at = datetime.now()

            # 删除旧条件
            ElkRuleConditions.query.filter_by(rule_id=rule_id).delete()

            # 创建新条件
            if 'conditions' in data and data['conditions']:
                for condition in data['conditions']:
                    new_condition = ElkRuleConditions(
                        rule_id=rule_id,
                        metric_type=condition['metric_type'],
                        threshold_value=condition['threshold_value'],
                        operator=condition.get('operator', '>='),
                        created_at=datetime.now(),
                        updated_at=datetime.now()
                    )
                    db.session.add(new_condition)

            db.session.commit()

            # 记录操作日志
            log_message = f"更新告警规则: {rule.rule_name}, ID: {rule_id}"
            current_app.logger.info(log_message)

            return jsonify({
                "message": "更新告警规则成功"
            }), HTTP_SUCCESS

        except Exception as e:
            db.session.rollback()
            current_app.logger.error(f"更新告警规则异常: {str(e)}")
            current_app.logger.error(traceback.format_exc())
            return jsonify({"error": f"更新告警规则失败: {str(e)}"}), HTTP_SERVER_ERROR

    def delete(self, rule_id):
        """删除告警规则"""
        if not rule_id:
            return jsonify({"error": "规则ID不能为空"}), HTTP_BAD_REQUEST

        # 检查规则是否存在
        rule = self.model.query.filter_by(id=rule_id).first()
        if not rule:
            return jsonify({"error": "规则不存在"}), HTTP_NOT_FOUND

        try:
            # 记录规则名称用于日志
            rule_name = rule.rule_name

            # 删除关联的条件
            ElkRuleConditions.query.filter_by(rule_id=rule_id).delete()

            # 删除规则
            db.session.delete(rule)

            # 标记相关的告警历史为已恢复
            current_time = datetime.now()
            ElkAlertHistory.query.filter_by(rule_id=rule_id).filter(
                ElkAlertHistory.resolved_time.is_(None)
            ).update({
                'resolved_time': current_time,
                'message': '规则已删除，告警自动恢复'
            })

            db.session.commit()

            # 记录操作日志
            log_message = f"删除告警规则: {rule_name}, ID: {rule_id}"
            current_app.logger.info(log_message)

            return jsonify({
                "message": "删除告警规则成功"
            }), HTTP_SUCCESS

        except Exception as e:
            db.session.rollback()
            current_app.logger.error(f"删除告警规则异常: {str(e)}")
            current_app.logger.error(traceback.format_exc())
            return jsonify({"error": f"删除告警规则失败: {str(e)}"}), HTTP_SERVER_ERROR


class ElkAlertHistoryAPI(BaseApi):
    """ELK告警历史API，处理告警历史记录查询"""

    def __init__(self):
        super().__init__()
        self.model = ElkAlertHistory  # 假设模型已经定义

    def get(self):
        """获取告警历史记录"""
        data = get_response(request)
        page_no = int(data.get("pageNo", 1))
        page_size = int(data.get("pageSize", 10))
        keyword = data.get("keyword", "")
        rule_id = data.get("ruleId")
        status = data.get("status")  # active 或 resolved
        npath = data.get("npath")  # 添加npath过滤
        start_date = data.get("startDate")
        end_date = data.get("endDate")

        try:
            # 构建查询过滤条件
            query = self.model.query

            if rule_id:
                query = query.filter(self.model.rule_id == rule_id)

            if status == "active":
                query = query.filter(self.model.resolved_time.is_(None))
            elif status == "resolved":
                query = query.filter(self.model.resolved_time.isnot(None))

            if npath:  # 添加npath过滤
                query = query.filter(self.model.npath == npath)

            if start_date and end_date:
                query = query.filter(
                    and_(
                        self.model.alert_time >= f"{start_date} 00:00:00",
                        self.model.alert_time <= f"{end_date} 23:59:59"
                    )
                )

            if keyword:
                query = query.filter(
                    or_(
                        self.model.npath.like(f"%{keyword}%"),
                        self.model.message.like(f"%{keyword}%")
                    )
                )

            # 获取总记录数
            total = query.count()

            # 分页查询告警历史
            alerts = query.order_by(self.model.alert_time.desc()) \
                .limit(page_size).offset((page_no - 1) * page_size).all()

            # 查询关联的规则信息
            result = []
            for alert in alerts:
                alert_dict = alert.to_dict()

                # 解析JSON格式的triggered_conditions字段
                if alert.triggered_conditions:
                    try:
                        alert_dict['triggered_conditions'] = json.loads(alert.triggered_conditions)
                    except:
                        alert_dict['triggered_conditions'] = []

                # 查询关联的规则名称
                rule = ElkAlertRules.query.filter_by(id=alert.rule_id).first()
                if rule:
                    alert_dict['rule_name'] = rule.rule_name

                result.append(alert_dict)

            return jsonify({"data": result, "total": total}), HTTP_SUCCESS

        except Exception as e:
            current_app.logger.error(f"获取告警历史异常: {str(e)}")
            current_app.logger.error(traceback.format_exc())
            return jsonify({"error": f"获取告警历史失败: {str(e)}"}), HTTP_SERVER_ERROR


@elk_bp.route('/http-aggregation', methods=['GET'])
def http_aggregation():
    """获取HTTP数据聚合与告警状态"""
    data = get_response(request)
    page_no = int(data.get("pageNo", 1))
    page_size = int(data.get("pageSize", 50))
    keyword = data.get("keyword", "")
    time_range = int(data.get("timeRange", 5))  # 默认5分钟
    sort_field = data.get("sortField", "total_count")  # 默认按请求量排序
    sort_order = data.get("sortOrder", "desc")  # 默认降序

    # 验证并处理排序字段
    valid_sort_fields = {
        "total_count", "error_4xx", "error_5xx",
        "avg_response_time", "error_4xx_ratio", "error_5xx_ratio"
    }

    if sort_field not in valid_sort_fields:
        sort_field = "total_count"

    sort_direction = "DESC" if sort_order.lower() == "desc" else "ASC"

    try:
        # 构建SQL查询
        sql = """
        SELECT
            npath,
            SUM(`count`) AS total_count,
            SUM(
                CASE
                    WHEN (CAST(status AS SIGNED) BETWEEN 400 AND 499)
                         OR (bk_status IS NOT NULL AND CAST(bk_status AS SIGNED) BETWEEN 400 AND 499)
                    THEN `count`
                    ELSE 0
                END
            ) AS error_4xx,
            SUM(
                CASE
                    WHEN (CAST(status AS SIGNED) BETWEEN 500 AND 599)
                         OR (bk_status IS NOT NULL AND CAST(bk_status AS SIGNED) BETWEEN 500 AND 599)
                    THEN `count`
                    ELSE 0
                END
            ) AS error_5xx,
            ROUND(SUM(avg_time * `count`) / SUM(`count`), 4) AS avg_response_time,
            ROUND(
                SUM(
                    CASE
                        WHEN left(status,1) = '4'
                             OR (bk_status IS NOT NULL AND left(bk_status,1)='4')
                        THEN `count`
                        ELSE 0
                    END
                ) / SUM(`count`), 4
            ) AS error_4xx_ratio,
            ROUND(
                SUM(
                    CASE
                        WHEN left(status,1) = '5'
                             OR (bk_status IS NOT NULL AND left(bk_status,1)='5')
                        THEN `count`
                        ELSE 0
                    END
                ) / SUM(`count`), 4
            ) AS error_5xx_ratio
        FROM elk_npath_status_5
        WHERE c_time >= NOW() - INTERVAL :time_range MINUTE
          AND right(npath, 3) <> '.js'
          AND right(npath, 4) <> '.css'
          AND right(npath, 5) <> '.html'
          AND right(npath, 4) <> '.png'
          AND npath <> '/shop/'
          AND npath <> '/api/v2/links'
          AND npath <> '/'
          AND npath not like '/weshop%'
          AND npath not like '/zjd%'
        """

        # 添加关键字过滤
        if keyword:
            sql += f" AND npath LIKE '%{keyword}%'"

        sql += """
        GROUP BY npath
        ORDER BY {sort_field} {sort_direction}
        LIMIT :limit OFFSET :offset
        """.format(sort_field=sort_field, sort_direction=sort_direction)

        # 执行查询
        result = db.session.execute(text(sql), {
            'time_range': time_range,
            'limit': page_size,
            'offset': (page_no - 1) * page_size
        })

        # 转换为字典列表
        aggregation_data = [dict(row) for row in result]

        # 提取所有 npath 为一个集合
        all_npaths = {item['npath'] for item in aggregation_data}

        # 获取总记录数
        count_sql = """
        SELECT COUNT(DISTINCT npath) as total
        FROM elk_npath_status_5
        WHERE c_time >= NOW() - INTERVAL :time_range MINUTE
          AND right(npath, 3) <> '.js'
          AND right(npath, 4) <> '.css'
          AND right(npath, 5) <> '.html'
          AND right(npath, 4) <> '.png'
          AND npath <> '/shop/'
          AND npath <> '/api/v2/links'
          AND npath <> '/'
          AND npath not like '/weshop%'
          AND npath not like '/zjd%'
        """

        if keyword:
            count_sql += f" AND npath LIKE '%{keyword}%'"

        count_result = db.session.execute(text(count_sql), {'time_range': time_range}).first()
        total = count_result['total'] if count_result else 0

        # 获取活跃告警
        active_alerts = ElkAlertHistory.query.filter(
            ElkAlertHistory.resolved_time.is_(None)
        ).all()

        # 创建npath到告警信息的映射
        alert_map = {}
        for alert in active_alerts:
            # 修复时区问题: 确保时间转换正确
            alert_dict = alert.to_dict()
            if 'alert_time' in alert_dict and alert_dict['alert_time'] and isinstance(alert_dict['alert_time'], datetime):
                alert_dict['alert_time'] = alert_dict['alert_time'].strftime('%Y-%m-%d %H:%M:%S+08:00')
            if 'resolved_time' in alert_dict and alert_dict['resolved_time'] and isinstance(alert_dict['resolved_time'], datetime):
                alert_dict['resolved_time'] = alert_dict['resolved_time'].strftime('%Y-%m-%d %H:%M:%S+08:00')

            if alert.npath in alert_map:
                alert_map[alert.npath].append(alert_dict)
            else:
                alert_map[alert.npath] = [alert_dict]

        # 查询所有告警规则
        all_rules = ElkAlertRules.query.all()

        # 创建规则到 npath 的映射，并跟踪哪些 npath 有配置规则
        rule_map = {}
        configured_npaths = set()  # 只包含有特定规则的路径

        for rule in all_rules:
            # 对于全局规则，适用于所有 npath，但不计入已配置规则的接口数量
            if rule.target_value == 'GLOBAL':
                for npath in all_npaths:
                    if npath not in rule_map:
                        rule_map[npath] = []
                    rule_map[npath].append({
                        'id': rule.id,
                        'rule_name': rule.rule_name,
                        'is_global': True
                    })
                    # 注意：不将全局规则添加到 configured_npaths
            else:
                # 对于特定规则，检查它是否适用于当前的任何 npath
                for npath in all_npaths:
                    # 检查规则 target_value 是否与 npath 匹配
                    if rule.target_value in npath:
                        if npath not in rule_map:
                            rule_map[npath] = []
                        rule_map[npath].append({
                            'id': rule.id,
                            'rule_name': rule.rule_name,
                            'is_global': False
                        })
                        configured_npaths.add(npath)  # 只将特定规则添加到 configured_npaths

        # 合并告警和规则信息到聚合数据
        for item in aggregation_data:
            npath = item['npath']
            item['alerts'] = alert_map.get(npath, [])
            item['has_alerts'] = len(item['alerts']) > 0
            item['has_specific_rules'] = npath in configured_npaths  # 只检查特定规则
            item['rules'] = rule_map.get(npath, [])

        # 如果前端要求优先显示有告警的项目，则需要二次排序
        if data.get("prioritizeAlerts", "true").lower() == "true":
            aggregation_data.sort(key=lambda x: (not x['has_alerts'], -x['total_count'] if sort_field == "total_count" else 0))

        return jsonify({
            "data": aggregation_data,
            "total": total,
            "activeAlertCount": len(active_alerts),
            "alertedEndpointCount": len(alert_map),
            "configuredEndpointCount": len(configured_npaths)  # 只计算有特定规则的路径数量
        }), HTTP_SUCCESS

    except Exception as e:
        current_app.logger.error(f"获取HTTP数据聚合异常: {str(e)}")
        current_app.logger.error(traceback.format_exc())
        return jsonify({"error": f"获取HTTP数据聚合失败: {str(e)}"}), HTTP_SERVER_ERROR


@elk_bp.route('/npath-metrics', methods=['GET'])
def npath_metrics():
    """获取特定npath的指标数据，用于展示折线图"""
    data = get_response(request)
    npath = data.get("npath")
    time_range = int(data.get("timeRange", 6))  # 默认6小时
    start_time = data.get("startTime")  # 自定义开始时间
    end_time = data.get("endTime")  # 自定义结束时间
    compare_start = data.get("compareStart")  # 对比开始时间
    compare_end = data.get("compareEnd")  # 对比结束时间

    if not npath:
        return jsonify({"error": "必须指定npath参数"}), HTTP_BAD_REQUEST

    try:
        # 确定数据源表和时间间隔
        if time_range <= 24 and not (start_time and end_time):
            # 24小时内，使用5分钟粒度表
            table = "elk_npath_status_5"
            interval = 5
        else:
            # 超过24小时，使用60分钟粒度表
            table = "elk_npath_status_60"
            interval = 60

        # 构建基本SQL
        metrics_sql = f"""
        SELECT 
            c_time,
            SUM(`count`) AS total_count,
            SUM(
                CASE
                    WHEN (CAST(status AS SIGNED) BETWEEN 400 AND 499)
                         OR (bk_status IS NOT NULL AND CAST(bk_status AS SIGNED) BETWEEN 400 AND 499)
                    THEN `count`
                    ELSE 0
                END
            ) AS error_4xx,
            SUM(
                CASE
                    WHEN (CAST(status AS SIGNED) BETWEEN 500 AND 599)
                         OR (bk_status IS NOT NULL AND CAST(bk_status AS SIGNED) BETWEEN 500 AND 599)
                    THEN `count`
                    ELSE 0
                END
            ) AS error_5xx,
            ROUND(SUM(avg_time * `count`) / NULLIF(SUM(`count`), 0), 4) AS avg_response_time,
            ROUND(SUM(avg_size * `count`) / NULLIF(SUM(`count`), 0), 4) AS avg_size
        FROM {table}
        WHERE npath = :npath
        """

        # 确定时间范围和参数
        params = {'npath': npath}
        timeaxis_start = None
        timeaxis_end = None

        if start_time and end_time:
            metrics_sql += " AND c_time BETWEEN :start_time AND :end_time"
            params['start_time'] = start_time
            params['end_time'] = end_time
            timeaxis_start = start_time
            timeaxis_end = end_time
        else:
            current_time = datetime.now()
            start_datetime = current_time - timedelta(hours=time_range)
            metrics_sql += " AND c_time >= :start_datetime"
            params['start_datetime'] = start_datetime
            timeaxis_start = start_datetime.strftime('%Y-%m-%d %H:%M:%S')
            timeaxis_end = current_time.strftime('%Y-%m-%d %H:%M:%S')

        # 按时间分组
        metrics_sql += " GROUP BY c_time ORDER BY c_time"

        # 执行查询
        result = db.session.execute(text(metrics_sql), params)
        metrics_data = [dict(row) for row in result]

        # 格式化时间
        for item in metrics_data:
            if 'c_time' in item and item['c_time'] and isinstance(item['c_time'], datetime):
                item['c_time'] = item['c_time'].strftime('%Y-%m-%d %H:%M:%S')

        # 获取对比数据（如果有请求）
        compare_data = []
        compare_timeaxis_start = None
        compare_timeaxis_end = None

        if compare_start and compare_end:
            compare_sql = f"""
            SELECT 
                c_time,
                SUM(`count`) AS total_count,
                SUM(
                    CASE
                        WHEN (CAST(status AS SIGNED) BETWEEN 400 AND 499)
                             OR (bk_status IS NOT NULL AND CAST(bk_status AS SIGNED) BETWEEN 400 AND 499)
                        THEN `count`
                        ELSE 0
                    END
                ) AS error_4xx,
                SUM(
                    CASE
                        WHEN (CAST(status AS SIGNED) BETWEEN 500 AND 599)
                             OR (bk_status IS NOT NULL AND CAST(bk_status AS SIGNED) BETWEEN 500 AND 599)
                        THEN `count`
                        ELSE 0
                    END
                ) AS error_5xx,
                ROUND(SUM(avg_time * `count`) / NULLIF(SUM(`count`), 0), 4) AS avg_response_time,
                ROUND(SUM(avg_size * `count`) / NULLIF(SUM(`count`), 0), 4) AS avg_size
            FROM elk_npath_status_60
            WHERE npath = :npath
              AND c_time BETWEEN :compare_start AND :compare_end
            GROUP BY c_time
            ORDER BY c_time
            """

            compare_result = db.session.execute(text(compare_sql), {
                'npath': npath,
                'compare_start': compare_start,
                'compare_end': compare_end
            })

            compare_data = [dict(row) for row in compare_result]

            # 格式化对比数据的时间
            for item in compare_data:
                if 'c_time' in item and item['c_time'] and isinstance(item['c_time'], datetime):
                    item['c_time'] = item['c_time'].strftime('%Y-%m-%d %H:%M:%S')

            compare_timeaxis_start = compare_start
            compare_timeaxis_end = compare_end

        return jsonify({
            "data": metrics_data,
            "compareData": compare_data,
            "interval": interval,
            "npath": npath,
            "timeRange": {
                "start": timeaxis_start,
                "end": timeaxis_end
            },
            "compareTimeRange": {
                "start": compare_timeaxis_start,
                "end": compare_timeaxis_end
            } if compare_start and compare_end else None
        }), HTTP_SUCCESS

    except Exception as e:
        current_app.logger.error(f"获取npath指标数据异常: {str(e)}")
        current_app.logger.error(traceback.format_exc())
        return jsonify({"error": f"获取npath指标数据失败: {str(e)}"}), HTTP_SERVER_ERROR


# 注册路由
rule_api = ElkRuleAPI.as_view('elk_rule_api')
elk_bp.add_url_rule('/rules', view_func=rule_api, methods=['GET', 'POST'])
elk_bp.add_url_rule('/rules/<int:rule_id>', view_func=rule_api, methods=['GET', 'PUT', 'DELETE'])

alert_api = ElkAlertHistoryAPI.as_view('elk_alert_api')
elk_bp.add_url_rule('/alerts', view_func=alert_api, methods=['GET'])