#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
@Title:     repair.py
@Date:      2023/6/25 10:44
@Author:    wanglh
@Desc：     阿里云腾讯云关于Pay和Order信息现网数据更新
"""

from flask import Blueprint, jsonify, request

from utils.repairOrder.RepairOneOrder import RepairOneOrder

repair_bp = Blueprint('repairPayOrder', __name__, url_prefix='/repair/api/v1')


@repair_bp.route('/order')
def repair_order():
    orderId = request.args.get('orderId')
    if orderId:
        repair: RepairOneOrder = RepairOneOrder()
        if orderId is None or len(orderId) == 0:
            return repair.makeResponse('订单号为空')

        albumId: str = repair.getAlbumIdByOrderIdFromBank(orderId)
        if albumId is not None:
            # albumId: str = "A202006061605215720103720"
            payErrMsg = repair.processPay(albumId, orderId)
            orderErrMsg = repair.processOrder(albumId, orderId)
        else:
            payErrMsg = "不用处理"
            orderErrMsg = "不是普通订单、或收款码订单"

        oldCollectOrder = repair.processOldCollectCodeOrder(orderId)
        # return jsonify({'message': "支付数据："+payErrMsg+"。订单数据："+orderErrMsg+"。老收款码订单："+oldCollectOrder})
        return repair.makeResponse('支付数据：' + payErrMsg + '。订单数据：' + orderErrMsg + '。老收款码订单：' + oldCollectOrder), 200
    return jsonify({'msg': '参数缺失'}), 403
