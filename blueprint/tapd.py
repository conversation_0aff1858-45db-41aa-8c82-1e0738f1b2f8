#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
@Title:   tapd.py
@Date:    2022/4/29 10:12
@Author:  wanglh
@Desc:    TAPD 接口
"""

from flask import Blueprint, request

from utils import *

tapd_bp = Blueprint('tapd', __name__, url_prefix='/tapd/api/v1')


@tapd_bp.route("/bizline/query")
def get_items():
    """
    获取TAPD业务线列表
    :return: render_template
    """
    result = get_all_project()
    return jsonify({"code": 200, "data": result, "msg": "数据获取成功"})

    # return render_template("tapd.html", result=result)


@tapd_bp.route('/rsyncdata')
def tapd_rsync():
    """
    同步TAPD所有迭代信息
    :return: {'code':200,'data':'迭代信息'}
    """
    action = "TAPD信息同步"
    # 所有
    result = get_all_project()
    iterations_items = []

    # 同步迭代信息
    for item in result:
        result = get_iterations(item["tapd_id"])
        iterations_items.extend(result)
    # iterations_items = sorted(iterations_items, key=lambda x: x['startdate'], reverse=False)
    insert_iterations(iterations_items)
    save_to_operation_log(request, action, result=200)
    return jsonify({'code': 200, 'data': iterations_items})


@tapd_bp.route('/rsyncdata/workspace')
def tapd_rsync_workspace():
    """
    同步指定的tapd_name
    :param: tapd_name
    :return:
    """
    action = "TAPD信息同步"
    dataraw = get_response(request)
    task_filter = {
        or_(
            CiBusinessline.tapd_name == dataraw['tapd_name'],
            CiBusinessline.line_name == dataraw['tapd_name'],
        )
    }
    query = CiBusinessline.query.filter(*task_filter).first()
    workspace_id = query.tapd_id
    result = get_iterations(workspace_id)
    result = [i for i in result if i['name'] == dataraw['version_code']]
    result = sorted(result, key=lambda result: result["id"], reverse=True)
    save_to_operation_log(request, action, result=200)
    return jsonify({'code': 200, 'data': result})


@tapd_bp.route('/query/iteration', methods=['POST'])
def get_iteration_info():
    """
    获取当前项目业务线迭代列表
    :param
        tapd_name: 项目业务线名称
    :return: jsonify
    """
    dataraw = get_response(request)
    workspace = dataraw.get("tapd_name") if dataraw.get("tapd_name") else dataraw.get("line_name")
    items = get_all_project()
    for item in items:
        if item["tapd_name"] == workspace:
            result = get_iterations(item["tapd_id"])
            return jsonify({"code": 200, "data": result, "msg": "TAPD：当前项目业务线迭代列表，获取成功"})
    return jsonify({"code": 200, "msg": "TAPD：当前项目业务线迭代列表，获取失败"})


@tapd_bp.route("/query/stories", methods=['POST'])
def get_stories_info():
    """
    获取当前项目线的迭代需求列表
    :param
        tapd_name: 项目业务线名称
    :return: jsonify,并更新数据库（删除-->写入）
    """
    dataraw = get_response(request)  # 请求传递tapd_name
    items = get_all_project()  # 从接口直接获取items列表
    for item in items:
        if item["tapd_name"] == dataraw["tapd_name"]:
            result = get_stories(item["tapd_id"], dataraw["iteration_id"])
            return jsonify({"code": 200, "data": result, "msg": "获取成功"})
    return jsonify({"code": 200, "msg": "获取失败"})


@tapd_bp.route('/bugs/query')
def bugs_query():
    """
    需求：获取当前项目线当前版本的缺陷
    :param:
        tapd_name: 项目业务线名称
        version_report：当前业务线迭代version版本
    :return:
    """
    dataraw = get_response(request)
    try:
        query = CiBusinessline.query.filter_by(tapd_name=dataraw['tapd_name']).first()
        if query is not None:
            workspace_id = query.tapd_id
            bugs_data = get_bugs_count(workspace_id, dataraw['version_report'])
            current_app.logger.info("TAPD bugs-query 数据获取完成")
            return jsonify({"code": 200, "data": bugs_data, "msg": "TAPD bugs-query 数据获取完成"}), 200
        else:
            return jsonify({"msg": "没有找到对应的数据，无法获取 TAPD bugs"}), 400
    except Exception as e:
        return jsonify({"msg": f"TAPD bugs-query 数据获取失败, 具体原因：{e}"}), 500
