#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
@Title:   main.py
@Date:    2022/4/28 9:46
@Author:  Wangl<PERSON>
@Desc:    程序启动
"""
import logging
from logging.handlers import TimedRotatingFileHandler

import pytz
from celery import Celery
from flasgger import Swagger
from flask import Flask, has_request_context, request
from uvicorn.middleware.wsgi import WSGIMiddleware

from blueprint import *
from config import *
from extension import db


def openCors(app):
    from flask_cors import CORS
    CORS(app, supports_credentials=True)


class RequestFormatter(logging.Formatter):
    def format(self, record):
        if has_request_context():
            record.username = request.headers.get("X-User", "unknown")
            record.method = request.method
            record.path = request.path
            record.remote_addr = request.remote_addr
            record.url = request.url
        else:
            record.username = "unknown"
            record.method = "unknown"
            record.path = "unknown"
            record.remote_addr = "unknown"
            record.url = "unknown"
        return super().format(record)


def create_log(level):
    """配置flask日志"""

    flask_logger = logging.getLogger(__name__)  # 创建flask.app日志器

    # 如果已经配置过，直接返回
    if flask_logger.hasHandlers():
        return flask_logger

    # 创建控制台输出器 console_handler
    # 清除所有已存在的handlers，确保不会重复添加
    for handler in flask_logger.handlers[:]:
        flask_logger.removeHandler(handler)

    # 设置全局级别
    flask_logger.setLevel(level)

    # 创建控制台处理器
    console_handler = logging.StreamHandler()
    console_handler.setLevel(level)
    console_formatter = RequestFormatter(
        fmt="[%(levelname)s] - PID:%(process)d - %(username)s - %(filename)s - %(lineno)d - %(message)s"
    )
    console_handler.setFormatter(console_formatter)

    # 创建文件处理器
    file_handler = TimedRotatingFileHandler(
        "logs/flask.log",
        when="D",
        interval=1,
        backupCount=15,
        encoding="UTF-8",
        delay=False,
        utc=True,
    )
    file_formatter = RequestFormatter(
        fmt="[%(levelname)s] - PID:%(process)d - %(username)s - [%(asctime)s] - [%(method)s] %(path)s - %(remote_addr)s - %(filename)s:%(funcName)s - %(lineno)d - %(message)s"
    )
    file_handler.setFormatter(file_formatter)
    file_handler.setLevel("INFO")

    # 添加处理器
    flask_logger.addHandler(console_handler)
    flask_logger.addHandler(file_handler)

    # 防止日志向上层传递
    flask_logger.propagate = False
    return flask_logger


def create_app(celery=None):
    config_name = os.getenv("FLASK_ENV") or "default"
    app = Flask(__name__)
    Swagger(app)
    openCors(app)  # 开启跨域
    search_blueprint(app)
    # if kwargs.get('celery'):
    #     init_celery(kwargs['celery'], app)        # 初始化celery 并注册celery
    app.config.from_object(config[config_name])  # 定义配置信息

    # # 初始化缓存
    # from utils.cache import cache
    # cache.init_app(app)

    logger = create_log(app.config.get("LEVEL"))

    logger.info(f"当前获取配置文件中的时区为：{app.config['TIMEZONE']}")
    app.config["TIMEZONE"] = pytz.timezone(app.config["TIMEZONE"])

    db.init_app(app)
    logger.info(
        f"FLASK_ENV:{config_name}\nLOG_LEVEL:{app.config.get('LEVEL')}\n数据库表：{app.config.get('HOSTNAME')}---{app.config.get('DATABASE')}"
    )
    if celery:
        init_celery(celery, app)
    return app


def init_celery(celery: Celery, app: Flask) -> None:
    celery.conf.update(app.config)

    class ContextTask(celery.Task):
        def __call__(self, *args, **kwargs):
            with app.app_context():
                return self.run(*args, **kwargs)

    celery.Task = ContextTask


app = create_app()
asgi_app = WSGIMiddleware(app)
