# 变更记录 - IP黑名单管理模块

## 2023-11-20 - 参数过滤和安全增强

### 修改文件
`devopscicd/blueprint/ipblacklist.py`

### 变更内容
1. **参数过滤功能增强**
   - 修改`prepare_params`方法，增加模型字段过滤
   - 从只过滤None值改为同时过滤非模型字段
   - 保留0值的处理逻辑

2. **更新记录方法安全加固**
   - 在`update_record`方法中添加二次参数过滤
   - 确保只更新模型定义的字段
   - 防止前端传递额外字段导致的问题

3. **错误处理优化**
   - 保留原有错误处理机制
   - 确保数据库操作失败时正确回滚

### 影响范围
- 所有通过API操作IP黑名单的接口
- 特别是创建和更新黑名单记录的功能

### 测试建议
1. 测试前端传递额外字段时是否被正确过滤
2. 验证None值和0值的处理是否符合预期
3. 检查更新和新增操作的正常流程
4. 测试错误场景下的回滚机制

### 相关提交
`commit-hash-xxxx` - 参数过滤和安全增强
